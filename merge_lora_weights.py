#!/usr/bin/env python3
"""
LoRA权重合并脚本
将训练好的LoRA权重合并到基础模型中，创建完整的微调模型
"""

import os
import torch
from safetensors.torch import load_file, save_file
import time
from pathlib import Path

def merge_lora_to_base_model(base_model_path, lora_path, output_path, alpha=1.0):
    """
    将LoRA权重合并到基础模型
    
    Args:
        base_model_path: 基础模型路径
        lora_path: LoRA权重路径
        output_path: 输出模型路径
        alpha: LoRA权重缩放因子
    """
    
    print(f"🔄 开始合并LoRA权重...")
    print(f"📥 基础模型: {base_model_path}")
    print(f"📥 LoRA权重: {lora_path}")
    print(f"📤 输出路径: {output_path}")
    print(f"⚖️  Alpha值: {alpha}")
    
    # 加载基础模型
    print("📥 加载基础模型...")
    base_weights = load_file(base_model_path)
    print(f"✅ 基础模型加载完成，包含 {len(base_weights)} 个权重")
    
    # 加载LoRA权重
    print("📥 加载LoRA权重...")
    lora_weights = load_file(lora_path)
    print(f"✅ LoRA权重加载完成，包含 {len(lora_weights)} 个权重")
    
    # 分析LoRA权重结构
    lora_pairs = {}
    for key in lora_weights.keys():
        if '.lora_A.' in key:
            base_key = key.replace('.lora_A.default.weight', '')
            if base_key not in lora_pairs:
                lora_pairs[base_key] = {}
            lora_pairs[base_key]['A'] = key
        elif '.lora_B.' in key:
            base_key = key.replace('.lora_B.default.weight', '')
            if base_key not in lora_pairs:
                lora_pairs[base_key] = {}
            lora_pairs[base_key]['B'] = key
    
    print(f"📊 找到 {len(lora_pairs)} 个LoRA层对")
    
    # 合并权重
    merged_weights = base_weights.copy()
    merged_count = 0
    
    print("🔄 开始权重合并...")
    for base_key, pair in lora_pairs.items():
        if 'A' in pair and 'B' in pair:
            # 构建对应的基础模型权重键名
            # LoRA键名格式: blocks.0.self_attn.q
            # 基础模型键名格式: blocks.0.self_attn.q.weight
            base_weight_key = f"{base_key}.weight"
            
            if base_weight_key in merged_weights:
                # 获取LoRA权重
                lora_A = lora_weights[pair['A']]  # [rank, in_features]
                lora_B = lora_weights[pair['B']]  # [out_features, rank]
                
                # 计算LoRA增量: B @ A
                lora_delta = torch.mm(lora_B, lora_A) * alpha
                
                # 合并到基础权重
                merged_weights[base_weight_key] = merged_weights[base_weight_key] + lora_delta
                merged_count += 1
                
                if merged_count <= 5:  # 显示前几个合并的层
                    print(f"  ✅ 合并层: {base_key}")
                    print(f"     LoRA_A: {lora_A.shape}, LoRA_B: {lora_B.shape}")
                    print(f"     增量: {lora_delta.shape}, 范围: [{lora_delta.min():.6f}, {lora_delta.max():.6f}]")
    
    print(f"✅ 权重合并完成，共合并 {merged_count} 个层")
    
    # 保存合并后的模型
    print("💾 保存合并后的模型...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    save_file(merged_weights, output_path)
    
    # 检查输出文件
    output_size = os.path.getsize(output_path) / (1024**3)  # GB
    print(f"✅ 模型保存成功: {output_path}")
    print(f"📊 文件大小: {output_size:.2f} GB")
    
    return True

def merge_multiple_alphas():
    """合并多个alpha值的模型"""
    
    print("=== Wan2.1-T2V-1.3B LoRA权重合并 ===")
    
    # 配置路径
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    lora_optimized_path = "./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors"
    lora_minimal_path = "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors"
    
    # 检查文件存在
    if not os.path.exists(base_model_path):
        print(f"❌ 基础模型不存在: {base_model_path}")
        return False
    
    # 合并配置
    merge_configs = []
    
    # 优化版LoRA合并
    if os.path.exists(lora_optimized_path):
        merge_configs.extend([
            {
                "name": "optimized",
                "lora_path": lora_optimized_path,
                "alphas": [0.5, 1.0, 1.5]
            }
        ])
    
    # 极限版LoRA合并
    if os.path.exists(lora_minimal_path):
        merge_configs.extend([
            {
                "name": "minimal", 
                "lora_path": lora_minimal_path,
                "alphas": [0.5, 1.0, 1.5]
            }
        ])
    
    if not merge_configs:
        print("❌ 未找到LoRA权重文件")
        return False
    
    # 执行合并
    success_count = 0
    total_count = 0
    
    for config in merge_configs:
        lora_name = config["name"]
        lora_path = config["lora_path"]
        alphas = config["alphas"]
        
        print(f"\n{'='*60}")
        print(f"🧪 合并LoRA: {lora_name}")
        print(f"📄 LoRA路径: {lora_path}")
        
        for alpha in alphas:
            total_count += 1
            output_path = f"./models/merged/Wan2.1-T2V-1.3B_{lora_name}_alpha_{alpha}.safetensors"
            
            print(f"\n🔄 合并 Alpha={alpha}...")
            
            try:
                success = merge_lora_to_base_model(
                    base_model_path=base_model_path,
                    lora_path=lora_path,
                    output_path=output_path,
                    alpha=alpha
                )
                
                if success:
                    success_count += 1
                    print(f"✅ Alpha={alpha} 合并成功")
                else:
                    print(f"❌ Alpha={alpha} 合并失败")
                    
            except Exception as e:
                print(f"❌ Alpha={alpha} 合并失败: {str(e)}")
    
    # 生成合并报告
    print(f"\n{'='*60}")
    print("📋 权重合并报告")
    print(f"📊 合并统计:")
    print(f"  - 总任务数: {total_count}")
    print(f"  - 成功数量: {success_count}")
    print(f"  - 失败数量: {total_count - success_count}")
    print(f"  - 成功率: {success_count/total_count*100:.1f}%")
    
    # 列出生成的模型
    merged_dir = "./models/merged"
    if os.path.exists(merged_dir):
        merged_files = []
        for file in os.listdir(merged_dir):
            if file.endswith('.safetensors'):
                file_path = os.path.join(merged_dir, file)
                file_size = os.path.getsize(file_path) / (1024**3)
                merged_files.append((file, file_size))
        
        if merged_files:
            print(f"\n📁 生成的合并模型:")
            total_size = 0
            for filename, size in merged_files:
                print(f"  - {filename}: {size:.2f} GB")
                total_size += size
            print(f"  - 总大小: {total_size:.2f} GB")
        else:
            print(f"\n⚠️  未找到合并的模型文件")
    
    if success_count > 0:
        print(f"\n✅ 权重合并完成！生成了 {success_count} 个合并模型")
        return True
    else:
        print(f"\n❌ 权重合并失败，未生成任何模型")
        return False

def main():
    """主函数"""
    
    print("🚀 开始LoRA权重合并...")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
        # 清理显存
        torch.cuda.empty_cache()
    else:
        print("⚠️  CUDA不可用，使用CPU进行合并")
    
    # 执行合并
    success = merge_multiple_alphas()
    
    print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("🎉 LoRA权重合并全部完成！")
    else:
        print("❌ LoRA权重合并失败")

if __name__ == "__main__":
    main()
