#!/usr/bin/env python3
"""
TensorBoard管理脚本
用于启动、停止和管理TensorBoard服务
"""

import os
import sys
import subprocess
import signal
import time
import argparse
from pathlib import Path
import webbrowser
import socket

class TensorBoardManager:
    """TensorBoard管理器"""
    
    def __init__(self):
        self.default_port = 6006
        self.default_logdir = "./tensorboard_logs"
        self.pid_file = "./tensorboard.pid"
    
    def find_free_port(self, start_port=6006):
        """查找可用端口"""
        port = start_port
        while port < start_port + 100:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                port += 1
        raise RuntimeError("无法找到可用端口")
    
    def is_tensorboard_running(self):
        """检查TensorBoard是否正在运行"""
        if not os.path.exists(self.pid_file):
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            os.kill(pid, 0)
            return True
        except (OSError, ValueError):
            # 进程不存在或PID文件损坏
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            return False
    
    def start_tensorboard(self, logdir=None, port=None, host="localhost", open_browser=True):
        """启动TensorBoard"""
        
        if self.is_tensorboard_running():
            print("⚠️ TensorBoard已在运行中")
            return False
        
        # 设置默认值
        if logdir is None:
            logdir = self.default_logdir
        if port is None:
            port = self.find_free_port(self.default_port)
        
        # 检查日志目录
        if not os.path.exists(logdir):
            print(f"❌ 日志目录不存在: {logdir}")
            return False
        
        # 检查日志目录是否有内容
        log_files = list(Path(logdir).rglob("events.out.tfevents.*"))
        if not log_files:
            print(f"⚠️ 日志目录中没有TensorBoard事件文件: {logdir}")
            print("💡 请先运行训练脚本生成日志")
        
        print(f"🚀 启动TensorBoard...")
        print(f"📁 日志目录: {logdir}")
        print(f"🌐 地址: http://{host}:{port}")
        
        # 构建命令
        cmd = [
            sys.executable, "-m", "tensorboard.main",
            "--logdir", logdir,
            "--port", str(port),
            "--host", host,
            "--reload_interval", "1",  # 1秒刷新间隔
            "--load_fast", "false"     # 加载所有数据
        ]
        
        try:
            # 启动TensorBoard进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            
            # 保存PID
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            # 等待启动
            time.sleep(3)
            
            # 检查是否启动成功
            if process.poll() is None:
                print("✅ TensorBoard启动成功!")
                print(f"📊 访问地址: http://{host}:{port}")
                
                # 自动打开浏览器
                if open_browser:
                    try:
                        webbrowser.open(f"http://{host}:{port}")
                        print("🌐 已自动打开浏览器")
                    except Exception as e:
                        print(f"⚠️ 无法自动打开浏览器: {str(e)}")
                
                return True
            else:
                # 获取错误信息
                stdout, stderr = process.communicate()
                print(f"❌ TensorBoard启动失败")
                if stderr:
                    print(f"错误信息: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ 启动TensorBoard时出错: {str(e)}")
            return False
    
    def stop_tensorboard(self):
        """停止TensorBoard"""
        
        if not self.is_tensorboard_running():
            print("⚠️ TensorBoard未在运行")
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            print(f"🛑 停止TensorBoard (PID: {pid})...")
            
            # 发送终止信号
            os.killpg(os.getpgid(pid), signal.SIGTERM)
            
            # 等待进程结束
            time.sleep(2)
            
            # 检查是否已停止
            try:
                os.kill(pid, 0)
                # 如果进程仍在运行，强制终止
                print("⚠️ 进程未响应，强制终止...")
                os.killpg(os.getpgid(pid), signal.SIGKILL)
                time.sleep(1)
            except OSError:
                pass  # 进程已停止
            
            # 删除PID文件
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            
            print("✅ TensorBoard已停止")
            return True
            
        except Exception as e:
            print(f"❌ 停止TensorBoard时出错: {str(e)}")
            return False
    
    def restart_tensorboard(self, logdir=None, port=None, host="localhost", open_browser=True):
        """重启TensorBoard"""
        print("🔄 重启TensorBoard...")
        self.stop_tensorboard()
        time.sleep(1)
        return self.start_tensorboard(logdir, port, host, open_browser)
    
    def status(self):
        """检查TensorBoard状态"""
        if self.is_tensorboard_running():
            try:
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                print(f"✅ TensorBoard正在运行 (PID: {pid})")
                return True
            except:
                print("⚠️ TensorBoard状态异常")
                return False
        else:
            print("❌ TensorBoard未运行")
            return False
    
    def list_logs(self, logdir=None):
        """列出可用的日志目录"""
        if logdir is None:
            logdir = self.default_logdir
        
        print(f"📁 扫描日志目录: {logdir}")
        
        if not os.path.exists(logdir):
            print("❌ 日志目录不存在")
            return
        
        # 查找所有包含TensorBoard事件文件的目录
        log_dirs = []
        for root, dirs, files in os.walk(logdir):
            for file in files:
                if file.startswith("events.out.tfevents."):
                    log_dirs.append(root)
                    break
        
        if not log_dirs:
            print("❌ 未找到TensorBoard日志文件")
            return
        
        print(f"📊 找到 {len(log_dirs)} 个日志目录:")
        for i, log_dir in enumerate(sorted(log_dirs), 1):
            rel_path = os.path.relpath(log_dir, logdir)
            # 获取最新文件时间
            latest_time = 0
            for file in os.listdir(log_dir):
                if file.startswith("events.out.tfevents."):
                    file_path = os.path.join(log_dir, file)
                    latest_time = max(latest_time, os.path.getmtime(file_path))
            
            if latest_time > 0:
                from datetime import datetime
                time_str = datetime.fromtimestamp(latest_time).strftime("%Y-%m-%d %H:%M:%S")
                print(f"  {i}. {rel_path} (最后更新: {time_str})")
            else:
                print(f"  {i}. {rel_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TensorBoard管理工具")
    parser.add_argument("command", choices=["start", "stop", "restart", "status", "list"],
                       help="要执行的命令")
    parser.add_argument("--logdir", type=str, default=None,
                       help="TensorBoard日志目录")
    parser.add_argument("--port", type=int, default=None,
                       help="TensorBoard端口")
    parser.add_argument("--host", type=str, default="localhost",
                       help="TensorBoard主机地址")
    parser.add_argument("--no-browser", action="store_true",
                       help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    manager = TensorBoardManager()
    
    if args.command == "start":
        manager.start_tensorboard(
            logdir=args.logdir,
            port=args.port,
            host=args.host,
            open_browser=not args.no_browser
        )
    elif args.command == "stop":
        manager.stop_tensorboard()
    elif args.command == "restart":
        manager.restart_tensorboard(
            logdir=args.logdir,
            port=args.port,
            host=args.host,
            open_browser=not args.no_browser
        )
    elif args.command == "status":
        manager.status()
    elif args.command == "list":
        manager.list_logs(args.logdir)

if __name__ == "__main__":
    main()
