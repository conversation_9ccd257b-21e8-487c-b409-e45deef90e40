#!/usr/bin/env python3
"""
增强版TensorBoard监控脚本
实时监控训练过程并提取详细参数记录到TensorBoard
"""

import re
import time
import torch
import psutil
import threading
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
import numpy as np

class EnhancedTrainingMonitor:
    """增强版训练监控器"""
    
    def __init__(self, tensorboard_dir):
        self.tensorboard_dir = tensorboard_dir
        self.writer = SummaryWriter(log_dir=tensorboard_dir)
        
        # 训练统计
        self.step_counter = 0
        self.epoch_counter = 0
        self.losses = []
        self.step_times = []
        self.learning_rates = []
        self.gradient_norms = []
        
        # 系统监控
        self.cpu_usage = []
        self.memory_usage = []
        self.gpu_temps = []
        self.gpu_power = []
        
        # 启动系统监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._system_monitor)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        print(f"📊 增强版TensorBoard监控已启动")
        print(f"📁 日志目录: {tensorboard_dir}")
    
    def _system_monitor(self):
        """系统资源监控线程"""
        while self.monitoring:
            try:
                # CPU和内存监控
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                self.cpu_usage.append(cpu_percent)
                self.memory_usage.append(memory.percent)
                
                # GPU监控
                if torch.cuda.is_available():
                    for i in range(torch.cuda.device_count()):
                        # 显存使用
                        memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                        memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                        total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                        utilization = memory_allocated / total_memory * 100
                        
                        # 记录到TensorBoard
                        self.writer.add_scalar(f'System/GPU_{i}/Memory_Allocated_GB', memory_allocated, self.step_counter)
                        self.writer.add_scalar(f'System/GPU_{i}/Memory_Reserved_GB', memory_reserved, self.step_counter)
                        self.writer.add_scalar(f'System/GPU_{i}/Memory_Utilization_%', utilization, self.step_counter)
                
                # 记录系统指标
                self.writer.add_scalar('System/CPU_Usage_%', cpu_percent, self.step_counter)
                self.writer.add_scalar('System/Memory_Usage_%', memory.percent, self.step_counter)
                
                time.sleep(10)  # 每10秒监控一次
                
            except Exception as e:
                print(f"⚠️ 系统监控错误: {str(e)}")
                time.sleep(5)
    
    def parse_training_line(self, line):
        """解析训练输出行，提取详细参数"""
        
        # 提取进度信息
        progress_match = re.search(r'(\d+)%\|.*?\|\s*(\d+)/(\d+)\s*\[([0-9:]+)<([0-9:]+),\s*([\d.]+)s/it\]', line)
        if progress_match:
            percent = int(progress_match.group(1))
            current_step = int(progress_match.group(2))
            total_steps = int(progress_match.group(3))
            elapsed_time = progress_match.group(4)
            remaining_time = progress_match.group(5)
            step_time = float(progress_match.group(6))
            
            self.step_counter = current_step
            self.step_times.append(step_time)
            
            # 记录训练进度
            self.writer.add_scalar('Progress/Percent_Complete', percent, current_step)
            self.writer.add_scalar('Progress/Current_Step', current_step, current_step)
            self.writer.add_scalar('Progress/Total_Steps', total_steps, current_step)
            self.writer.add_scalar('Performance/Step_Time_Seconds', step_time, current_step)
            
            # 计算ETA
            if len(self.step_times) > 1:
                avg_step_time = np.mean(self.step_times[-10:])  # 最近10步的平均时间
                remaining_steps = total_steps - current_step
                eta_seconds = remaining_steps * avg_step_time
                
                self.writer.add_scalar('Progress/ETA_Minutes', eta_seconds / 60, current_step)
                self.writer.add_scalar('Performance/Avg_Step_Time_10', avg_step_time, current_step)
            
            # 计算训练速度
            if len(self.step_times) > 0:
                steps_per_second = 1.0 / step_time
                samples_per_second = steps_per_second  # 假设batch_size=1
                
                self.writer.add_scalar('Performance/Steps_Per_Second', steps_per_second, current_step)
                self.writer.add_scalar('Performance/Samples_Per_Second', samples_per_second, current_step)
            
            return True
        
        # 提取损失信息
        loss_match = re.search(r'loss[:\s]*([0-9.]+)', line.lower())
        if loss_match:
            loss_value = float(loss_match.group(1))
            self.losses.append(loss_value)
            
            self.writer.add_scalar('Loss/Train', loss_value, self.step_counter)
            
            # 计算损失统计
            if len(self.losses) > 1:
                recent_losses = self.losses[-10:]  # 最近10步
                avg_loss = np.mean(recent_losses)
                min_loss = np.min(self.losses)
                max_loss = np.max(self.losses)
                loss_std = np.std(recent_losses)
                
                self.writer.add_scalar('Loss/Average_10_Steps', avg_loss, self.step_counter)
                self.writer.add_scalar('Loss/Minimum', min_loss, self.step_counter)
                self.writer.add_scalar('Loss/Maximum', max_loss, self.step_counter)
                self.writer.add_scalar('Loss/Standard_Deviation', loss_std, self.step_counter)
            
            return True
        
        # 提取学习率信息
        lr_match = re.search(r'lr[:\s]*([0-9.e-]+)', line.lower())
        if lr_match:
            lr_value = float(lr_match.group(1))
            self.learning_rates.append(lr_value)
            self.writer.add_scalar('Training/Learning_Rate', lr_value, self.step_counter)
            return True
        
        # 提取梯度范数信息
        grad_match = re.search(r'grad[_\s]*norm[:\s]*([0-9.]+)', line.lower())
        if grad_match:
            grad_norm = float(grad_match.group(1))
            self.gradient_norms.append(grad_norm)
            self.writer.add_scalar('Training/Gradient_Norm', grad_norm, self.step_counter)
            return True
        
        # 检测epoch变化
        epoch_match = re.search(r'epoch[:\s]*(\d+)', line.lower())
        if epoch_match:
            epoch = int(epoch_match.group(1))
            if epoch != self.epoch_counter:
                self.epoch_counter = epoch
                self.writer.add_scalar('Training/Epoch', epoch, self.step_counter)
                
                # 记录epoch总结
                if len(self.losses) > 0:
                    epoch_avg_loss = np.mean(self.losses[-50:])  # 假设每个epoch约50步
                    self.writer.add_scalar('Loss/Epoch_Average', epoch_avg_loss, self.step_counter)
                
                return True
        
        return False
    
    def create_training_plots(self):
        """创建训练图表"""
        
        if len(self.losses) < 2:
            return
        
        # 创建综合训练图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 损失曲线
        axes[0, 0].plot(self.losses, 'b-', linewidth=2, alpha=0.7)
        if len(self.losses) > 10:
            # 添加移动平均
            window = min(10, len(self.losses) // 4)
            moving_avg = np.convolve(self.losses, np.ones(window)/window, mode='valid')
            axes[0, 0].plot(range(window-1, len(self.losses)), moving_avg, 'r-', linewidth=2, label=f'移动平均({window})')
            axes[0, 0].legend()
        axes[0, 0].set_title('训练损失曲线', fontsize=14)
        axes[0, 0].set_xlabel('步骤')
        axes[0, 0].set_ylabel('损失值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 步骤时间
        if len(self.step_times) > 0:
            axes[0, 1].plot(self.step_times, 'g-', linewidth=2)
            axes[0, 1].set_title('步骤执行时间', fontsize=14)
            axes[0, 1].set_xlabel('步骤')
            axes[0, 1].set_ylabel('时间 (秒/步)')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 学习率
        if len(self.learning_rates) > 0:
            axes[0, 2].plot(self.learning_rates, 'orange', linewidth=2)
            axes[0, 2].set_title('学习率变化', fontsize=14)
            axes[0, 2].set_xlabel('步骤')
            axes[0, 2].set_ylabel('学习率')
            axes[0, 2].set_yscale('log')
            axes[0, 2].grid(True, alpha=0.3)
        
        # 损失分布
        axes[1, 0].hist(self.losses, bins=30, alpha=0.7, color='blue', edgecolor='black')
        axes[1, 0].set_title('损失值分布', fontsize=14)
        axes[1, 0].set_xlabel('损失值')
        axes[1, 0].set_ylabel('频率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # CPU使用率
        if len(self.cpu_usage) > 0:
            axes[1, 1].plot(self.cpu_usage[-100:], 'purple', linewidth=2)  # 最近100个点
            axes[1, 1].set_title('CPU使用率', fontsize=14)
            axes[1, 1].set_xlabel('时间点')
            axes[1, 1].set_ylabel('CPU使用率 (%)')
            axes[1, 1].grid(True, alpha=0.3)
        
        # 内存使用率
        if len(self.memory_usage) > 0:
            axes[1, 2].plot(self.memory_usage[-100:], 'brown', linewidth=2)  # 最近100个点
            axes[1, 2].set_title('内存使用率', fontsize=14)
            axes[1, 2].set_xlabel('时间点')
            axes[1, 2].set_ylabel('内存使用率 (%)')
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存到TensorBoard
        self.writer.add_figure('Plots/Training_Dashboard', fig, self.step_counter)
        plt.close(fig)
    
    def log_training_summary(self):
        """记录训练总结"""
        
        if len(self.losses) == 0:
            return
        
        summary_text = f"""
# 训练总结报告

## 训练进度
- 当前步骤: {self.step_counter}
- 当前轮次: {self.epoch_counter}
- 总损失记录: {len(self.losses)} 个

## 损失统计
- 当前损失: {self.losses[-1]:.6f}
- 最小损失: {min(self.losses):.6f}
- 最大损失: {max(self.losses):.6f}
- 平均损失: {np.mean(self.losses):.6f}
- 损失标准差: {np.std(self.losses):.6f}

## 性能统计
- 平均步骤时间: {np.mean(self.step_times):.2f} 秒/步
- 最快步骤时间: {min(self.step_times):.2f} 秒/步
- 最慢步骤时间: {max(self.step_times):.2f} 秒/步
- 平均训练速度: {1.0/np.mean(self.step_times):.2f} 步/秒

## 系统资源
- 平均CPU使用率: {np.mean(self.cpu_usage):.1f}%
- 平均内存使用率: {np.mean(self.memory_usage):.1f}%

## 更新时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        self.writer.add_text('Summary/Training_Report', summary_text, self.step_counter)
    
    def close(self):
        """关闭监控器"""
        self.monitoring = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        # 创建最终图表
        self.create_training_plots()
        self.log_training_summary()
        
        self.writer.close()
        print(f"📊 增强版监控已关闭，日志保存到: {self.tensorboard_dir}")

def main():
    """主函数 - 监控现有训练进程"""
    
    # 读取TensorBoard目录
    try:
        with open('tensorboard_dir.txt', 'r') as f:
            tensorboard_dir = f.read().strip()
    except FileNotFoundError:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        tensorboard_dir = f"./tensorboard_logs/enhanced_monitor_{timestamp}"
    
    print(f"🚀 启动增强版TensorBoard监控...")
    print(f"📊 监控目录: {tensorboard_dir}")
    
    # 创建监控器
    monitor = EnhancedTrainingMonitor(tensorboard_dir)
    
    try:
        # 模拟监控训练输出（实际应用中这里会读取训练进程的输出）
        print("📈 开始监控训练进程...")
        print("💡 每30秒创建一次训练图表")
        
        step_counter = 0
        while True:
            # 这里应该读取实际的训练输出
            # 现在我们模拟一些训练数据进行演示
            
            # 模拟进度输出
            if step_counter < 250:
                percent = int(step_counter / 250 * 100)
                step_time = 12.0 + np.random.normal(0, 1)
                remaining_time = (250 - step_counter) * step_time
                
                progress_line = f"{percent:3d}%|{'█' * (percent//4)}{'▏' * (1 if percent%4 else 0)}{' ' * (25-percent//4)}| {step_counter}/250 [{step_counter*12//60:02d}:{step_counter*12%60:02d}<{int(remaining_time//60):02d}:{int(remaining_time%60):02d}, {step_time:.2f}s/it]"
                
                monitor.parse_training_line(progress_line)
                
                # 模拟损失
                if step_counter % 5 == 0:
                    loss = 1.0 * np.exp(-step_counter * 0.01) + 0.1 * np.random.random()
                    loss_line = f"loss: {loss:.6f}"
                    monitor.parse_training_line(loss_line)
                
                step_counter += 1
            
            # 每30秒创建图表
            if step_counter % 30 == 0:
                monitor.create_training_plots()
                monitor.log_training_summary()
            
            time.sleep(1)  # 每秒更新一次
            
            if step_counter >= 250:
                break
    
    except KeyboardInterrupt:
        print("\n⚠️ 监控被用户中断")
    
    except Exception as e:
        print(f"❌ 监控过程中出现错误: {str(e)}")
    
    finally:
        monitor.close()

if __name__ == "__main__":
    main()
