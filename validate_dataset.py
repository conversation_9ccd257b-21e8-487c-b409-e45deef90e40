#!/usr/bin/env python3
"""
验证训练数据集完整性
"""

import pandas as pd
import os
import cv2
from pathlib import Path

def validate_dataset(dataset_path):
    """验证数据集完整性"""
    
    print(f"=== 验证数据集: {dataset_path} ===")
    
    metadata_path = os.path.join(dataset_path, "metadata.csv")
    
    if not os.path.exists(metadata_path):
        print(f"❌ metadata.csv 不存在: {metadata_path}")
        return False
    
    # 读取元数据
    try:
        df = pd.read_csv(metadata_path)
        print(f"✅ 成功读取metadata.csv")
        print(f"📊 数据集包含 {len(df)} 个样本")
        
        # 检查列名
        expected_columns = ['video', 'prompt']  # 注意这里是'prompt'不是'text'
        if 'text' in df.columns and 'prompt' not in df.columns:
            expected_columns = ['video', 'text']
        
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ 缺少必要列: {missing_columns}")
            print(f"📋 实际列名: {list(df.columns)}")
            return False
        
        print(f"✅ 列名验证通过: {list(df.columns)}")
        
    except Exception as e:
        print(f"❌ 读取metadata.csv失败: {str(e)}")
        return False
    
    # 显示前几个样本
    print(f"\n📋 前{min(5, len(df))}个样本:")
    for i, row in df.head().iterrows():
        video_col = 'video'
        text_col = 'prompt' if 'prompt' in df.columns else 'text'
        print(f"  {i+1}. 视频: {row[video_col]}")
        print(f"     文本: {row[text_col]}")
    
    # 验证视频文件存在
    print(f"\n🔍 验证视频文件:")
    missing_files = []
    valid_files = []
    
    for idx, row in df.iterrows():
        video_file = row['video']
        video_path = os.path.join(dataset_path, video_file)
        
        if os.path.exists(video_path):
            # 检查视频文件信息
            try:
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    duration = frame_count / fps if fps > 0 else 0
                    
                    file_size = os.path.getsize(video_path) / (1024*1024)  # MB
                    
                    print(f"  ✅ {video_file}")
                    print(f"     尺寸: {width}x{height}, 帧数: {frame_count}, FPS: {fps:.1f}")
                    print(f"     时长: {duration:.1f}s, 大小: {file_size:.1f}MB")
                    
                    valid_files.append({
                        'file': video_file,
                        'width': width,
                        'height': height,
                        'frames': frame_count,
                        'fps': fps,
                        'duration': duration,
                        'size_mb': file_size
                    })
                    
                    cap.release()
                else:
                    print(f"  ❌ {video_file}: 无法打开视频文件")
                    missing_files.append(video_file)
            except Exception as e:
                print(f"  ❌ {video_file}: 读取视频信息失败 - {str(e)}")
                missing_files.append(video_file)
        else:
            print(f"  ❌ {video_file}: 文件不存在")
            missing_files.append(video_file)
    
    # 统计信息
    print(f"\n📊 数据集统计:")
    print(f"  - 总样本数: {len(df)}")
    print(f"  - 有效视频: {len(valid_files)}")
    print(f"  - 缺失视频: {len(missing_files)}")
    
    if valid_files:
        avg_duration = sum(v['duration'] for v in valid_files) / len(valid_files)
        total_size = sum(v['size_mb'] for v in valid_files)
        print(f"  - 平均时长: {avg_duration:.1f}秒")
        print(f"  - 总大小: {total_size:.1f}MB")
        
        # 分辨率统计
        resolutions = [(v['width'], v['height']) for v in valid_files]
        unique_resolutions = list(set(resolutions))
        print(f"  - 分辨率: {unique_resolutions}")
    
    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 个视频文件:")
        for file in missing_files:
            print(f"    - {file}")
        return False
    else:
        print(f"\n✅ 数据集验证通过！所有 {len(df)} 个样本都有效")
        return True

def main():
    """主函数"""
    
    print("=== 数据集验证工具 ===")
    
    # 验证官方示例数据集
    official_dataset = "./data/example_video_dataset"
    if os.path.exists(official_dataset):
        print(f"\n{'='*60}")
        success1 = validate_dataset(official_dataset)
    else:
        print(f"⚠️  官方示例数据集不存在: {official_dataset}")
        success1 = False
    
    # 验证自定义数据集（如果存在）
    custom_dataset = "./data/custom_video_dataset"
    success2 = True
    if os.path.exists(custom_dataset):
        print(f"\n{'='*60}")
        success2 = validate_dataset(custom_dataset)
    else:
        print(f"\n⚠️  自定义数据集不存在: {custom_dataset}")
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 验证总结:")
    if success1:
        print("✅ 官方示例数据集验证通过")
    else:
        print("❌ 官方示例数据集验证失败")
    
    if os.path.exists(custom_dataset):
        if success2:
            print("✅ 自定义数据集验证通过")
        else:
            print("❌ 自定义数据集验证失败")
    
    if success1 or success2:
        print("\n🎉 至少有一个数据集可用，可以开始训练！")
    else:
        print("\n❌ 没有可用的数据集，请检查数据准备")

if __name__ == "__main__":
    main()
