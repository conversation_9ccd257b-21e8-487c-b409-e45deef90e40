#!/usr/bin/env python3
"""
验证Wan2.1-T2V-1.3B模型文件完整性
"""

import os
from pathlib import Path

def validate_model_files():
    """验证模型文件完整性"""
    
    model_path = Path('./models/Wan-AI/Wan2.1-T2V-1.3B')
    required_files = [
        'diffusion_pytorch_model.safetensors',
        'models_t5_umt5-xxl-enc-bf16.pth',
        'Wan2.1_VAE.pth',
        'config.json'
    ]
    
    print('=== Wan2.1-T2V-1.3B 模型文件验证 ===')
    print(f'模型路径: {model_path}')
    
    if not model_path.exists():
        print(f'❌ 模型路径不存在: {model_path}')
        return False
    
    total_size = 0
    missing_files = []
    
    for filename in required_files:
        file_path = model_path / filename
        if file_path.exists():
            size_bytes = file_path.stat().st_size
            size_gb = size_bytes / (1024**3)
            total_size += size_bytes
            print(f'✅ {filename}: {size_gb:.2f} GB')
        else:
            missing_files.append(filename)
            print(f'❌ {filename}: 缺失')
    
    if missing_files:
        print(f'\n❌ 缺少关键文件: {missing_files}')
        return False
    
    total_size_gb = total_size / (1024**3)
    print(f'\n📊 模型统计:')
    print(f'  - 核心文件总大小: {total_size_gb:.2f} GB')
    
    # 检查完整目录大小
    all_files_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
    all_files_size_gb = all_files_size / (1024**3)
    print(f'  - 完整目录大小: {all_files_size_gb:.2f} GB')
    
    # 验证文件大小是否合理
    expected_sizes = {
        'diffusion_pytorch_model.safetensors': (5.0, 6.0),  # 5-6 GB
        'models_t5_umt5-xxl-enc-bf16.pth': (10.0, 12.0),    # 10-12 GB  
        'Wan2.1_VAE.pth': (0.4, 0.6),                       # 0.4-0.6 GB
        'config.json': (0.0001, 0.001)                      # < 1 MB
    }
    
    print(f'\n🔍 文件大小验证:')
    all_sizes_valid = True
    
    for filename, (min_gb, max_gb) in expected_sizes.items():
        file_path = model_path / filename
        if file_path.exists():
            size_gb = file_path.stat().st_size / (1024**3)
            if min_gb <= size_gb <= max_gb:
                print(f'  ✅ {filename}: {size_gb:.2f} GB (正常)')
            else:
                print(f'  ⚠️  {filename}: {size_gb:.2f} GB (异常，期望 {min_gb}-{max_gb} GB)')
                all_sizes_valid = False
    
    if all_sizes_valid:
        print(f'\n✅ 所有文件大小验证通过')
    else:
        print(f'\n⚠️  部分文件大小异常，但可能仍可使用')
    
    print(f'\n✅ 模型文件验证完成！')
    return True

if __name__ == "__main__":
    validate_model_files()
