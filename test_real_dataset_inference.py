#!/usr/bin/env python3
"""
真实动画数据集LoRA推理测试脚本
测试使用真实动画数据集训练的LoRA模型的视频生成效果
"""

import torch
import os
import json
import time
from datetime import datetime
from pathlib import Path
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

def test_real_dataset_lora():
    """测试真实数据集训练的LoRA模型"""
    
    print("🎬 开始真实动画数据集LoRA推理测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查CUDA可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行推理测试")
        return False
    
    print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)}")
    
    try:
        # 1. 加载基础模型
        print("\n📥 步骤1: 加载基础模型...")
        start_time = time.time()
        
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda"
        )
        
        load_time = time.time() - start_time
        print(f"✅ 基础模型加载完成，耗时: {load_time:.1f}秒")
        
        # 2. 加载真实数据集训练的LoRA权重
        print("\n🎯 步骤2: 加载真实数据集LoRA权重...")
        lora_path = "./models/train/Wan2.1-T2V-1.3B_real_dataset_lora"
        
        if not os.path.exists(lora_path):
            print(f"❌ LoRA权重路径不存在: {lora_path}")
            print("💡 请先运行训练脚本: ./train_with_real_dataset.sh")
            return False
        
        # 检查LoRA文件
        lora_files = []
        for file in os.listdir(lora_path):
            if file.endswith('.safetensors') or file.endswith('.bin'):
                lora_files.append(file)
                print(f"📁 找到LoRA文件: {file}")
        
        if not lora_files:
            print("❌ 未找到LoRA权重文件")
            return False
        
        # 加载LoRA权重
        pipe.load_lora_weights(lora_path)
        print("✅ 真实数据集LoRA权重加载完成")
        
        # 3. 读取原始数据集信息
        dataset_path = Path("data/enhanced_t2v_dataset")
        if dataset_path.exists():
            # 读取数据集统计信息
            stats_file = dataset_path / "dataset_stats.json"
            if stats_file.exists():
                with open(stats_file, 'r') as f:
                    dataset_stats = json.load(f)
                print(f"\n📊 使用的训练数据集信息:")
                print(f"   总样本数: {dataset_stats['total_samples']}")
                print(f"   独特场景: {dataset_stats['unique_scenes']}")
                print(f"   视频规格: {dataset_stats['video_specs']['resolution']}, {dataset_stats['video_specs']['fps']}fps")
            
            # 读取完整元数据
            metadata_file = dataset_path / "metadata_full.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    full_metadata = json.load(f)
                
                # 提取不同类型的提示词进行测试
                test_prompts_by_category = {}
                for record in full_metadata:
                    category = record['category']
                    if category not in test_prompts_by_category:
                        test_prompts_by_category[category] = []
                    if len(test_prompts_by_category[category]) < 2:  # 每个类别最多2个
                        test_prompts_by_category[category].append(record['prompt'])
        
        # 4. 准备测试提示词
        if 'test_prompts_by_category' in locals():
            # 使用训练数据集中的提示词
            test_prompts = []
            for category, prompts in test_prompts_by_category.items():
                test_prompts.extend(prompts)
            print(f"\n📝 使用训练数据集中的提示词进行测试 ({len(test_prompts)}个)")
        else:
            # 使用默认测试提示词
            test_prompts = [
                "Beautiful sunset over calm ocean waves, golden hour lighting, peaceful seascape with warm colors",
                "Misty forest in early morning with sunlight filtering through trees, peaceful woodland scene",
                "Modern city skyline at night with illuminated windows and urban atmosphere, cinematic view",
                "Snow-capped mountains at dawn with pink sky and morning mist, majestic landscape",
                "Desert landscape with golden sand dunes and wind-blown particles, dramatic atmosphere"
            ]
            print(f"\n📝 使用默认测试提示词 ({len(test_prompts)}个)")
        
        # 5. 生成测试视频
        print(f"\n🎬 步骤3: 生成测试视频")
        
        results = []
        for i, prompt in enumerate(test_prompts):
            print(f"\n📝 测试 {i+1}/{len(test_prompts)}: {prompt[:50]}...")
            
            try:
                # 生成视频
                start_time = time.time()
                
                video = pipe(
                    prompt=prompt,
                    height=320,
                    width=576,
                    num_frames=16,
                    num_inference_steps=50,
                    guidance_scale=7.5
                )
                
                generation_time = time.time() - start_time
                
                # 保存视频
                output_filename = f"real_dataset_test_{i+1:02d}.mp4"
                video.save(output_filename)
                
                # 检查文件大小
                file_size = os.path.getsize(output_filename) / (1024 * 1024)  # MB
                
                result = {
                    'prompt': prompt,
                    'filename': output_filename,
                    'generation_time': generation_time,
                    'file_size_mb': file_size,
                    'success': True
                }
                
                results.append(result)
                
                print(f"✅ 生成成功: {output_filename}")
                print(f"⏱️ 生成时间: {generation_time:.1f}秒")
                print(f"📁 文件大小: {file_size:.2f}MB")
                
            except Exception as e:
                print(f"❌ 生成失败: {str(e)}")
                results.append({
                    'prompt': prompt,
                    'filename': None,
                    'generation_time': 0,
                    'file_size_mb': 0,
                    'success': False,
                    'error': str(e)
                })
        
        # 6. 生成对比测试（基础模型 vs LoRA模型）
        print(f"\n🔄 步骤4: 生成对比测试...")
        
        # 卸载LoRA权重，使用基础模型生成
        pipe.unload_lora_weights()
        print("📤 已卸载LoRA权重，使用基础模型生成对比视频")
        
        comparison_prompt = test_prompts[0]  # 使用第一个提示词
        print(f"📝 对比提示词: {comparison_prompt[:50]}...")
        
        try:
            start_time = time.time()
            base_video = pipe(
                prompt=comparison_prompt,
                height=320,
                width=576,
                num_frames=16,
                num_inference_steps=50,
                guidance_scale=7.5
            )
            base_generation_time = time.time() - start_time
            
            base_filename = "base_model_comparison.mp4"
            base_video.save(base_filename)
            base_file_size = os.path.getsize(base_filename) / (1024 * 1024)
            
            print(f"✅ 基础模型对比视频: {base_filename}")
            print(f"⏱️ 生成时间: {base_generation_time:.1f}秒")
            
        except Exception as e:
            print(f"❌ 基础模型对比生成失败: {str(e)}")
            base_filename = None
            base_generation_time = 0
            base_file_size = 0
        
        # 7. 生成详细测试报告
        print("\n📊 步骤5: 生成测试报告...")
        
        successful_tests = sum(1 for r in results if r['success'])
        total_tests = len(results)
        success_rate = successful_tests / total_tests * 100
        
        avg_generation_time = sum(r['generation_time'] for r in results if r['success']) / max(successful_tests, 1)
        total_file_size = sum(r['file_size_mb'] for r in results if r['success'])
        
        # 创建详细报告
        report = f"""
# 真实动画数据集LoRA推理测试报告

## 测试概览
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- LoRA模型: Wan2.1-T2V-1.3B_real_dataset_lora
- 总测试数: {total_tests}
- 成功数量: {successful_tests}
- 成功率: {success_rate:.1f}%

## 性能统计
- 平均生成时间: {avg_generation_time:.1f}秒
- 总文件大小: {total_file_size:.2f}MB
- 平均文件大小: {total_file_size/max(successful_tests, 1):.2f}MB

## 训练数据集信息
"""
        
        if 'dataset_stats' in locals():
            report += f"""
- 训练样本数: {dataset_stats['total_samples']}
- 独特场景: {dataset_stats['unique_scenes']}
- 视频规格: {dataset_stats['video_specs']['resolution']}, {dataset_stats['video_specs']['fps']}fps
- 场景分类: {', '.join(dataset_stats['categories'].keys())}
"""
        
        report += f"""
## 对比测试结果
- LoRA模型生成时间: {results[0]['generation_time']:.1f}秒 (如果成功)
- 基础模型生成时间: {base_generation_time:.1f}秒
- 对比视频: {base_filename if base_filename else 'N/A'}

## 详细结果
"""
        
        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            report += f"""
### 测试 {i}: {status}
- **提示词**: {result['prompt']}
- **输出文件**: {result.get('filename', 'N/A')}
- **生成时间**: {result['generation_time']:.1f}秒
- **文件大小**: {result['file_size_mb']:.2f}MB
"""
            if not result['success']:
                report += f"- **错误信息**: {result.get('error', 'Unknown error')}\n"
        
        report += f"""
## 系统信息
- GPU: {torch.cuda.get_device_name(0)}
- CUDA版本: {torch.version.cuda}
- PyTorch版本: {torch.__version__}
- 显存使用: {torch.cuda.memory_allocated(0) / (1024**3):.2f}GB

## 输出文件位置
"""
        
        for result in results:
            if result['success']:
                report += f"- {result['filename']}\n"
        
        if base_filename:
            report += f"- {base_filename} (基础模型对比)\n"
        
        # 保存报告
        report_filename = f"real_dataset_lora_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📊 测试报告已保存: {report_filename}")
        
        # 打印总结
        print(f"\n🎉 真实动画数据集LoRA推理测试完成!")
        print(f"✅ 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ 平均生成时间: {avg_generation_time:.1f}秒")
        print(f"📁 输出文件: {[r['filename'] for r in results if r['success']]}")
        
        if base_filename:
            print(f"🔄 对比文件: {base_filename}")
        
        return success_rate > 0
        
    except Exception as e:
        print(f"❌ 推理测试过程中出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    success = test_real_dataset_lora()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
