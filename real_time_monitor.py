#!/usr/bin/env python3
"""
实时监控当前训练进程的详细参数
"""

import re
import time
import psutil
import subprocess
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
import torch

def find_training_process():
    """查找正在运行的训练进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and any('train.py' in arg for arg in proc.info['cmdline']):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def extract_detailed_metrics(line):
    """从训练输出中提取详细指标"""
    metrics = {}
    
    # 提取进度信息
    progress_match = re.search(r'(\d+)%\|.*?\|\s*(\d+)/(\d+)\s*\[([0-9:]+)<([0-9:]+),\s*([\d.]+)s/it\]', line)
    if progress_match:
        metrics.update({
            'progress_percent': int(progress_match.group(1)),
            'current_step': int(progress_match.group(2)),
            'total_steps': int(progress_match.group(3)),
            'elapsed_time': progress_match.group(4),
            'remaining_time': progress_match.group(5),
            'step_time': float(progress_match.group(6))
        })
    
    # 提取损失信息
    loss_patterns = [
        r'loss[:\s]*([0-9.]+)',
        r'train_loss[:\s]*([0-9.]+)',
        r'training_loss[:\s]*([0-9.]+)'
    ]
    
    for pattern in loss_patterns:
        loss_match = re.search(pattern, line.lower())
        if loss_match:
            metrics['loss'] = float(loss_match.group(1))
            break
    
    # 提取学习率
    lr_patterns = [
        r'lr[:\s]*([0-9.e-]+)',
        r'learning_rate[:\s]*([0-9.e-]+)'
    ]
    
    for pattern in lr_patterns:
        lr_match = re.search(pattern, line.lower())
        if lr_match:
            metrics['learning_rate'] = float(lr_match.group(1))
            break
    
    # 提取梯度范数
    grad_patterns = [
        r'grad[_\s]*norm[:\s]*([0-9.]+)',
        r'gradient[_\s]*norm[:\s]*([0-9.]+)'
    ]
    
    for pattern in grad_patterns:
        grad_match = re.search(pattern, line.lower())
        if grad_match:
            metrics['gradient_norm'] = float(grad_match.group(1))
            break
    
    # 提取epoch信息
    epoch_match = re.search(r'epoch[:\s]*(\d+)', line.lower())
    if epoch_match:
        metrics['epoch'] = int(epoch_match.group(1))
    
    return metrics

def monitor_system_resources():
    """监控系统资源使用情况"""
    resources = {}
    
    # CPU和内存
    resources['cpu_percent'] = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    resources['memory_percent'] = memory.percent
    resources['memory_used_gb'] = memory.used / (1024**3)
    resources['memory_total_gb'] = memory.total / (1024**3)
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_info = {}
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
            total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            
            gpu_info[f'gpu_{i}'] = {
                'memory_allocated_gb': memory_allocated,
                'memory_reserved_gb': memory_reserved,
                'memory_total_gb': total_memory,
                'memory_utilization_percent': memory_allocated / total_memory * 100
            }
        
        resources['gpu'] = gpu_info
    
    return resources

def main():
    """主监控函数"""
    
    # 创建TensorBoard writer
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tensorboard_dir = f"./tensorboard_logs/real_time_monitor_{timestamp}"
    writer = SummaryWriter(log_dir=tensorboard_dir)
    
    print(f"🚀 启动实时训练监控...")
    print(f"📊 TensorBoard日志: {tensorboard_dir}")
    print(f"💡 访问: tensorboard --logdir {tensorboard_dir}")
    
    # 查找训练进程
    training_pid = find_training_process()
    if not training_pid:
        print("❌ 未找到正在运行的训练进程")
        return
    
    print(f"✅ 找到训练进程 PID: {training_pid}")
    
    step_counter = 0
    last_metrics = {}
    
    try:
        while True:
            # 监控系统资源
            resources = monitor_system_resources()
            
            # 记录系统资源到TensorBoard
            writer.add_scalar('System/CPU_Usage_%', resources['cpu_percent'], step_counter)
            writer.add_scalar('System/Memory_Usage_%', resources['memory_percent'], step_counter)
            writer.add_scalar('System/Memory_Used_GB', resources['memory_used_gb'], step_counter)
            
            if 'gpu' in resources:
                for gpu_id, gpu_data in resources['gpu'].items():
                    writer.add_scalar(f'GPU/{gpu_id}/Memory_Allocated_GB', gpu_data['memory_allocated_gb'], step_counter)
                    writer.add_scalar(f'GPU/{gpu_id}/Memory_Reserved_GB', gpu_data['memory_reserved_gb'], step_counter)
                    writer.add_scalar(f'GPU/{gpu_id}/Memory_Utilization_%', gpu_data['memory_utilization_percent'], step_counter)
            
            # 检查训练进程是否还在运行
            try:
                proc = psutil.Process(training_pid)
                if not proc.is_running():
                    print("⚠️ 训练进程已结束")
                    break
            except psutil.NoSuchProcess:
                print("⚠️ 训练进程不存在")
                break
            
            # 创建训练状态报告
            if step_counter % 30 == 0:  # 每30秒创建一次报告
                status_text = f"""
# 实时训练监控报告

## 系统资源状态
- CPU使用率: {resources['cpu_percent']:.1f}%
- 内存使用率: {resources['memory_percent']:.1f}%
- 内存使用量: {resources['memory_used_gb']:.2f}GB / {resources['memory_total_gb']:.2f}GB

## GPU状态
"""
                
                if 'gpu' in resources:
                    for gpu_id, gpu_data in resources['gpu'].items():
                        status_text += f"""
### {gpu_id.upper()}
- 显存分配: {gpu_data['memory_allocated_gb']:.2f}GB
- 显存预留: {gpu_data['memory_reserved_gb']:.2f}GB
- 显存利用率: {gpu_data['memory_utilization_percent']:.1f}%
"""
                
                status_text += f"""
## 监控信息
- 训练进程PID: {training_pid}
- 监控步骤: {step_counter}
- 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                
                writer.add_text('Status/System_Monitor', status_text, step_counter)
            
            step_counter += 1
            time.sleep(5)  # 每5秒监控一次
            
    except KeyboardInterrupt:
        print("\n⚠️ 监控被用户中断")
    
    except Exception as e:
        print(f"❌ 监控过程中出现错误: {str(e)}")
    
    finally:
        # 记录监控结束
        end_text = f"""
# 监控结束报告

监控于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 结束
总监控步骤: {step_counter}
训练进程PID: {training_pid}
"""
        writer.add_text('Status/Monitor_End', end_text, step_counter)
        writer.close()
        print(f"📊 监控结束，日志保存到: {tensorboard_dir}")

if __name__ == "__main__":
    main()
