#!/usr/bin/env python3
"""
TensorBoard可视化配置
定义各种图表和监控指标的配置
"""

# TensorBoard可视化配置
TENSORBOARD_CONFIG = {
    # 基础标量指标
    "scalars": {
        "loss": {
            "train_loss": "Loss/Train",
            "validation_loss": "Loss/Validation",
            "epoch_average_loss": "Loss/Epoch_Average",
        },
        "learning_rate": {
            "current_lr": "Learning_Rate",
            "base_lr": "Learning_Rate/Base",
            "effective_lr": "Learning_Rate/Effective",
        },
        "gradients": {
            "gradient_norm": "Gradient_Norm",
            "gradient_norm_clipped": "Gradient_Norm/Clipped",
        },
        "training_stats": {
            "epoch": "Epoch",
            "step": "Step",
            "samples_per_second": "Performance/Samples_Per_Second",
            "steps_per_second": "Performance/Steps_Per_Second",
        },
        "memory": {
            "gpu_memory_allocated": "Memory/GPU_0/Allocated_GB",
            "gpu_memory_reserved": "Memory/GPU_0/Reserved_GB",
            "gpu_memory_utilization": "Memory/GPU_0/Utilization_%",
        }
    },
    
    # 直方图指标
    "histograms": {
        "parameters": "Parameters/",
        "gradients": "Gradients/",
        "activations": "Activations/",
    },
    
    # 图像指标
    "images": {
        "sample_videos": "Videos/Samples",
        "generated_frames": "Videos/Generated_Frames",
        "attention_maps": "Attention/Maps",
    },
    
    # 自定义图表
    "figures": {
        "loss_curves": "Plots/Loss_Curve",
        "lr_schedule": "Plots/Learning_Rate",
        "gradient_norms": "Plots/Gradient_Norms",
        "training_summary": "Summary/Training_Summary",
    },
    
    # 超参数
    "hyperparameters": {
        "model_params": [
            "lora_rank",
            "lora_alpha", 
            "target_modules",
            "lora_dropout",
        ],
        "training_params": [
            "learning_rate",
            "batch_size",
            "num_epochs",
            "weight_decay",
            "warmup_steps",
        ],
        "video_params": [
            "width",
            "height", 
            "frames",
            "fps",
        ]
    }
}

# 可视化样式配置
VISUALIZATION_STYLES = {
    "colors": {
        "loss": "#1f77b4",           # 蓝色
        "learning_rate": "#ff7f0e",  # 橙色
        "gradient_norm": "#2ca02c",  # 绿色
        "memory": "#d62728",         # 红色
        "validation": "#9467bd",     # 紫色
    },
    
    "line_styles": {
        "train": "-",      # 实线
        "validation": "--", # 虚线
        "trend": ":",      # 点线
    },
    
    "figure_size": (12, 8),
    "dpi": 100,
    "font_size": 12,
}

# 监控阈值配置
MONITORING_THRESHOLDS = {
    "loss": {
        "min_improvement": 0.001,    # 最小改善幅度
        "patience": 10,              # 早停耐心值
        "nan_threshold": 1e6,        # NaN检测阈值
    },
    
    "gradient": {
        "max_norm": 10.0,            # 最大梯度范数
        "min_norm": 1e-8,            # 最小梯度范数
        "explosion_threshold": 100.0, # 梯度爆炸阈值
    },
    
    "memory": {
        "max_utilization": 95.0,     # 最大显存利用率(%)
        "warning_threshold": 85.0,   # 显存警告阈值(%)
    },
    
    "learning_rate": {
        "min_lr": 1e-8,              # 最小学习率
        "max_lr": 1e-2,              # 最大学习率
    }
}

# 日志记录频率配置
LOGGING_FREQUENCY = {
    "scalars": {
        "every_step": ["loss", "learning_rate", "gradient_norm"],
        "every_10_steps": ["memory", "performance"],
        "every_epoch": ["validation_metrics", "model_stats"],
    },
    
    "histograms": {
        "every_10_steps": ["gradients"],
        "every_50_steps": ["parameters"],
        "every_epoch": ["activations"],
    },
    
    "images": {
        "every_50_steps": ["sample_outputs"],
        "every_epoch": ["attention_maps"],
    },
    
    "figures": {
        "every_10_steps": ["loss_curves"],
        "every_50_steps": ["gradient_analysis"],
        "every_epoch": ["training_summary"],
    }
}

# 自定义标签和描述
METRIC_DESCRIPTIONS = {
    "Loss/Train": "训练损失 - 模型在训练数据上的损失值",
    "Loss/Validation": "验证损失 - 模型在验证数据上的损失值", 
    "Learning_Rate": "学习率 - 当前优化器使用的学习率",
    "Gradient_Norm": "梯度范数 - 所有参数梯度的L2范数",
    "Memory/GPU_0/Allocated_GB": "GPU显存分配 - 已分配的显存大小(GB)",
    "Memory/GPU_0/Utilization_%": "GPU显存利用率 - 显存使用百分比",
    "Parameters/": "参数分布 - 模型参数的直方图分布",
    "Gradients/": "梯度分布 - 参数梯度的直方图分布",
}

# TensorBoard插件配置
TENSORBOARD_PLUGINS = {
    "scalars": True,        # 标量图表
    "histograms": True,     # 直方图
    "images": True,         # 图像显示
    "graphs": True,         # 计算图
    "distributions": True,  # 分布图
    "projector": False,     # 嵌入投影器
    "hparams": True,        # 超参数
    "mesh": False,          # 3D网格
    "debugger": False,      # 调试器
}

# 导出配置
EXPORT_CONFIG = {
    "formats": ["png", "svg", "pdf"],  # 支持的导出格式
    "resolution": {
        "png": 300,         # PNG分辨率(DPI)
        "svg": None,        # SVG矢量格式
        "pdf": 300,         # PDF分辨率(DPI)
    },
    "auto_export": {
        "enabled": True,
        "frequency": "every_epoch",
        "metrics": ["loss_curves", "training_summary"],
    }
}

def get_tensorboard_config():
    """获取完整的TensorBoard配置"""
    return {
        "visualization": TENSORBOARD_CONFIG,
        "styles": VISUALIZATION_STYLES,
        "thresholds": MONITORING_THRESHOLDS,
        "frequency": LOGGING_FREQUENCY,
        "descriptions": METRIC_DESCRIPTIONS,
        "plugins": TENSORBOARD_PLUGINS,
        "export": EXPORT_CONFIG,
    }

def validate_config():
    """验证配置的有效性"""
    config = get_tensorboard_config()
    
    print("🔍 验证TensorBoard配置...")
    
    # 检查必需的配置项
    required_sections = ["visualization", "styles", "thresholds", "frequency"]
    for section in required_sections:
        if section not in config:
            print(f"❌ 缺少配置节: {section}")
            return False
        else:
            print(f"✅ 配置节 '{section}' 存在")
    
    # 检查颜色配置
    colors = config["styles"]["colors"]
    for metric, color in colors.items():
        if not color.startswith("#") or len(color) != 7:
            print(f"❌ 无效的颜色配置: {metric} = {color}")
            return False
    
    print("✅ TensorBoard配置验证通过")
    return True

if __name__ == "__main__":
    # 验证配置
    if validate_config():
        print("\n📊 TensorBoard配置概览:")
        config = get_tensorboard_config()
        
        print(f"  - 标量指标: {len(config['visualization']['scalars'])} 类")
        print(f"  - 直方图指标: {len(config['visualization']['histograms'])} 类")
        print(f"  - 图像指标: {len(config['visualization']['images'])} 类")
        print(f"  - 自定义图表: {len(config['visualization']['figures'])} 类")
        print(f"  - 监控阈值: {len(config['thresholds'])} 类")
        print(f"  - 启用插件: {sum(config['plugins'].values())} 个")
    else:
        print("❌ 配置验证失败")
