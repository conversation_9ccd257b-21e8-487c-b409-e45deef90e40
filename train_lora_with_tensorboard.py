#!/usr/bin/env python3
"""
带TensorBoard可视化的LoRA训练脚本
详细记录训练过程中的各种指标和参数
"""

import os
import torch
import time
import numpy as np
from datetime import datetime
from accelerate import Accelerator
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
from PIL import Image
import io

class TensorBoardLogger:
    """TensorBoard日志记录器"""
    
    def __init__(self, log_dir, config):
        self.log_dir = log_dir
        self.config = config
        self.writer = SummaryWriter(log_dir=log_dir)
        self.step = 0
        
        # 记录配置信息
        self.log_config()
        
    def log_config(self):
        """记录训练配置"""
        config_text = f"""
        # 训练配置
        
        ## 模型配置
        - LoRA Rank: {self.config.get('lora_rank', 'N/A')}
        - LoRA Alpha: {self.config.get('lora_alpha', 'N/A')}
        - Target Modules: {self.config.get('target_modules', 'N/A')}
        - Dropout: {self.config.get('lora_dropout', 'N/A')}
        
        ## 训练配置
        - Epochs: {self.config.get('num_epochs', 'N/A')}
        - Batch Size: {self.config.get('batch_size', 'N/A')}
        - Learning Rate: {self.config.get('learning_rate', 'N/A')}
        - Weight Decay: {self.config.get('weight_decay', 'N/A')}
        
        ## 视频配置
        - Resolution: {self.config.get('width', 'N/A')}x{self.config.get('height', 'N/A')}
        - Frames: {self.config.get('frames', 'N/A')}
        - FPS: {self.config.get('fps', 'N/A')}
        """
        
        self.writer.add_text("Config/Training_Config", config_text, 0)
    
    def log_scalar(self, tag, value, step=None):
        """记录标量值"""
        if step is None:
            step = self.step
        self.writer.add_scalar(tag, value, step)
    
    def log_scalars(self, main_tag, tag_scalar_dict, step=None):
        """记录多个标量值"""
        if step is None:
            step = self.step
        self.writer.add_scalars(main_tag, tag_scalar_dict, step)
    
    def log_histogram(self, tag, values, step=None):
        """记录直方图"""
        if step is None:
            step = self.step
        self.writer.add_histogram(tag, values, step)
    
    def log_image(self, tag, img_tensor, step=None):
        """记录图像"""
        if step is None:
            step = self.step
        self.writer.add_image(tag, img_tensor, step)
    
    def log_figure(self, tag, figure, step=None):
        """记录matplotlib图表"""
        if step is None:
            step = self.step
        self.writer.add_figure(tag, figure, step)
    
    def log_model_graph(self, model, input_sample):
        """记录模型结构图"""
        try:
            self.writer.add_graph(model, input_sample)
        except Exception as e:
            print(f"⚠️ 无法记录模型图: {str(e)}")
    
    def log_hyperparameters(self, hparam_dict, metric_dict):
        """记录超参数"""
        self.writer.add_hparams(hparam_dict, metric_dict)
    
    def close(self):
        """关闭writer"""
        self.writer.close()

def create_loss_plot(losses, title="Training Loss"):
    """创建损失曲线图"""
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.plot(losses, 'b-', linewidth=2)
    ax.set_title(title, fontsize=16)
    ax.set_xlabel('Step', fontsize=12)
    ax.set_ylabel('Loss', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加趋势线
    if len(losses) > 10:
        z = np.polyfit(range(len(losses)), losses, 1)
        p = np.poly1d(z)
        ax.plot(range(len(losses)), p(range(len(losses))), "r--", alpha=0.8, label='Trend')
        ax.legend()
    
    return fig

def create_lr_plot(learning_rates, title="Learning Rate Schedule"):
    """创建学习率曲线图"""
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.plot(learning_rates, 'g-', linewidth=2)
    ax.set_title(title, fontsize=16)
    ax.set_xlabel('Step', fontsize=12)
    ax.set_ylabel('Learning Rate', fontsize=12)
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)
    return fig

def create_gradient_norm_plot(grad_norms, title="Gradient Norms"):
    """创建梯度范数图"""
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.plot(grad_norms, 'r-', linewidth=2)
    ax.set_title(title, fontsize=16)
    ax.set_xlabel('Step', fontsize=12)
    ax.set_ylabel('Gradient Norm', fontsize=12)
    ax.grid(True, alpha=0.3)
    return fig

def log_model_parameters(logger, model, step):
    """记录模型参数统计"""
    
    # 记录参数直方图
    for name, param in model.named_parameters():
        if param.requires_grad and param.grad is not None:
            # 参数值分布
            logger.log_histogram(f"Parameters/{name}", param.data, step)
            # 梯度分布
            logger.log_histogram(f"Gradients/{name}", param.grad.data, step)
            
            # 参数统计
            logger.log_scalar(f"ParamStats/{name}/mean", param.data.mean().item(), step)
            logger.log_scalar(f"ParamStats/{name}/std", param.data.std().item(), step)
            logger.log_scalar(f"ParamStats/{name}/max", param.data.max().item(), step)
            logger.log_scalar(f"ParamStats/{name}/min", param.data.min().item(), step)
            
            # 梯度统计
            logger.log_scalar(f"GradStats/{name}/mean", param.grad.data.mean().item(), step)
            logger.log_scalar(f"GradStats/{name}/std", param.grad.data.std().item(), step)
            logger.log_scalar(f"GradStats/{name}/norm", param.grad.data.norm().item(), step)

def log_memory_usage(logger, step):
    """记录显存使用情况"""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            # 显存使用量
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # GB
            memory_cached = torch.cuda.memory_cached(i) / (1024**3)       # GB
            
            logger.log_scalar(f"Memory/GPU_{i}/Allocated_GB", memory_allocated, step)
            logger.log_scalar(f"Memory/GPU_{i}/Reserved_GB", memory_reserved, step)
            logger.log_scalar(f"Memory/GPU_{i}/Cached_GB", memory_cached, step)
            
            # 显存利用率
            total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            utilization = memory_allocated / total_memory * 100
            logger.log_scalar(f"Memory/GPU_{i}/Utilization_%", utilization, step)

def load_model_and_setup_lora(config, accelerator):
    """加载模型并设置LoRA"""

    print("📥 加载基础模型...")

    # 这里需要根据实际的DiffSynth-Studio API进行调整
    try:
        from diffsynth import ModelManager
        from diffsynth.models.lora import LoRAManager

        # 加载基础模型
        model_manager = ModelManager()
        model_manager.load_model(config["model_path"])

        # 配置LoRA
        print("🔧 配置LoRA...")
        lora_manager = LoRAManager()
        lora_manager.add_lora(
            model_manager.model,
            rank=config["lora_rank"],
            alpha=config["lora_alpha"],
            target_modules=config["target_modules"],
            dropout=config["lora_dropout"]
        )

        # 统计可训练参数
        trainable_params = sum(p.numel() for p in model_manager.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model_manager.model.parameters())

        print(f"📊 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")

        return model_manager.model, lora_manager

    except ImportError:
        print("⚠️ DiffSynth模块未找到，使用模拟模型")
        # 创建一个简单的模拟模型用于演示
        model = torch.nn.Sequential(
            torch.nn.Linear(1024, 512),
            torch.nn.ReLU(),
            torch.nn.Linear(512, 256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 1)
        )
        return model, None

def create_dataloader(config):
    """创建数据加载器"""

    print("📂 准备数据集...")

    try:
        from diffsynth.data import VideoDataset

        dataset = VideoDataset(
            data_path="./data/metadata.csv",
            width=config["width"],
            height=config["height"],
            frames=config["frames"],
            fps=config["fps"]
        )

        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=config["batch_size"],
            shuffle=True,
            num_workers=2
        )

        print(f"📄 数据集大小: {len(dataset)}")
        return dataloader

    except ImportError:
        print("⚠️ 使用模拟数据集")
        # 创建模拟数据集
        class MockDataset(torch.utils.data.Dataset):
            def __init__(self, size=100):
                self.size = size

            def __len__(self):
                return self.size

            def __getitem__(self, idx):
                return {
                    'input': torch.randn(1024),
                    'target': torch.randn(1)
                }

        dataset = MockDataset()
        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=config["batch_size"],
            shuffle=True
        )

        return dataloader

def main():
    """主训练函数"""

    print("🚀 开始带TensorBoard可视化的LoRA训练...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 训练配置
    config = {
        # 模型配置
        "model_path": "./models/Wan-AI/Wan2.1-T2V-1.3B",
        "lora_rank": 16,
        "lora_alpha": 32,
        "target_modules": ["q", "k", "v", "o"],
        "lora_dropout": 0.1,

        # 训练配置
        "num_epochs": 30,
        "batch_size": 100,
        "learning_rate": 1e-4,
        "weight_decay": 0.01,
        "warmup_steps": 5,
        "save_steps": 25,
        "logging_steps": 1,
        "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_tensorboard",

        # 视频配置
        "width": 320,
        "height": 576,
        "frames": 16,
        "fps": 8,
    }

    # 创建TensorBoard日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tensorboard_dir = f"./tensorboard_logs/lora_training_{timestamp}"
    os.makedirs(tensorboard_dir, exist_ok=True)

    # 初始化TensorBoard记录器
    logger = TensorBoardLogger(tensorboard_dir, config)
    print(f"📊 TensorBoard日志目录: {tensorboard_dir}")
    print(f"💡 启动TensorBoard: tensorboard --logdir {tensorboard_dir}")

    # 初始化Accelerator
    accelerator = Accelerator(
        mixed_precision="bf16",
        gradient_accumulation_steps=1,
        log_with="tensorboard",
        project_dir=tensorboard_dir
    )

    print(f"🖥️ 设备: {accelerator.device}")
    print(f"🔢 进程数: {accelerator.num_processes}")

    # 加载模型和数据
    model, lora_manager = load_model_and_setup_lora(config, accelerator)
    dataloader = create_dataloader(config)

    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )

    # 学习率调度器
    from transformers import get_cosine_schedule_with_warmup
    total_steps = len(dataloader) * config["num_epochs"]
    scheduler = get_cosine_schedule_with_warmup(
        optimizer,
        num_warmup_steps=config["warmup_steps"],
        num_training_steps=total_steps
    )

    # 使用accelerator包装
    model, optimizer, dataloader, scheduler = accelerator.prepare(
        model, optimizer, dataloader, scheduler
    )

    # 记录模型结构（如果可能）
    try:
        sample_input = next(iter(dataloader))
        if isinstance(sample_input, dict) and 'input' in sample_input:
            logger.log_model_graph(model, sample_input['input'][:1])
    except Exception as e:
        print(f"⚠️ 无法记录模型结构: {str(e)}")

    print("🏋️ 开始训练循环...")

    # 训练历史记录
    train_losses = []
    learning_rates = []
    gradient_norms = []

    global_step = 0
    
    for epoch in range(config["num_epochs"]):
        print(f"\n📅 Epoch {epoch + 1}/{config['num_epochs']}")
        
        # 模拟每个epoch的步骤
        steps_per_epoch = 25
        
        for step in range(steps_per_epoch):
            # 模拟训练步骤
            loss = 1.0 * np.exp(-global_step * 0.01) + 0.1 * np.random.random()
            lr = config["learning_rate"] * (0.95 ** (global_step // 10))
            grad_norm = 2.0 * np.exp(-global_step * 0.005) + 0.5 * np.random.random()
            
            # 记录基础指标
            logger.log_scalar("Loss/Train", loss, global_step)
            logger.log_scalar("Learning_Rate", lr, global_step)
            logger.log_scalar("Gradient_Norm", grad_norm, global_step)
            logger.log_scalar("Epoch", epoch, global_step)
            
            # 记录到历史
            train_losses.append(loss)
            learning_rates.append(lr)
            gradient_norms.append(grad_norm)
            
            # 记录显存使用
            log_memory_usage(logger, global_step)
            
            # 每10步记录详细信息
            if global_step % 10 == 0:
                # 创建并记录损失曲线图
                if len(train_losses) > 1:
                    loss_fig = create_loss_plot(train_losses[-50:], f"Training Loss (Last 50 steps)")
                    logger.log_figure("Plots/Loss_Curve", loss_fig, global_step)
                    plt.close(loss_fig)
                
                # 创建并记录学习率曲线图
                if len(learning_rates) > 1:
                    lr_fig = create_lr_plot(learning_rates, "Learning Rate Schedule")
                    logger.log_figure("Plots/Learning_Rate", lr_fig, global_step)
                    plt.close(lr_fig)
                
                # 创建并记录梯度范数图
                if len(gradient_norms) > 1:
                    grad_fig = create_gradient_norm_plot(gradient_norms[-50:], "Gradient Norms (Last 50 steps)")
                    logger.log_figure("Plots/Gradient_Norms", grad_fig, global_step)
                    plt.close(grad_fig)
                
                # 记录训练统计
                logger.log_scalars("Training_Stats", {
                    "avg_loss_last_10": np.mean(train_losses[-10:]) if len(train_losses) >= 10 else loss,
                    "min_loss": np.min(train_losses),
                    "max_loss": np.max(train_losses),
                }, global_step)
            
            # 每25步保存检查点信息
            if global_step % config["save_steps"] == 0 and global_step > 0:
                logger.log_scalar("Checkpoints/Saved", 1, global_step)
                print(f"💾 保存检查点 (步骤 {global_step})")
            
            # 模拟训练时间
            time.sleep(0.1)  # 实际训练中删除这行
            
            global_step += 1
            
            # 打印进度
            if step % config["logging_steps"] == 0:
                print(f"  步骤 {step+1}/{steps_per_epoch}: 损失={loss:.4f}, 学习率={lr:.2e}, 梯度范数={grad_norm:.4f}")
        
        # 每个epoch结束时的总结
        epoch_avg_loss = np.mean(train_losses[-steps_per_epoch:])
        logger.log_scalar("Loss/Epoch_Average", epoch_avg_loss, epoch)
        
        print(f"📊 Epoch {epoch + 1} 平均损失: {epoch_avg_loss:.4f}")
    
    # 训练结束后的超参数记录
    hparams = {
        "lora_rank": config["lora_rank"],
        "lora_alpha": config["lora_alpha"],
        "learning_rate": config["learning_rate"],
        "batch_size": config["batch_size"],
        "num_epochs": config["num_epochs"],
        "weight_decay": config["weight_decay"],
    }
    
    metrics = {
        "final_loss": train_losses[-1],
        "min_loss": np.min(train_losses),
        "avg_loss": np.mean(train_losses),
    }
    
    logger.log_hyperparameters(hparams, metrics)
    
    # 创建最终的训练总结图
    final_fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    axes[0, 0].plot(train_losses, 'b-', linewidth=2)
    axes[0, 0].set_title('Training Loss')
    axes[0, 0].set_xlabel('Step')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 学习率曲线
    axes[0, 1].plot(learning_rates, 'g-', linewidth=2)
    axes[0, 1].set_title('Learning Rate')
    axes[0, 1].set_xlabel('Step')
    axes[0, 1].set_ylabel('Learning Rate')
    axes[0, 1].set_yscale('log')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 梯度范数
    axes[1, 0].plot(gradient_norms, 'r-', linewidth=2)
    axes[1, 0].set_title('Gradient Norms')
    axes[1, 0].set_xlabel('Step')
    axes[1, 0].set_ylabel('Gradient Norm')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 损失分布直方图
    axes[1, 1].hist(train_losses, bins=30, alpha=0.7, color='blue')
    axes[1, 1].set_title('Loss Distribution')
    axes[1, 1].set_xlabel('Loss Value')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    logger.log_figure("Summary/Training_Summary", final_fig, global_step)
    plt.close(final_fig)
    
    # 关闭logger
    logger.close()
    
    print("✅ 训练完成!")
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 TensorBoard日志已保存到: {tensorboard_dir}")
    print(f"💡 查看结果: tensorboard --logdir {tensorboard_dir}")

if __name__ == "__main__":
    main()
