#!/usr/bin/env python3
"""
合并模型测试脚本
测试不同alpha值的合并模型效果
"""

import os
import torch
from safetensors.torch import load_file
import time
from pathlib import Path

def analyze_merged_models():
    """分析合并模型文件"""
    
    print("=== 合并模型分析 ===")
    
    merged_dir = "./models/merged"
    if not os.path.exists(merged_dir):
        print(f"❌ 合并模型目录不存在: {merged_dir}")
        return False
    
    # 获取所有合并模型
    merged_files = []
    for file in os.listdir(merged_dir):
        if file.endswith('.safetensors'):
            file_path = os.path.join(merged_dir, file)
            file_size = os.path.getsize(file_path) / (1024**3)  # GB
            merged_files.append((file, file_path, file_size))
    
    if not merged_files:
        print(f"❌ 未找到合并模型文件")
        return False
    
    merged_files.sort()  # 按文件名排序
    
    print(f"📊 找到 {len(merged_files)} 个合并模型:")
    for filename, filepath, size in merged_files:
        print(f"  - {filename}: {size:.2f} GB")
    
    # 分析每个模型
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    
    if not os.path.exists(base_model_path):
        print(f"❌ 基础模型不存在: {base_model_path}")
        return False
    
    print(f"\n📥 加载基础模型进行对比...")
    base_weights = load_file(base_model_path)
    print(f"✅ 基础模型加载完成，包含 {len(base_weights)} 个权重")
    
    # 分析每个合并模型与基础模型的差异
    for filename, filepath, size in merged_files:
        print(f"\n{'='*50}")
        print(f"🧪 分析模型: {filename}")
        
        try:
            # 加载合并模型
            merged_weights = load_file(filepath)
            print(f"✅ 模型加载成功，包含 {len(merged_weights)} 个权重")
            
            # 计算权重差异
            differences = []
            for key in base_weights.keys():
                if key in merged_weights:
                    base_tensor = base_weights[key]
                    merged_tensor = merged_weights[key]
                    
                    # 计算差异
                    diff = merged_tensor - base_tensor
                    max_diff = diff.abs().max().item()
                    mean_diff = diff.abs().mean().item()
                    
                    differences.append({
                        'key': key,
                        'max_diff': max_diff,
                        'mean_diff': mean_diff,
                        'shape': base_tensor.shape
                    })
            
            # 排序并显示最大差异的权重
            differences.sort(key=lambda x: x['max_diff'], reverse=True)
            
            print(f"📊 权重差异分析:")
            print(f"  - 总权重数: {len(differences)}")
            
            if differences:
                max_diff_overall = max(d['max_diff'] for d in differences)
                mean_diff_overall = sum(d['mean_diff'] for d in differences) / len(differences)
                
                print(f"  - 最大差异: {max_diff_overall:.8f}")
                print(f"  - 平均差异: {mean_diff_overall:.8f}")
                
                print(f"  - 差异最大的5个权重:")
                for i, diff in enumerate(differences[:5]):
                    print(f"    {i+1}. {diff['key']}: max={diff['max_diff']:.8f}, mean={diff['mean_diff']:.8f}")
            
            # 解析模型信息
            model_info = parse_model_filename(filename)
            print(f"📋 模型信息:")
            print(f"  - LoRA类型: {model_info['lora_type']}")
            print(f"  - Alpha值: {model_info['alpha']}")
            print(f"  - 文件大小: {size:.2f} GB")
            
        except Exception as e:
            print(f"❌ 模型分析失败: {str(e)}")
            continue
    
    return True

def parse_model_filename(filename):
    """解析模型文件名"""
    
    # 文件名格式: Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors
    parts = filename.replace('.safetensors', '').split('_')
    
    info = {
        'lora_type': 'unknown',
        'alpha': 'unknown'
    }
    
    for i, part in enumerate(parts):
        if part in ['optimized', 'minimal']:
            info['lora_type'] = part
        elif part == 'alpha' and i + 1 < len(parts):
            info['alpha'] = parts[i + 1]
    
    return info

def create_model_comparison_report():
    """创建模型对比报告"""
    
    print(f"\n{'='*60}")
    print("📋 生成模型对比报告")
    
    merged_dir = "./models/merged"
    report_path = "./reports/merged_models_comparison.md"
    
    os.makedirs("./reports", exist_ok=True)
    
    # 获取模型信息
    models_info = []
    if os.path.exists(merged_dir):
        for file in os.listdir(merged_dir):
            if file.endswith('.safetensors'):
                file_path = os.path.join(merged_dir, file)
                file_size = os.path.getsize(file_path) / (1024**3)
                model_info = parse_model_filename(file)
                
                models_info.append({
                    'filename': file,
                    'filepath': file_path,
                    'size': file_size,
                    'lora_type': model_info['lora_type'],
                    'alpha': model_info['alpha']
                })
    
    # 生成报告
    report_content = f"""# Wan2.1-T2V-1.3B 合并模型对比报告

## 生成时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 模型概览

### 合并模型统计
- 总模型数量: {len(models_info)}
- LoRA类型: optimized (rank=16), minimal (rank=8)
- Alpha值: 0.5, 1.0, 1.5
- 文件格式: SafeTensors

### 模型列表
"""
    
    # 按类型和alpha值分组
    optimized_models = [m for m in models_info if m['lora_type'] == 'optimized']
    minimal_models = [m for m in models_info if m['lora_type'] == 'minimal']
    
    report_content += f"""
#### 优化版模型 (rank=16, 3轮训练)
"""
    for model in sorted(optimized_models, key=lambda x: float(x['alpha'])):
        report_content += f"- `{model['filename']}` (Alpha={model['alpha']}, {model['size']:.2f}GB)\n"
    
    report_content += f"""
#### 极限版模型 (rank=8, 1轮训练)
"""
    for model in sorted(minimal_models, key=lambda x: float(x['alpha'])):
        report_content += f"- `{model['filename']}` (Alpha={model['alpha']}, {model['size']:.2f}GB)\n"
    
    report_content += f"""

## Alpha值说明

### Alpha=0.5 (保守合并)
- 权重变化: 50%的LoRA影响
- 适用场景: 保持原模型特性，轻微调整
- 推荐用途: 初次测试，风险较低

### Alpha=1.0 (标准合并)
- 权重变化: 100%的LoRA影响
- 适用场景: 完整应用训练效果
- 推荐用途: 正常使用，平衡效果

### Alpha=1.5 (激进合并)
- 权重变化: 150%的LoRA影响
- 适用场景: 强化训练效果
- 推荐用途: 追求最大改变，可能过拟合

## 使用建议

### 推荐配置
1. **质量优先**: `optimized_alpha_1.0` - 最佳平衡
2. **速度优先**: `minimal_alpha_1.0` - 快速推理
3. **保守选择**: `optimized_alpha_0.5` - 稳定效果
4. **激进选择**: `optimized_alpha_1.5` - 最大变化

### 测试顺序
1. 先测试 `optimized_alpha_1.0`
2. 根据效果调整alpha值
3. 对比minimal版本的速度优势
4. 选择最适合的配置

## 技术细节

### 合并算法
```
merged_weight = base_weight + (lora_B @ lora_A) * alpha
```

### 文件结构
- 基础模型: 825个权重张量
- 优化版LoRA: 240个层对 (q,k,v,o)
- 极限版LoRA: 120个层对 (q,k)

### 存储信息
- 单个模型大小: 5.29GB
- 总存储需求: {len(models_info) * 5.29:.1f}GB
- 格式: SafeTensors (安全可靠)
"""
    
    # 保存报告
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 对比报告已生成: {report_path}")
    return report_path

def validate_merged_models():
    """验证合并模型的完整性"""
    
    print(f"\n{'='*50}")
    print("🔍 验证合并模型完整性")
    
    merged_dir = "./models/merged"
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    
    if not os.path.exists(base_model_path):
        print(f"❌ 基础模型不存在")
        return False
    
    # 加载基础模型
    base_weights = load_file(base_model_path)
    base_keys = set(base_weights.keys())
    
    validation_results = []
    
    for file in os.listdir(merged_dir):
        if file.endswith('.safetensors'):
            file_path = os.path.join(merged_dir, file)
            
            try:
                # 加载合并模型
                merged_weights = load_file(file_path)
                merged_keys = set(merged_weights.keys())
                
                # 验证键名一致性
                missing_keys = base_keys - merged_keys
                extra_keys = merged_keys - base_keys
                
                # 验证张量形状
                shape_mismatches = []
                for key in base_keys & merged_keys:
                    if base_weights[key].shape != merged_weights[key].shape:
                        shape_mismatches.append(key)
                
                result = {
                    'filename': file,
                    'total_keys': len(merged_keys),
                    'missing_keys': len(missing_keys),
                    'extra_keys': len(extra_keys),
                    'shape_mismatches': len(shape_mismatches),
                    'valid': len(missing_keys) == 0 and len(extra_keys) == 0 and len(shape_mismatches) == 0
                }
                
                validation_results.append(result)
                
                if result['valid']:
                    print(f"  ✅ {file}: 验证通过")
                else:
                    print(f"  ❌ {file}: 验证失败")
                    if missing_keys:
                        print(f"    缺少键: {len(missing_keys)}")
                    if extra_keys:
                        print(f"    多余键: {len(extra_keys)}")
                    if shape_mismatches:
                        print(f"    形状不匹配: {len(shape_mismatches)}")
                
            except Exception as e:
                print(f"  ❌ {file}: 加载失败 - {str(e)}")
                validation_results.append({
                    'filename': file,
                    'valid': False,
                    'error': str(e)
                })
    
    # 统计验证结果
    valid_count = sum(1 for r in validation_results if r.get('valid', False))
    total_count = len(validation_results)
    
    print(f"\n📊 验证统计:")
    print(f"  - 总模型数: {total_count}")
    print(f"  - 验证通过: {valid_count}")
    print(f"  - 验证失败: {total_count - valid_count}")
    print(f"  - 成功率: {valid_count/total_count*100:.1f}%")
    
    return valid_count == total_count

def main():
    """主函数"""
    
    print("🚀 开始合并模型测试与对比...")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析合并模型
    analysis_success = analyze_merged_models()
    
    if not analysis_success:
        print("❌ 模型分析失败")
        return
    
    # 验证模型完整性
    validation_success = validate_merged_models()
    
    # 生成对比报告
    report_path = create_model_comparison_report()
    
    print(f"\n{'='*60}")
    print("📋 测试总结:")
    print(f"✅ 模型分析: {'成功' if analysis_success else '失败'}")
    print(f"✅ 完整性验证: {'成功' if validation_success else '失败'}")
    print(f"✅ 对比报告: 已生成")
    print(f"📄 报告路径: {report_path}")
    
    print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if analysis_success and validation_success:
        print("🎉 合并模型测试与对比全部完成！")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
