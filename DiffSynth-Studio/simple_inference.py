#!/usr/bin/env python3
"""
简化的视频生成脚本
使用训练好的LoRA权重生成视频
"""

import torch
from diffsynth import ModelManager, WanVideoPipeline
import os

def generate_video_with_lora():
    """使用LoRA生成视频"""
    
    # 清理显存
    torch.cuda.empty_cache()
    
    # 配置
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    lora_path = "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors"
    output_path = "./outputs/lora_test_video.mp4"
    
    os.makedirs("./outputs", exist_ok=True)
    
    try:
        print("📥 加载模型...")
        model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cuda:0")
        
        # 只加载必要的模型
        model_manager.load_models([
            f"{base_model_path}/diffusion_pytorch_model.safetensors",
            f"{base_model_path}/models_t5_umt5-xxl-enc-bf16.pth", 
            f"{base_model_path}/Wan2.1_VAE.pth"
        ])
        
        print("📥 加载LoRA...")
        model_manager.load_lora(lora_path, lora_alpha=1.0)
        
        print("🔧 创建管道...")
        pipe = WanVideoPipeline.from_model_manager(model_manager)
        
        print("🎬 生成视频...")
        video = pipe(
            prompt="from sunset to night, a small town, light, house, river",
            height=256,
            width=448,
            num_frames=8,  # 更少帧数
            num_inference_steps=10  # 更少步数
        )
        
        print("💾 保存视频...")
        video.save(output_path, fps=4)
        
        file_size = os.path.getsize(output_path) / (1024*1024)
        print(f"✅ 视频已保存: {output_path} ({file_size:.1f}MB)")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        return False
    finally:
        # 清理显存
        if 'pipe' in locals():
            del pipe
        if 'model_manager' in locals():
            del model_manager
        torch.cuda.empty_cache()

if __name__ == "__main__":
    generate_video_with_lora()
