#!/usr/bin/env python3
"""
创建文生视频(T2V)自定义数据集
为Wan2.1-T2V-1.3B微调生成真实的视频数据集
"""

import os
import json
import pandas as pd
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2
from datetime import datetime
import math
import random

class T2VDatasetCreator:
    def __init__(self, output_dir="data/t2v_custom_dataset"):
        self.output_dir = Path(output_dir)
        self.videos_dir = self.output_dir / "videos"
        self.metadata_file = self.output_dir / "metadata.csv"
        
        # 创建目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.videos_dir.mkdir(exist_ok=True)
        
        # 数据记录
        self.dataset_records = []
        
        print(f"🎬 文生视频(T2V)数据集创建器初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   视频目录: {self.videos_dir}")
    
    def create_animated_video(self, scene_config, duration=3.0, fps=15, output_path=None):
        """创建动画视频"""
        width, height = 832, 480
        total_frames = int(duration * fps)

        if output_path is None:
            output_path = f"/tmp/temp_video_{scene_config['name']}.mp4"

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        print(f"   创建视频: {scene_config['name']} ({total_frames}帧, {fps}fps)")

        for frame_idx in range(total_frames):
            # 创建帧
            frame = self.create_animated_frame(scene_config, frame_idx, total_frames, width, height)
            # 转换PIL图像到OpenCV格式
            frame_cv = cv2.cvtColor(np.array(frame), cv2.COLOR_RGB2BGR)
            out.write(frame_cv)

        out.release()
        return output_path
    
    def create_animated_frame(self, scene_config, frame_idx, total_frames, width, height):
        """创建单个动画帧"""
        progress = frame_idx / total_frames
        
        # 创建基础图像
        img = Image.new('RGB', (width, height), color=scene_config['colors'][0])
        draw = ImageDraw.Draw(img)
        
        # 根据场景类型添加动画效果
        animation_func = getattr(self, f"animate_{scene_config['name']}", None)
        if animation_func:
            animation_func(draw, progress, width, height, scene_config['colors'])
        
        return img
    
    def animate_ocean_waves(self, draw, progress, width, height, colors):
        """海洋波浪动画"""
        # 渐变背景 - 天空到海洋
        for y in range(height):
            ratio = y / height
            if y < height * 0.6:  # 天空部分
                r = int(135 + (255-135) * (1-y/(height*0.6)))
                g = int(206 + (140-206) * (1-y/(height*0.6)))
                b = int(235 + (0-235) * (1-y/(height*0.6)))
            else:  # 海洋部分
                ocean_ratio = (y - height*0.6) / (height*0.4)
                r = int(0 + 50 * ocean_ratio)
                g = int(100 + 50 * ocean_ratio)
                b = int(200 + 55 * ocean_ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 动态波浪
        wave_base = int(height * 0.6)
        for wave_num in range(5):
            wave_offset = (progress * 200 + wave_num * 50) % 100
            for x in range(0, width, 10):
                wave_height = 15 + 10 * math.sin((x + wave_offset) * 0.02 + wave_num)
                wave_y = wave_base + wave_num * 20 + int(wave_height)
                draw.ellipse([x, wave_y, x+20, wave_y+8], fill=(255, 255, 255, 150))
    
    def animate_forest_wind(self, draw, progress, width, height, colors):
        """森林风吹动画"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(34 + (144-34) * ratio)
            g = int(139 + (238-139) * ratio)
            b = int(34 + (144-34) * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 摇摆的树木
        for tree_idx in range(8):
            tree_x = tree_idx * 100 + 50
            sway = int(15 * math.sin(progress * math.pi * 2 + tree_idx * 0.5))
            
            # 树干
            trunk_x = tree_x + sway
            draw.rectangle([trunk_x, height-150, trunk_x+20, height], fill=(101, 67, 33))
            
            # 树冠
            crown_x = trunk_x - 30 + int(10 * math.sin(progress * math.pi * 3 + tree_idx))
            crown_y = height - 200
            draw.ellipse([crown_x, crown_y, crown_x+80, crown_y+80], fill=colors[1])
    
    def animate_mountain_clouds(self, draw, progress, width, height, colors):
        """山脉云朵动画"""
        # 天空渐变
        for y in range(height//2):
            ratio = y / (height//2)
            r = int(135 + (255-135) * (1-ratio))
            g = int(206 + (255-206) * (1-ratio))
            b = int(235)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 山脉轮廓
        mountain_points = []
        for x in range(0, width, 20):
            mountain_height = height//2 + int(100 * math.sin(x * 0.01) + 50 * math.sin(x * 0.03))
            mountain_points.extend([x, mountain_height])
        mountain_points.extend([width, height, 0, height])
        draw.polygon(mountain_points, fill=(100, 100, 100))
        
        # 移动的云朵
        cloud_offset = int(progress * 150) % 200
        for i in range(4):
            cloud_x = i * 200 - cloud_offset
            cloud_y = 50 + i * 20
            draw.ellipse([cloud_x, cloud_y, cloud_x+120, cloud_y+40], fill=(255, 255, 255, 200))
    
    def animate_city_traffic(self, draw, progress, width, height, colors):
        """城市交通动画"""
        # 夜空背景
        for y in range(height//3):
            ratio = y / (height//3)
            r = int(25 + 50 * ratio)
            g = int(25 + 50 * ratio)
            b = int(112 + 50 * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 建筑物轮廓
        for building in range(6):
            building_x = building * 130 + 20
            building_height = 200 + random.randint(-50, 100)
            draw.rectangle([building_x, height-building_height, building_x+100, height], fill=(40, 40, 40))
            
            # 窗户灯光
            for floor in range(building_height//30):
                for window in range(3):
                    if random.random() > 0.3:  # 70%的窗户有灯光
                        window_x = building_x + 20 + window * 25
                        window_y = height - building_height + floor * 30 + 10
                        brightness = int(200 + 55 * math.sin(progress * math.pi * 2 + building + floor))
                        draw.rectangle([window_x, window_y, window_x+15, window_y+20], 
                                     fill=(brightness, brightness, 100))
        
        # 移动的车辆灯光
        car_offset = int(progress * 300) % width
        for car in range(3):
            car_x = (car * 200 + car_offset) % width
            car_y = height - 50
            draw.ellipse([car_x, car_y, car_x+40, car_y+15], fill=(255, 255, 255))
    
    def animate_flower_garden(self, draw, progress, width, height, colors):
        """花园花朵动画"""
        # 天空到草地渐变
        for y in range(height):
            if y < height * 0.3:  # 天空
                ratio = y / (height * 0.3)
                r = int(135 + (255-135) * (1-ratio))
                g = int(206 + (255-206) * (1-ratio))
                b = 235
            else:  # 草地
                ratio = (y - height * 0.3) / (height * 0.7)
                r = int(34 + 50 * ratio)
                g = int(139 + 50 * ratio)
                b = int(34 + 50 * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 摇摆的花朵
        flower_colors = [(255, 192, 203), (255, 20, 147), (138, 43, 226), (255, 165, 0)]
        for flower_idx in range(20):
            flower_x = (flower_idx % 5) * 160 + 80
            flower_y = (flower_idx // 5) * 80 + 250
            
            # 花朵摇摆
            sway = int(10 * math.sin(progress * math.pi * 2 + flower_idx * 0.3))
            final_x = flower_x + sway
            
            # 花茎
            draw.line([final_x+15, flower_y+30, final_x+15, flower_y+60], fill=(34, 139, 34), width=3)
            
            # 花朵
            flower_color = flower_colors[flower_idx % len(flower_colors)]
            draw.ellipse([final_x, flower_y, final_x+30, flower_y+30], fill=flower_color)
    
    def animate_desert_sandstorm(self, draw, progress, width, height, colors):
        """沙漠沙尘暴动画"""
        # 沙漠背景渐变
        for y in range(height):
            ratio = y / height
            r = int(238 + (255-238) * (1-ratio))
            g = int(203 + (218-203) * (1-ratio))
            b = int(173 + (185-173) * (1-ratio))
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 沙丘轮廓
        for dune in range(3):
            dune_x = dune * 300
            dune_points = []
            for x in range(dune_x, dune_x + 300, 20):
                dune_height = height - 100 - int(50 * math.sin((x - dune_x) * 0.02))
                dune_points.extend([x, dune_height])
            dune_points.extend([dune_x + 300, height, dune_x, height])
            draw.polygon(dune_points, fill=(210, 180, 140))
        
        # 飞舞的沙粒
        sand_offset = int(progress * 200) % 50
        for particle in range(100):
            particle_x = (particle * 23 + sand_offset) % width
            particle_y = (particle * 17 + int(progress * 100)) % height
            size = random.randint(1, 3)
            draw.ellipse([particle_x, particle_y, particle_x+size, particle_y+size], 
                        fill=(255, 228, 196, 100))
    
    def create_t2v_dataset(self):
        """创建完整的文生视频数据集"""
        print("🎬 创建文生视频(T2V)数据集...")
        
        # 定义多样化的场景配置
        scenes = [
            {
                'name': 'ocean_waves',
                'prompt': 'Beautiful ocean waves gently rolling onto a sandy beach at sunset, peaceful and serene atmosphere, cinematic lighting',
                'colors': [(135, 206, 235), (0, 100, 200), (255, 255, 255)],
                'category': 'nature'
            },
            {
                'name': 'forest_wind',
                'prompt': 'Dense green forest with trees swaying gently in the wind, morning sunlight filtering through leaves, natural beauty',
                'colors': [(34, 139, 34), (144, 238, 144), (255, 255, 224)],
                'category': 'nature'
            },
            {
                'name': 'mountain_clouds',
                'prompt': 'Majestic snow-capped mountains with white clouds drifting across a clear blue sky, dramatic landscape view',
                'colors': [(135, 206, 235), (255, 255, 255), (100, 100, 100)],
                'category': 'landscape'
            },
            {
                'name': 'city_traffic',
                'prompt': 'Bustling city at night with moving traffic lights and illuminated skyscrapers, urban nightlife atmosphere',
                'colors': [(25, 25, 112), (255, 215, 0), (40, 40, 40)],
                'category': 'urban'
            },
            {
                'name': 'flower_garden',
                'prompt': 'Colorful flower garden with blooming flowers swaying in a gentle spring breeze, vibrant and cheerful scene',
                'colors': [(255, 192, 203), (255, 20, 147), (138, 43, 226)],
                'category': 'nature'
            },
            {
                'name': 'desert_sandstorm',
                'prompt': 'Vast desert landscape with golden sand dunes and particles blowing in the wind, dramatic and mystical atmosphere',
                'colors': [(238, 203, 173), (255, 218, 185), (210, 180, 140)],
                'category': 'landscape'
            },
            # 添加更多变化的场景
            {
                'name': 'rain_window',
                'prompt': 'Raindrops falling on a window with blurred city lights in the background, cozy and atmospheric mood',
                'colors': [(70, 70, 70), (100, 100, 100), (255, 255, 255)],
                'category': 'weather'
            },
            {
                'name': 'campfire_night',
                'prompt': 'Warm campfire crackling under a starry night sky, peaceful outdoor camping scene with dancing flames',
                'colors': [(25, 25, 112), (255, 140, 0), (255, 69, 0)],
                'category': 'outdoor'
            },
            {
                'name': 'butterfly_meadow',
                'prompt': 'Butterflies flying over a sunny meadow filled with wildflowers, peaceful summer day with gentle movement',
                'colors': [(144, 238, 144), (255, 255, 0), (255, 20, 147)],
                'category': 'nature'
            },
            {
                'name': 'aurora_night',
                'prompt': 'Northern lights dancing across a dark sky over a snowy landscape, magical and ethereal atmosphere',
                'colors': [(25, 25, 112), (0, 255, 127), (147, 0, 211)],
                'category': 'natural_phenomenon'
            }
        ]
        
        # 为每个场景创建多个变体
        for scene_idx, scene in enumerate(scenes):
            # 创建3个变体（不同的随机种子和参数）
            for variant in range(3):
                print(f"\n🎨 处理场景 {scene_idx+1}/{len(scenes)}, 变体 {variant+1}/3: {scene['name']}")
                
                # 设置随机种子以确保可重现性
                random.seed(scene_idx * 10 + variant)
                np.random.seed(scene_idx * 10 + variant)
                
                # 创建视频文件名
                video_filename = f"{scene['name']}_v{variant+1}_{scene_idx:02d}.mp4"
                final_video_path = self.videos_dir / video_filename
                
                # 创建视频
                temp_video_path = self.create_animated_video(
                    scene, 
                    duration=3.0, 
                    fps=15, 
                    output_path=str(final_video_path)
                )
                
                # 记录数据
                record = {
                    'video': f"videos/{video_filename}",
                    'prompt': scene['prompt'],
                    'category': scene['category'],
                    'scene_name': scene['name'],
                    'variant': variant + 1
                }
                self.dataset_records.append(record)
                
                print(f"   ✅ 视频: {video_filename}")
        
        print(f"\n✅ 创建了 {len(self.dataset_records)} 个视频样本")
        print(f"   场景类型: {len(scenes)} 种")
        print(f"   每种场景: 3 个变体")
    
    def save_metadata(self):
        """保存元数据文件"""
        if not self.dataset_records:
            print("⚠️  没有数据记录，跳过保存")
            return
        
        # 保存训练用的CSV格式
        df = pd.DataFrame(self.dataset_records)
        # 只保留训练需要的列
        training_df = df[['video', 'prompt']].copy()
        training_df.to_csv(self.metadata_file, index=False)
        
        # 保存完整的JSON格式
        json_file = self.output_dir / "metadata_full.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset_records, f, ensure_ascii=False, indent=2)
        
        # 创建统计信息
        categories = {}
        for record in self.dataset_records:
            cat = record['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        stats = {
            'total_samples': len(self.dataset_records),
            'unique_scenes': len(set(record['scene_name'] for record in self.dataset_records)),
            'variants_per_scene': 3,
            'categories': categories,
            'video_specs': {
                'resolution': '832x480',
                'fps': 15,
                'duration_seconds': 3.0,
                'total_frames': 45
            },
            'creation_time': datetime.now().isoformat()
        }
        
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 元数据已保存:")
        print(f"   训练CSV: {self.metadata_file}")
        print(f"   完整JSON: {json_file}")
        print(f"   统计文件: {stats_file}")
    
    def print_summary(self):
        """打印数据集摘要"""
        if not self.dataset_records:
            print("📊 数据集为空")
            return
        
        categories = {}
        for record in self.dataset_records:
            cat = record['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"\n📊 文生视频(T2V)数据集摘要:")
        print(f"   总样本数: {len(self.dataset_records)}")
        print(f"   独特场景: {len(set(record['scene_name'] for record in self.dataset_records))} 种")
        print(f"   每场景变体: 3 个")
        print(f"   视频规格: 832×480, 15fps, 3.0秒, 45帧")
        
        print(f"\n🏷️  场景分类:")
        for category, count in categories.items():
            print(f"   • {category}: {count} 个样本")
        
        print(f"\n🎬 场景列表:")
        unique_scenes = {}
        for record in self.dataset_records:
            scene_name = record['scene_name']
            if scene_name not in unique_scenes:
                unique_scenes[scene_name] = record['prompt']
        
        for scene_name, prompt in unique_scenes.items():
            print(f"   • {scene_name}: {prompt[:60]}...")
        
        print(f"\n📁 文件结构:")
        print(f"   {self.output_dir}/")
        print(f"   ├── metadata.csv          # 训练用CSV文件")
        print(f"   ├── metadata_full.json    # 完整元数据")
        print(f"   ├── dataset_stats.json    # 统计信息")
        print(f"   └── videos/               # 视频文件目录")

def main():
    """主函数"""
    print("🎬 Wan2.1-T2V-1.3B 文生视频数据集创建工具")
    print("=" * 60)
    
    # 创建T2V数据集创建器
    creator = T2VDatasetCreator()
    
    # 创建视频数据集
    creator.create_t2v_dataset()
    
    # 保存元数据
    creator.save_metadata()
    
    # 打印摘要
    creator.print_summary()
    
    print(f"\n🎉 文生视频数据集创建完成!")
    print(f"📁 数据集位置: {creator.output_dir}")
    print(f"📝 训练用CSV: {creator.metadata_file}")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 检查生成的视频: ls -la {creator.videos_dir}")
    print(f"   2. 运行T2V训练脚本")
    print(f"   3. 验证训练结果")

if __name__ == "__main__":
    main()
