#!/bin/bash

# Wan2.1-T2V-1.3B LoRA多卡微调训练脚本
# 时间: 2025-07-22
# GPU: 2x RTX 3090

echo "🚀 开始Wan2.1-T2V-1.3B LoRA多卡微调训练..."
echo "时间: $(date)"
echo "GPU配置: 2x NVIDIA GeForce RTX 3090"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export NCCL_DEBUG=INFO  # 调试NCCL通信

# 训练参数配置
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-T2V-1.3B_lora"
LOG_DIR="./logs/training"

# 创建输出目录
mkdir -p ${OUTPUT_PATH}
mkdir -p ${LOG_DIR}

echo "📁 输出路径: ${OUTPUT_PATH}"
echo "📁 日志路径: ${LOG_DIR}"

# 验证数据集
echo "🔍 验证数据集..."
if [ ! -f "${DATASET_METADATA_PATH}" ]; then
    echo "❌ 数据集元数据文件不存在: ${DATASET_METADATA_PATH}"
    exit 1
fi

echo "✅ 数据集验证通过"

# 验证模型文件
echo "🔍 验证模型文件..."
MODEL_FILES=(
    "models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    "models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth"
    "models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth"
)

for file in "${MODEL_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 模型文件不存在: $file"
        exit 1
    fi
done

echo "✅ 模型文件验证通过"

# 记录训练开始时间
TRAIN_START_TIME=$(date +%s)
echo "⏰ 训练开始时间: $(date)"

# 启动多卡LoRA训练
echo "🚀 启动多卡LoRA训练..."
echo "📊 训练配置:"
echo "  - 数据集: ${DATASET_BASE_PATH}"
echo "  - 输出分辨率: 480x832"
echo "  - 数据重复: 100次"
echo "  - 学习率: 1e-4"
echo "  - 训练轮数: 5"
echo "  - LoRA rank: 32"
echo "  - 目标模块: q,k,v,o,ffn.0,ffn.2"

# 执行训练命令
accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  2>&1 | tee ${LOG_DIR}/training_$(date +%Y%m%d_%H%M%S).log

# 记录训练结束时间
TRAIN_END_TIME=$(date +%s)
TRAIN_DURATION=$((TRAIN_END_TIME - TRAIN_START_TIME))
TRAIN_HOURS=$((TRAIN_DURATION / 3600))
TRAIN_MINUTES=$(((TRAIN_DURATION % 3600) / 60))
TRAIN_SECONDS=$((TRAIN_DURATION % 60))

echo "⏰ 训练结束时间: $(date)"
echo "⏱️  训练总耗时: ${TRAIN_HOURS}小时${TRAIN_MINUTES}分钟${TRAIN_SECONDS}秒"

# 检查训练结果
echo "🔍 检查训练结果..."
if [ -d "${OUTPUT_PATH}" ]; then
    echo "✅ 输出目录存在: ${OUTPUT_PATH}"
    
    # 列出生成的checkpoint文件
    echo "📁 生成的checkpoint文件:"
    ls -la ${OUTPUT_PATH}/*.safetensors 2>/dev/null || echo "⚠️  未找到.safetensors文件"
    
    # 计算输出目录大小
    OUTPUT_SIZE=$(du -sh ${OUTPUT_PATH} | cut -f1)
    echo "📊 输出目录大小: ${OUTPUT_SIZE}"
    
    echo "✅ LoRA训练完成！模型保存在: ${OUTPUT_PATH}"
else
    echo "❌ 输出目录不存在，训练可能失败"
    exit 1
fi

echo "🎉 Wan2.1-T2V-1.3B LoRA多卡微调训练完成！"
