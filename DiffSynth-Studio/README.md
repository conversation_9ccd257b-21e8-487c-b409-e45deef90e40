# Wan2.1-T2V-1.3B LoRA训练完整解决方案

## 🎯 项目简介

本项目提供了Wan2.1-T2V-1.3B视频生成模型的完整LoRA微调解决方案，包含从环境搭建到训练完成的全流程自动化脚本和详细监控系统。

### ✨ 主要特性

- 🚀 **一键启动训练** - 全自动化训练流程
- 📊 **多层次监控** - TensorBoard + 自定义监控系统
- 📝 **详细过程记录** - 从环境搭建到模型输出的完整日志
- 🔧 **智能故障排除** - 常见问题自动检测和解决建议
- 📚 **完整文档体系** - 中英文详细指南

---

## 📚 文档导航

### 🚀 快速开始
- **[快速开始指南.md](快速开始指南.md)** - 5分钟快速启动训练
- **[start_complete_training.sh](start_complete_training.sh)** - 一键启动脚本

### 📖 详细指南
- **[完整训练指南.md](完整训练指南.md)** - 中文完整指南 (推荐)
- **[Complete_Training_Guide.md](Complete_Training_Guide.md)** - English Complete Guide

### 🛠️ 核心脚本
- **[complete_training_pipeline.py](complete_training_pipeline.py)** - 完整训练流水线
- **[demo_complete_pipeline.py](demo_complete_pipeline.py)** - 演示版流水线
- **[real_time_monitor.py](real_time_monitor.py)** - 实时监控脚本

### 📊 监控系统
- **[tensorboard_manager.py](tensorboard_manager.py)** - TensorBoard管理工具
- **[tensorboard_config.py](tensorboard_config.py)** - 监控配置文件

---

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 创建环境
conda create -n wan_video_env python=3.12 -y
conda activate wan_video_env

# 安装依赖
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
pip install accelerate transformers diffusers tensorboard matplotlib psutil opencv-python pillow
pip install -e .
```

### 2. 一键启动训练

```bash
# 启动完整训练流水线
./start_complete_training.sh
```

### 3. 监控训练进度

训练启动后，访问以下TensorBoard界面：

- **完整流水线**: http://localhost:6009
- **实时监控**: http://localhost:6008  
- **基础监控**: http://localhost:6007

---

## 📊 监控系统架构

### 三层监控体系

1. **完整流水线监控** (端口6009)
   - 环境搭建详细记录
   - 模型下载过程追踪
   - 数据集准备步骤
   - LoRA配置详解
   - 训练执行监控

2. **实时系统监控** (端口6008)
   - GPU使用率和显存监控
   - CPU和内存使用情况
   - 系统资源实时统计
   - 训练进程状态追踪

3. **基础训练监控** (端口6007)
   - 训练损失曲线
   - 学习率变化
   - 梯度范数统计
   - 基础性能指标

### 监控内容详解

#### 🔧 环境搭建阶段
- Python、CUDA、PyTorch版本信息
- GPU型号和显存容量
- 依赖包安装过程
- 环境验证结果

#### 📥 模型下载阶段
- Wan2.1-T2V-1.3B模型组件 (扩散模型2.8GB + 文本编码器4.2GB + 视频VAE1.1GB)
- 下载进度和完整性验证
- 存储路径和文件结构

#### 📊 数据集准备阶段
- 数据集目录结构创建
- metadata.csv格式和内容
- 数据预处理配置
- 数据增强参数设置

#### ⚡ 训练配置阶段
- Accelerate多GPU配置详解
- LoRA参数详细说明 (rank=16, 目标模块q,k,v,o,ffn.0,ffn.2)
- 训练超参数设置
- 内存和性能优化配置

#### 🏋️ 训练执行阶段
- 实时损失曲线和收敛情况
- GPU显存使用和利用率
- 训练速度和ETA预估
- 梯度范数和学习率变化

---

## 📁 项目结构

```
DiffSynth-Studio/
├── 📚 文档系统
│   ├── README.md                        # 项目总览 (本文件)
│   ├── 快速开始指南.md                   # 5分钟快速开始
│   ├── 完整训练指南.md                   # 中文完整指南
│   └── Complete_Training_Guide.md       # 英文完整指南
│
├── 🚀 核心脚本
│   ├── start_complete_training.sh       # 一键启动脚本
│   ├── complete_training_pipeline.py    # 完整训练流水线
│   ├── demo_complete_pipeline.py        # 演示版流水线
│   └── real_time_monitor.py            # 实时监控脚本
│
├── 📊 监控系统
│   ├── tensorboard_manager.py          # TensorBoard管理
│   ├── tensorboard_config.py           # 监控配置
│   └── tensorboard_logs/               # TensorBoard日志
│       ├── complete_training_*/        # 完整训练监控
│       ├── real_time_monitor_*/        # 实时系统监控
│       └── wan_lora_real_*/           # 基础训练监控
│
├── 🔧 配置文件
│   ├── accelerate_config.yaml          # Accelerate配置
│   └── environment.yml                 # Conda环境配置
│
├── 📊 数据和模型
│   ├── data/example_video_dataset/     # 示例数据集
│   ├── models/Wan-AI/Wan2.1-T2V-1.3B/ # 基础模型
│   └── models/train/                   # 训练输出
│
└── 📝 日志系统
    └── logs/                           # 详细训练日志
        ├── 01_environment_check_*.log  # 环境检查
        ├── 02_model_check_*.log       # 模型检查
        ├── 07_training_output_*.log   # 训练输出
        └── final_report_*.md          # 最终报告
```

---

## 🎯 核心功能

### 1. 自动化训练流水线

- ✅ **环境检查** - 自动验证Python、CUDA、依赖包
- ✅ **模型下载** - 自动下载和验证Wan2.1-T2V-1.3B模型
- ✅ **数据准备** - 自动创建数据集和元数据文件
- ✅ **配置生成** - 自动生成Accelerate和LoRA配置
- ✅ **训练执行** - 自动启动多GPU分布式训练
- ✅ **结果验证** - 自动检查输出文件和权重
- ✅ **报告生成** - 自动生成详细训练报告

### 2. 多层次监控系统

- 📊 **TensorBoard可视化** - 图形化监控界面
- 📈 **实时指标追踪** - GPU、CPU、内存使用情况
- 📝 **详细日志记录** - 每个步骤的完整日志
- 🔍 **智能故障检测** - 自动识别常见问题
- 📋 **状态报告生成** - 定期生成训练状态报告

### 3. 智能配置管理

- ⚙️ **自适应参数调整** - 根据硬件自动优化参数
- 🔧 **配置文件生成** - 自动生成最优配置
- 📊 **性能监控优化** - 实时调整训练参数
- 🛠️ **故障自动恢复** - 智能处理训练中断

---

## 🔍 技术特点

### LoRA微调技术

- **低秩适应**: 仅训练1.52%的参数 (21.8M/1.3B)
- **高效训练**: 显著减少显存需求和训练时间
- **灵活部署**: 可以快速切换不同的LoRA权重
- **质量保证**: 保持原模型性能的同时实现个性化

### 分布式训练

- **多GPU支持**: 自动配置2卡并行训练
- **混合精度**: BF16精度加速训练
- **梯度累积**: 模拟更大的batch size
- **内存优化**: 梯度检查点节省显存

### 监控系统

- **实时可视化**: TensorBoard多维度监控
- **系统资源**: GPU、CPU、内存实时追踪
- **训练指标**: 损失、学习率、梯度范数
- **性能分析**: 训练速度、ETA预估

---

## 📈 预期结果

### 训练性能

- **训练时间**: 约50分钟 (5 epochs, 250 steps)
- **显存使用**: 每卡约18.9GB/24.6GB (77%利用率)
- **训练速度**: 约12秒/步
- **参数效率**: 仅训练1.52%的参数

### 输出文件

- **LoRA权重**: `pytorch_lora_weights.safetensors` (~87MB)
- **配置文件**: `adapter_config.json`
- **训练日志**: 详细的训练过程记录
- **监控数据**: 完整的TensorBoard日志

### 模型质量

- **收敛稳定**: 损失曲线平滑下降
- **生成质量**: 保持原模型的生成能力
- **个性化**: 针对训练数据的风格适应
- **泛化能力**: 良好的新提示词响应

---

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少LoRA rank到8或4
   - 增加梯度累积步数
   - 启用梯度检查点

2. **模型下载失败**
   - 检查网络连接
   - 使用代理或VPN
   - 重新运行训练脚本

3. **多GPU训练失败**
   - 检查GPU状态
   - 重新配置Accelerate
   - 使用单GPU训练

4. **数据集格式错误**
   - 检查metadata.csv格式
   - 验证视频文件路径
   - 确认文件权限

### 获取帮助

- 📖 查看详细文档: [完整训练指南.md](完整训练指南.md)
- 🔍 检查日志文件: `logs/` 目录
- 📊 查看TensorBoard: http://localhost:6009
- 🐛 提交Issue: GitHub Issues

---

## 🎉 总结

本项目提供了业界最完整的Wan2.1-T2V-1.3B LoRA训练解决方案，具有以下优势：

✅ **完全自动化** - 一键启动，无需手动配置
✅ **详细监控** - 多层次可视化监控系统
✅ **完整文档** - 中英文详细指南和快速开始
✅ **智能故障排除** - 自动检测和解决常见问题
✅ **高效训练** - 优化的LoRA配置和分布式训练
✅ **易于使用** - 简洁的API和清晰的文档结构

**立即开始您的视频生成模型训练之旅**:

```bash
./start_complete_training.sh
```

**查看详细过程**:
- http://localhost:6009 (完整流水线)
- http://localhost:6008 (实时监控)
- http://localhost:6007 (基础监控)

祝您训练成功！🚀
