# Wan2.1-T2V-1.3B LoRA Training Complete Guide

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [Environment Setup](#environment-setup)
3. [Model Download](#model-download)
4. [Dataset Preparation](#dataset-preparation)
5. [Configuration Files](#configuration-files)
6. [TensorBoard Monitoring](#tensorboard-monitoring)
7. [Training Execution](#training-execution)
8. [Result Validation](#result-validation)
9. [Troubleshooting](#troubleshooting)
10. [Appendix](#appendix)

---

## 🎯 Project Overview

### Objective
Fine-tune the Wan2.1-T2V-1.3B video generation model using LoRA (Low-Rank Adaptation) technique for personalized text-to-video generation.

### Tech Stack
- **Base Model**: Wan2.1-T2V-1.3B (1.3B parameter text-to-video diffusion model)
- **Fine-tuning**: LoRA (Low-Rank Adaptation)
- **Training Framework**: PyTorch + Accelerate (Multi-GPU distributed training)
- **Monitoring**: TensorBoard + Custom monitoring scripts
- **Environment**: Conda

### Hardware Requirements
- **GPU**: 2x RTX 3090 (24GB VRAM) or equivalent
- **RAM**: 64GB+ system memory
- **Storage**: 100GB+ available space
- **Network**: Stable internet connection (for model download)

---

## 🔧 Environment Setup

### 1. Create Conda Environment

```bash
# Create Python 3.12 environment
conda create -n wan_video_env python=3.12 -y

# Activate environment
conda activate wan_video_env
```

### 2. Install PyTorch

```bash
# Install PyTorch (CUDA 12.1 version)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
```

### 3. Install Dependencies

```bash
# Core dependencies
pip install accelerate transformers diffusers

# Monitoring and visualization
pip install tensorboard matplotlib psutil

# Image and video processing
pip install opencv-python pillow

# Other utilities
pip install tqdm numpy scipy
```

### 4. Verify Installation

```python
import torch
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU count: {torch.cuda.device_count()}")
for i in range(torch.cuda.device_count()):
    print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
```

### 5. Clone Project Code

```bash
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio
pip install -e .
```

---

## 📥 Model Download

### Model Components

The Wan2.1-T2V-1.3B model consists of three main components:

1. **Diffusion Model Weights** (`diffusion_pytorch_model.safetensors`)
   - Size: ~2.8GB
   - Purpose: Main diffusion transformer model
   - Architecture: DiT (Diffusion Transformer)

2. **Text Encoder** (`models_t5_umt5-xxl-enc-bf16.pth`)
   - Size: ~4.2GB
   - Purpose: T5-XXL text encoder for understanding text prompts
   - Precision: BFloat16

3. **Video VAE** (`Wan2.1_VAE.pth`)
   - Size: ~1.1GB
   - Purpose: Video Variational Autoencoder for video encoding/decoding
   - Supported resolution: 320x576, 576x320

### Automatic Download

Models will be automatically downloaded during training:

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

model_configs = [
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="diffusion_pytorch_model*.safetensors"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="Wan2.1_VAE.pth")
]

pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cpu",
    model_configs=model_configs
)
```

---

## 📊 Dataset Preparation

### Dataset Structure

```
data/example_video_dataset/
├── metadata.csv          # Metadata file (required)
├── videos/               # Video files directory
│   ├── video1.mp4
│   ├── video2.mp4
│   └── ...
└── prompts/              # Prompt files (optional)
    ├── video1.txt
    ├── video2.txt
    └── ...
```

### metadata.csv Format

```csv
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"
video2.mp4,"a cat playing in the garden, sunny day, flowers"
video3.mp4,"city street at night, neon lights, cars passing by"
```

### Video Requirements

- **Format**: MP4, AVI, MOV
- **Resolution**: 320x576 or 576x320 (recommended)
- **Frame Rate**: 8-30 FPS
- **Duration**: 2-10 seconds
- **Encoding**: H.264 (recommended)

---

## ⚙️ Configuration Files

### 1. Accelerate Configuration (accelerate_config.yaml)

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. LoRA Training Parameters

```python
# LoRA configuration
lora_base_model = "dit"                    # Target model
lora_target_modules = "q,k,v,o,ffn.0,ffn.2"  # Target layers
lora_rank = 16                             # Low-rank dimension
lora_alpha = 32                            # Scaling factor
lora_dropout = 0.1                         # Dropout rate

# Training configuration
learning_rate = 1e-4                       # Learning rate
num_epochs = 5                             # Training epochs
batch_size = 1                             # Batch size
gradient_accumulation_steps = 8            # Gradient accumulation
dataset_repeat = 500                       # Dataset repetition
```

---

## 📊 TensorBoard Monitoring

### Monitoring System Architecture

We created a multi-level monitoring system:

1. **Basic Training Monitoring** - Training loss, learning rate, etc.
2. **System Resource Monitoring** - GPU, CPU, memory usage
3. **Complete Pipeline Monitoring** - From environment setup to training completion

### Launch TensorBoard

```bash
# Launch basic monitoring
tensorboard --logdir ./tensorboard_logs/wan_lora_real_* --port 6007

# Launch system monitoring
tensorboard --logdir ./tensorboard_logs/real_time_monitor_* --port 6008

# Launch complete pipeline monitoring
tensorboard --logdir ./tensorboard_logs/complete_demo_* --port 6009
```

### Monitoring Metrics

#### Training Metrics
- **Loss/Train**: Training loss value
- **Learning_Rate**: Learning rate changes
- **Gradient_Norm**: Gradient norm
- **Training/Epoch**: Current training epoch

#### System Resources
- **System/CPU_Usage_%**: CPU usage rate
- **System/Memory_Usage_%**: Memory usage rate
- **GPU/gpu_0/Memory_Allocated_GB**: GPU 0 memory allocation
- **GPU/gpu_1/Memory_Allocated_GB**: GPU 1 memory allocation

#### Performance Metrics
- **Performance/Step_Time_Seconds**: Time per training step
- **Performance/Steps_Per_Second**: Training speed
- **Progress/ETA_Minutes**: Estimated remaining time

### Access URLs

- **Basic Monitoring**: http://localhost:6007
- **System Monitoring**: http://localhost:6008  
- **Complete Pipeline**: http://localhost:6009

---

## 🚀 Training Execution

### Method 1: One-Click Launch Script

```bash
# Use complete training script
./start_complete_training.sh
```

This script automatically executes:
1. Environment check
2. Model file verification
3. Dataset preparation
4. Accelerate configuration
5. TensorBoard launch
6. LoRA training execution
7. Result validation
8. Report generation

### Method 2: Manual Execution

```bash
# Activate environment
conda activate wan_video_env

# Launch training
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 \
  --width 576 \
  --dataset_repeat 500 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-T2V-1.3B_lora" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 16 \
  --use_gradient_checkpointing_offload
```

### Expected Training Time

- **Dataset size**: 1 video × 500 repeats = 500 training samples
- **Training epochs**: 5 epochs
- **Total steps**: ~250 steps (500 samples ÷ 2 GPUs)
- **Time per step**: ~12 seconds
- **Total training time**: ~50 minutes

---

## ✅ Result Validation

### Check Output Files

```bash
# View output directory
ls -la ./models/train/Wan2.1-T2V-1.3B_lora/

# Typical output files
./models/train/Wan2.1-T2V-1.3B_lora/
├── pytorch_lora_weights.safetensors    # LoRA weight file
├── adapter_config.json                 # LoRA configuration
├── training_args.json                  # Training arguments
└── logs/                              # Training logs
```

### Test Inference

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

# Load base model
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda"
)

# Load LoRA weights
pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora")

# Generate video
prompt = "a beautiful sunset over mountains"
video = pipe(
    prompt=prompt,
    height=320,
    width=576,
    num_frames=16,
    num_inference_steps=50
)

# Save video
video.save("output_video.mp4")
```
