{"_valid_processor_keys": ["images", "do_resize", "size", "resample", "do_center_crop", "crop_size", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_convert_rgb", "return_tensors", "data_format", "input_data_format"], "crop_size": {"height": 336, "width": 336}, "do_center_crop": true, "do_convert_rgb": true, "do_normalize": true, "do_rescale": true, "do_resize": true, "image_mean": [0.48145466, 0.4578275, 0.40821073], "image_processor_type": "CLIPImageProcessor", "image_std": [0.26862954, 0.26130258, 0.27577711], "processor_class": "LlavaProcessor", "resample": 3, "rescale_factor": 0.00392156862745098, "size": {"shortest_edge": 336}}