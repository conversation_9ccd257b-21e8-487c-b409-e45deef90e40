{"_name_or_path": "/home/<USER>/t5/mt5-xl", "architectures": ["MT5ForConditionalGeneration"], "d_ff": 5120, "d_kv": 64, "d_model": 2048, "decoder_start_token_id": 0, "dropout_rate": 0.1, "eos_token_id": 1, "feed_forward_proj": "gated-gelu", "initializer_factor": 1.0, "is_encoder_decoder": true, "layer_norm_epsilon": 1e-06, "model_type": "mt5", "num_decoder_layers": 24, "num_heads": 32, "num_layers": 24, "output_past": true, "pad_token_id": 0, "relative_attention_num_buckets": 32, "tie_word_embeddings": false, "tokenizer_class": "T5Tokenizer", "transformers_version": "4.10.0.dev0", "use_cache": true, "vocab_size": 250112}