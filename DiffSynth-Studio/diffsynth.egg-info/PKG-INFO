Metadata-Version: 2.4
Name: diffsynth
Version: 1.1.7
Summary: Enjoy the magic of Diffusion models!
Author: Artiprocher
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
License-File: LICENSE
Requires-Dist: torch>=2.0.0
Requires-Dist: torchvision
Requires-Dist: cupy-cuda12x
Requires-Dist: transformers
Requires-Dist: controlnet-aux==0.0.7
Requires-Dist: imageio
Requires-Dist: imageio[ffmpeg]
Requires-Dist: safetensors
Requires-Dist: einops
Requires-Dist: sentencepiece
Requires-Dist: protobuf
Requires-Dist: modelscope
Requires-Dist: ftfy
Requires-Dist: pynvml
Requires-Dist: pandas
Requires-Dist: accelerate
Dynamic: author
Dynamic: classifier
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
