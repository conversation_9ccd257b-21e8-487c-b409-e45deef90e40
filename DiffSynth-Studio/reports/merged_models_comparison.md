# Wan2.1-T2V-1.3B 合并模型对比报告

## 生成时间
2025-07-22 17:05:11

## 模型概览

### 合并模型统计
- 总模型数量: 6
- LoRA类型: optimized (rank=16), minimal (rank=8)
- Alpha值: 0.5, 1.0, 1.5
- 文件格式: SafeTensors

### 模型列表

#### 优化版模型 (rank=16, 3轮训练)
- `Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors` (Alpha=0.5, 5.29GB)
- `Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors` (Alpha=1.0, 5.29GB)
- `Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors` (Alpha=1.5, 5.29GB)

#### 极限版模型 (rank=8, 1轮训练)
- `Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors` (Alpha=0.5, 5.29GB)
- `Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors` (Alpha=1.0, 5.29GB)
- `Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors` (Alpha=1.5, 5.29GB)


## Alpha值说明

### Alpha=0.5 (保守合并)
- 权重变化: 50%的LoRA影响
- 适用场景: 保持原模型特性，轻微调整
- 推荐用途: 初次测试，风险较低

### Alpha=1.0 (标准合并)
- 权重变化: 100%的LoRA影响
- 适用场景: 完整应用训练效果
- 推荐用途: 正常使用，平衡效果

### Alpha=1.5 (激进合并)
- 权重变化: 150%的LoRA影响
- 适用场景: 强化训练效果
- 推荐用途: 追求最大改变，可能过拟合

## 使用建议

### 推荐配置
1. **质量优先**: `optimized_alpha_1.0` - 最佳平衡
2. **速度优先**: `minimal_alpha_1.0` - 快速推理
3. **保守选择**: `optimized_alpha_0.5` - 稳定效果
4. **激进选择**: `optimized_alpha_1.5` - 最大变化

### 测试顺序
1. 先测试 `optimized_alpha_1.0`
2. 根据效果调整alpha值
3. 对比minimal版本的速度优势
4. 选择最适合的配置

## 技术细节

### 合并算法
```
merged_weight = base_weight + (lora_B @ lora_A) * alpha
```

### 文件结构
- 基础模型: 825个权重张量
- 优化版LoRA: 240个层对 (q,k,v,o)
- 极限版LoRA: 120个层对 (q,k)

### 存储信息
- 单个模型大小: 5.29GB
- 总存储需求: 31.7GB
- 格式: SafeTensors (安全可靠)
