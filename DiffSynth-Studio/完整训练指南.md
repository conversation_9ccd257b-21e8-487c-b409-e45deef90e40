# Wan2.1-T2V-1.3B LoRA训练完整指南

## 📋 目录

1. [项目概述](#项目概述)
2. [环境搭建](#环境搭建)
3. [模型下载](#模型下载)
4. [数据集准备](#数据集准备)
5. [配置文件](#配置文件)
6. [TensorBoard监控](#tensorboard监控)
7. [训练执行](#训练执行)
8. [结果验证](#结果验证)
9. [故障排除](#故障排除)
10. [附录](#附录)

---

## 🎯 项目概述

### 项目目标
使用LoRA (Low-Rank Adaptation) 技术对Wan2.1-T2V-1.3B视频生成模型进行微调，实现个性化的文本到视频生成。

### 技术栈
- **基础模型**: Wan2.1-T2V-1.3B (1.3B参数的文本到视频扩散模型)
- **微调技术**: LoRA (Low-Rank Adaptation)
- **训练框架**: PyTorch + Accelerate (多GPU分布式训练)
- **监控工具**: TensorBoard + 自定义监控脚本
- **环境管理**: Conda

### 硬件要求
- **GPU**: 2x RTX 3090 (24GB显存) 或同等性能
- **内存**: 64GB+ 系统内存
- **存储**: 100GB+ 可用空间
- **网络**: 稳定的互联网连接（用于模型下载）

---

## 🔧 环境搭建

### 1. 创建Conda环境

```bash
# 创建Python 3.12环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env
```

### 2. 安装PyTorch

```bash
# 安装PyTorch (CUDA 12.1版本)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
```

### 3. 安装依赖包

```bash
# 核心依赖
pip install accelerate transformers diffusers

# 监控和可视化
pip install tensorboard matplotlib psutil

# 图像和视频处理
pip install opencv-python pillow

# 其他工具
pip install tqdm numpy scipy
```

### 4. 验证安装

```python
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
for i in range(torch.cuda.device_count()):
    print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
```

### 5. 克隆项目代码

```bash
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio
pip install -e .
```

---

## 📥 模型下载

### 模型组件说明

Wan2.1-T2V-1.3B模型包含三个主要组件：

1. **扩散模型权重** (`diffusion_pytorch_model.safetensors`)
   - 大小: ~2.8GB
   - 用途: 主要的扩散变换器模型
   - 架构: DiT (Diffusion Transformer)

2. **文本编码器** (`models_t5_umt5-xxl-enc-bf16.pth`)
   - 大小: ~4.2GB
   - 用途: T5-XXL文本编码器，理解文本提示
   - 精度: BFloat16

3. **视频VAE** (`Wan2.1_VAE.pth`)
   - 大小: ~1.1GB
   - 用途: 视频变分自编码器，编码/解码视频
   - 支持分辨率: 320x576, 576x320

### 自动下载方式

模型会在训练时自动下载，无需手动操作：

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

model_configs = [
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="diffusion_pytorch_model*.safetensors"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="Wan2.1_VAE.pth")
]

pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cpu",
    model_configs=model_configs
)
```

### 存储位置

```
./models/Wan-AI/Wan2.1-T2V-1.3B/
├── diffusion_pytorch_model.safetensors  # 扩散模型
├── models_t5_umt5-xxl-enc-bf16.pth     # 文本编码器
├── Wan2.1_VAE.pth                      # 视频VAE
└── config.json                         # 配置文件
```

---

## 📊 数据集准备

### 数据集结构

```
data/example_video_dataset/
├── metadata.csv          # 元数据文件（必需）
├── videos/               # 视频文件目录
│   ├── video1.mp4
│   ├── video2.mp4
│   └── ...
└── prompts/              # 提示词文件（可选）
    ├── video1.txt
    ├── video2.txt
    └── ...
```

### metadata.csv 格式

```csv
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"
video2.mp4,"a cat playing in the garden, sunny day, flowers"
video3.mp4,"city street at night, neon lights, cars passing by"
```

### 视频要求

- **格式**: MP4, AVI, MOV
- **分辨率**: 320x576 或 576x320 (推荐)
- **帧率**: 8-30 FPS
- **时长**: 2-10秒
- **编码**: H.264 (推荐)

### 文本提示词要求

- **长度**: 10-100个词
- **语言**: 英文（推荐）
- **内容**: 详细描述视频内容、风格、场景
- **格式**: 简洁明了，避免过于复杂的语法

### 创建示例数据集

```bash
# 创建数据集目录
mkdir -p data/example_video_dataset

# 创建元数据文件
cat > data/example_video_dataset/metadata.csv << EOF
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"
EOF
```

---

## ⚙️ 配置文件

### 1. Accelerate配置 (accelerate_config.yaml)

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. LoRA训练参数

```python
# LoRA配置
lora_base_model = "dit"                    # 目标模型
lora_target_modules = "q,k,v,o,ffn.0,ffn.2"  # 目标层
lora_rank = 16                             # 低秩维度
lora_alpha = 32                            # 缩放因子
lora_dropout = 0.1                         # Dropout率

# 训练配置
learning_rate = 1e-4                       # 学习率
num_epochs = 5                             # 训练轮数
batch_size = 1                             # 批大小
gradient_accumulation_steps = 8            # 梯度累积
dataset_repeat = 500                       # 数据重复次数
```

### 3. 视频参数

```python
height = 320                               # 视频高度
width = 576                                # 视频宽度
num_frames = 16                            # 帧数
fps = 8                                    # 帧率
```

---

## 📊 TensorBoard监控

### 监控系统架构

我们创建了多层次的监控系统：

1. **基础训练监控** - 训练损失、学习率等
2. **系统资源监控** - GPU、CPU、内存使用
3. **完整流水线监控** - 从环境搭建到训练完成

### 启动TensorBoard

```bash
# 启动基础监控
tensorboard --logdir ./tensorboard_logs/wan_lora_real_* --port 6007

# 启动系统监控
tensorboard --logdir ./tensorboard_logs/real_time_monitor_* --port 6008

# 启动完整流水线监控
tensorboard --logdir ./tensorboard_logs/complete_demo_* --port 6009
```

### 监控指标说明

#### 训练指标
- **Loss/Train**: 训练损失值
- **Learning_Rate**: 学习率变化
- **Gradient_Norm**: 梯度范数
- **Training/Epoch**: 当前训练轮次

#### 系统资源
- **System/CPU_Usage_%**: CPU使用率
- **System/Memory_Usage_%**: 内存使用率
- **GPU/gpu_0/Memory_Allocated_GB**: GPU 0显存分配
- **GPU/gpu_1/Memory_Allocated_GB**: GPU 1显存分配
- **GPU/gpu_0/Memory_Utilization_%**: GPU 0显存利用率

#### 性能指标
- **Performance/Step_Time_Seconds**: 每步训练时间
- **Performance/Steps_Per_Second**: 训练速度
- **Progress/ETA_Minutes**: 预计剩余时间

### 访问地址

- **基础监控**: http://localhost:6007
- **系统监控**: http://localhost:6008  
- **完整流水线**: http://localhost:6009

---

## 🚀 训练执行

### 方法1: 一键启动脚本

```bash
# 使用完整的训练脚本
./start_complete_training.sh
```

这个脚本会自动执行：
1. 环境检查
2. 模型文件验证
3. 数据集准备
4. Accelerate配置
5. TensorBoard启动
6. LoRA训练执行
7. 结果验证
8. 报告生成

### 方法2: 手动执行

```bash
# 激活环境
conda activate wan_video_env

# 启动训练
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 \
  --width 576 \
  --dataset_repeat 500 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-T2V-1.3B_lora" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 16 \
  --use_gradient_checkpointing_offload
```

### 训练过程监控

训练过程中可以通过以下方式监控：

1. **终端输出**: 实时查看训练进度
2. **TensorBoard**: 图形化监控界面
3. **系统监控**: GPU使用情况
4. **日志文件**: 详细的训练日志

### 预期训练时间

- **数据集大小**: 1个视频 × 500重复 = 500个训练样本
- **训练轮数**: 5 epochs
- **总步数**: 约250步 (500样本 ÷ 2GPU)
- **每步时间**: 约12秒
- **总训练时间**: 约50分钟

---

## ✅ 结果验证

### 检查输出文件

```bash
# 查看输出目录
ls -la ./models/train/Wan2.1-T2V-1.3B_lora/

# 典型输出文件
./models/train/Wan2.1-T2V-1.3B_lora/
├── pytorch_lora_weights.safetensors    # LoRA权重文件
├── adapter_config.json                 # LoRA配置
├── training_args.json                  # 训练参数
└── logs/                              # 训练日志
```

### 验证LoRA权重

```python
import torch
from safetensors import safe_open

# 加载LoRA权重
lora_path = "./models/train/Wan2.1-T2V-1.3B_lora/pytorch_lora_weights.safetensors"

with safe_open(lora_path, framework="pt", device="cpu") as f:
    for key in f.keys():
        tensor = f.get_tensor(key)
        print(f"{key}: {tensor.shape}")
```

### 测试推理

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

# 加载基础模型
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda"
)

# 加载LoRA权重
pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora")

# 生成视频
prompt = "a beautiful sunset over mountains"
video = pipe(
    prompt=prompt,
    height=320,
    width=576,
    num_frames=16,
    num_inference_steps=50
)

# 保存视频
video.save("output_video.mp4")
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. CUDA内存不足 (OOM)

**错误信息**: `RuntimeError: CUDA out of memory`

**解决方案**:
```bash
# 减少batch_size
--batch_size 1

# 增加梯度累积步数
--gradient_accumulation_steps 16

# 启用梯度检查点
--use_gradient_checkpointing_offload

# 使用更小的LoRA rank
--lora_rank 8
```

#### 2. 模型下载失败

**错误信息**: `Connection timeout` 或 `Download failed`

**解决方案**:
```bash
# 设置代理（如果需要）
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port

# 手动下载模型文件
wget https://modelscope.cn/models/Wan-AI/Wan2.1-T2V-1.3B/resolve/master/diffusion_pytorch_model.safetensors

# 或使用git lfs
git lfs clone https://www.modelscope.cn/Wan-AI/Wan2.1-T2V-1.3B.git
```

#### 3. 多GPU训练失败

**错误信息**: `NCCL error` 或 `Distributed training failed`

**解决方案**:
```bash
# 检查GPU状态
nvidia-smi

# 重新配置Accelerate
accelerate config

# 使用单GPU训练
num_processes: 1
```

#### 4. 数据集加载错误

**错误信息**: `FileNotFoundError` 或 `Invalid dataset format`

**解决方案**:
```bash
# 检查数据集路径
ls -la data/example_video_dataset/

# 验证metadata.csv格式
head data/example_video_dataset/metadata.csv

# 检查视频文件
ffprobe data/example_video_dataset/video1.mp4
```

#### 5. TensorBoard无法访问

**错误信息**: `Connection refused` 或页面无法加载

**解决方案**:
```bash
# 检查TensorBoard进程
ps aux | grep tensorboard

# 重启TensorBoard
pkill tensorboard
tensorboard --logdir ./tensorboard_logs --port 6006 --host 0.0.0.0

# 检查防火墙设置
sudo ufw allow 6006
```

### 性能优化建议

#### 1. 内存优化
- 使用混合精度训练 (BF16)
- 启用梯度检查点
- 适当调整batch_size和梯度累积

#### 2. 速度优化
- 使用多GPU训练
- 优化数据加载器
- 使用编译优化

#### 3. 质量优化
- 调整学习率调度
- 增加训练轮数
- 优化LoRA参数

---

## 📚 附录

### A. 完整的文件结构

```
DiffSynth-Studio/
├── data/                                    # 数据集目录
│   └── example_video_dataset/
│       ├── metadata.csv
│       └── videos/
├── models/                                  # 模型目录
│   ├── Wan-AI/Wan2.1-T2V-1.3B/            # 基础模型
│   └── train/Wan2.1-T2V-1.3B_lora/        # 训练输出
├── logs/                                    # 日志目录
│   ├── 01_environment_check_*.log
│   ├── 02_model_check_*.log
│   ├── 07_training_output_*.log
│   └── final_report_*.md
├── tensorboard_logs/                        # TensorBoard日志
│   ├── wan_lora_real_*/
│   ├── real_time_monitor_*/
│   └── complete_demo_*/
├── accelerate_config.yaml                   # Accelerate配置
├── start_complete_training.sh               # 一键启动脚本
├── complete_training_pipeline.py            # 完整流水线脚本
├── demo_complete_pipeline.py                # 演示脚本
├── real_time_monitor.py                     # 实时监控脚本
└── 完整训练指南.md                          # 本文档
```

### B. 重要命令速查

```bash
# 环境管理
conda activate wan_video_env
conda deactivate

# 训练相关
./start_complete_training.sh                # 一键启动
accelerate launch --config_file ...         # 手动启动

# 监控相关
tensorboard --logdir ./tensorboard_logs     # 启动TensorBoard
nvidia-smi                                   # 查看GPU状态
ps aux | grep python                         # 查看训练进程

# 文件操作
ls -la ./models/train/                       # 查看输出
tail -f logs/07_training_output_*.log        # 实时查看日志
```

### C. 参数调优指南

#### LoRA参数
- **rank**: 4-64，越大模型容量越大，但训练越慢
- **alpha**: 通常设为rank的2倍
- **target_modules**: 根据模型架构选择关键层

#### 训练参数
- **learning_rate**: 1e-5 到 1e-3，LoRA通常用较小值
- **batch_size**: 受显存限制，通常1-4
- **gradient_accumulation_steps**: 用于模拟更大的batch_size

#### 数据参数
- **dataset_repeat**: 小数据集可以设置较大值
- **height/width**: 必须是模型支持的分辨率

### D. 相关资源链接

- **项目主页**: https://github.com/modelscope/DiffSynth-Studio
- **模型页面**: https://modelscope.cn/models/Wan-AI/Wan2.1-T2V-1.3B
- **LoRA论文**: https://arxiv.org/abs/2106.09685
- **Accelerate文档**: https://huggingface.co/docs/accelerate
- **TensorBoard指南**: https://www.tensorflow.org/tensorboard

### E. 版本信息

- **文档版本**: v1.0
- **创建日期**: 2025-07-28
- **适用模型**: Wan2.1-T2V-1.3B
- **Python版本**: 3.12+
- **PyTorch版本**: 2.0+
- **CUDA版本**: 12.1+

---

## 🎉 总结

本指南提供了Wan2.1-T2V-1.3B LoRA训练的完整流程，包括：

1. **详细的环境搭建步骤** - 从Conda环境到依赖安装
2. **完整的模型下载指南** - 自动下载和手动下载方式
3. **数据集准备详解** - 格式要求和创建方法
4. **多层次的监控系统** - TensorBoard + 自定义监控
5. **一键启动脚本** - 自动化整个训练流程
6. **详细的故障排除** - 常见问题和解决方案

通过本指南，您可以：
- ✅ 快速搭建训练环境
- ✅ 理解每个步骤的详细过程
- ✅ 实时监控训练状态
- ✅ 解决常见问题
- ✅ 优化训练性能

**开始训练只需一条命令**:
```bash
./start_complete_training.sh
```

**查看详细过程**:
- 访问 http://localhost:6009 查看完整流水线
- 访问 http://localhost:6008 查看实时监控
- 查看 `logs/` 目录下的详细日志文件

祝您训练顺利！🚀
