#!/usr/bin/env python3
"""
简化的LoRA推理测试脚本
验证LoRA权重加载和基本功能
"""

import os
import torch
from safetensors.torch import load_file
import time

def test_lora_weights():
    """测试LoRA权重文件"""
    
    print("=== LoRA权重文件测试 ===")
    
    # LoRA文件路径
    lora_files = [
        ("optimized", "./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors"),
        ("minimal", "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors")
    ]
    
    for lora_name, lora_path in lora_files:
        print(f"\n{'='*50}")
        print(f"🧪 测试LoRA: {lora_name}")
        print(f"📄 文件路径: {lora_path}")
        
        if not os.path.exists(lora_path):
            print(f"❌ 文件不存在: {lora_path}")
            continue
        
        try:
            # 获取文件大小
            file_size = os.path.getsize(lora_path) / (1024*1024)  # MB
            print(f"📊 文件大小: {file_size:.1f}MB")
            
            # 加载LoRA权重
            print("📥 加载LoRA权重...")
            lora_weights = load_file(lora_path)
            
            # 分析权重
            print(f"📊 权重统计:")
            print(f"  - 权重数量: {len(lora_weights)}")
            
            # 统计不同类型的权重
            weight_types = {}
            total_params = 0
            
            for key, tensor in lora_weights.items():
                # 提取权重类型
                if 'lora_A' in key:
                    weight_type = 'lora_A'
                elif 'lora_B' in key:
                    weight_type = 'lora_B'
                else:
                    weight_type = 'other'
                
                if weight_type not in weight_types:
                    weight_types[weight_type] = 0
                weight_types[weight_type] += 1
                
                # 统计参数数量
                total_params += tensor.numel()
                
                # 显示前几个权重的信息
                if len(weight_types) <= 6:  # 只显示前几个
                    print(f"    {key}: {tensor.shape} ({tensor.dtype})")
            
            print(f"  - 权重类型分布: {weight_types}")
            print(f"  - 总参数数量: {total_params:,}")
            
            # 检查权重范围
            all_values = []
            for tensor in lora_weights.values():
                all_values.extend(tensor.flatten().tolist()[:1000])  # 采样1000个值
            
            if all_values:
                min_val = min(all_values)
                max_val = max(all_values)
                avg_val = sum(all_values) / len(all_values)
                print(f"  - 权重范围: [{min_val:.6f}, {max_val:.6f}]")
                print(f"  - 平均值: {avg_val:.6f}")
            
            print(f"✅ {lora_name} LoRA权重加载成功")
            
        except Exception as e:
            print(f"❌ {lora_name} LoRA权重加载失败: {str(e)}")
            continue
    
    return True

def test_model_loading():
    """测试基础模型加载"""
    
    print(f"\n{'='*50}")
    print("🧪 测试基础模型加载")
    
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    # 检查模型文件
    model_files = [
        "diffusion_pytorch_model.safetensors",
        "models_t5_umt5-xxl-enc-bf16.pth", 
        "Wan2.1_VAE.pth"
    ]
    
    print("📋 检查模型文件:")
    for file in model_files:
        file_path = os.path.join(base_model_path, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024**3)  # GB
            print(f"  ✅ {file}: {size:.2f}GB")
        else:
            print(f"  ❌ {file}: 不存在")
            return False
    
    # 检查GPU显存
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_free = memory_total - memory_allocated
            print(f"  📊 GPU {i}: {memory_free:.1f}GB 可用 / {memory_total:.1f}GB 总计")
    
    print("✅ 基础模型文件检查完成")
    return True

def create_inference_script():
    """创建简化的推理脚本"""
    
    print(f"\n{'='*50}")
    print("📝 创建简化推理脚本")
    
    script_content = '''#!/usr/bin/env python3
"""
简化的视频生成脚本
使用训练好的LoRA权重生成视频
"""

import torch
from diffsynth import ModelManager, WanVideoPipeline
import os

def generate_video_with_lora():
    """使用LoRA生成视频"""
    
    # 清理显存
    torch.cuda.empty_cache()
    
    # 配置
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    lora_path = "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors"
    output_path = "./outputs/lora_test_video.mp4"
    
    os.makedirs("./outputs", exist_ok=True)
    
    try:
        print("📥 加载模型...")
        model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cuda:0")
        
        # 只加载必要的模型
        model_manager.load_models([
            f"{base_model_path}/diffusion_pytorch_model.safetensors",
            f"{base_model_path}/models_t5_umt5-xxl-enc-bf16.pth", 
            f"{base_model_path}/Wan2.1_VAE.pth"
        ])
        
        print("📥 加载LoRA...")
        model_manager.load_lora(lora_path, lora_alpha=1.0)
        
        print("🔧 创建管道...")
        pipe = WanVideoPipeline.from_model_manager(model_manager)
        
        print("🎬 生成视频...")
        video = pipe(
            prompt="from sunset to night, a small town, light, house, river",
            height=256,
            width=448,
            num_frames=8,  # 更少帧数
            num_inference_steps=10  # 更少步数
        )
        
        print("💾 保存视频...")
        video.save(output_path, fps=4)
        
        file_size = os.path.getsize(output_path) / (1024*1024)
        print(f"✅ 视频已保存: {output_path} ({file_size:.1f}MB)")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        return False
    finally:
        # 清理显存
        if 'pipe' in locals():
            del pipe
        if 'model_manager' in locals():
            del model_manager
        torch.cuda.empty_cache()

if __name__ == "__main__":
    generate_video_with_lora()
'''
    
    script_path = "./simple_inference.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 推理脚本已创建: {script_path}")
    return script_path

def main():
    """主函数"""
    
    print("🚀 开始LoRA简化测试...")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试LoRA权重
    test_lora_weights()
    
    # 测试模型文件
    test_model_loading()
    
    # 创建推理脚本
    create_inference_script()
    
    print(f"\n{'='*50}")
    print("📋 测试总结:")
    print("✅ LoRA权重文件验证完成")
    print("✅ 基础模型文件检查完成") 
    print("✅ 简化推理脚本已创建")
    print("\n💡 下一步可以运行: python simple_inference.py")
    
    print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
