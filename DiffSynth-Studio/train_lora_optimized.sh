#!/bin/bash

# Wan2.1-T2V-1.3B LoRA多卡微调训练脚本 (显存优化版)
# 时间: 2025-07-22
# GPU: 2x RTX 3090
# 优化: 降低分辨率、减小batch size、启用梯度检查点

echo "🚀 开始Wan2.1-T2V-1.3B LoRA多卡微调训练 (显存优化版)..."
echo "时间: $(date)"
echo "GPU配置: 2x NVIDIA GeForce RTX 3090"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True  # 减少显存碎片
export NCCL_DEBUG=WARN  # 减少NCCL日志

# 训练参数配置 (显存优化)
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-T2V-1.3B_lora_optimized"
LOG_DIR="./logs/training"

# 创建输出目录
mkdir -p ${OUTPUT_PATH}
mkdir -p ${LOG_DIR}

echo "📁 输出路径: ${OUTPUT_PATH}"
echo "📁 日志路径: ${LOG_DIR}"

# 清理GPU显存
echo "🧹 清理GPU显存..."
python -c "
import torch
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
    print('✅ GPU显存已清理')
else:
    print('❌ CUDA不可用')
"

# 验证数据集
echo "🔍 验证数据集..."
if [ ! -f "${DATASET_METADATA_PATH}" ]; then
    echo "❌ 数据集元数据文件不存在: ${DATASET_METADATA_PATH}"
    exit 1
fi

echo "✅ 数据集验证通过"

# 验证模型文件
echo "🔍 验证模型文件..."
MODEL_FILES=(
    "models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    "models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth"
    "models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth"
)

for file in "${MODEL_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 模型文件不存在: $file"
        exit 1
    fi
done

echo "✅ 模型文件验证通过"

# 记录训练开始时间
TRAIN_START_TIME=$(date +%s)
echo "⏰ 训练开始时间: $(date)"

# 启动多卡LoRA训练 (显存优化配置)
echo "🚀 启动多卡LoRA训练 (显存优化)..."
echo "📊 优化配置:"
echo "  - 数据集: ${DATASET_BASE_PATH}"
echo "  - 输出分辨率: 320x576 (降低分辨率节省显存)"
echo "  - 数据重复: 50次 (减少重复)"
echo "  - 学习率: 5e-5 (降低学习率)"
echo "  - 训练轮数: 3 (减少轮数)"
echo "  - LoRA rank: 16 (降低rank节省显存)"
echo "  - 目标模块: q,k,v,o (减少目标模块)"
echo "  - 梯度累积: 启用"
echo "  - 显存优化: 启用"

# 执行训练命令 (显存优化版)
accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 320 \
  --width 576 \
  --dataset_repeat 50 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 5e-5 \
  --num_epochs 3 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o" \
  --lora_rank 16 \
  2>&1 | tee ${LOG_DIR}/training_optimized_$(date +%Y%m%d_%H%M%S).log

# 记录训练结束时间
TRAIN_END_TIME=$(date +%s)
TRAIN_DURATION=$((TRAIN_END_TIME - TRAIN_START_TIME))
TRAIN_HOURS=$((TRAIN_DURATION / 3600))
TRAIN_MINUTES=$(((TRAIN_DURATION % 3600) / 60))
TRAIN_SECONDS=$((TRAIN_DURATION % 60))

echo "⏰ 训练结束时间: $(date)"
echo "⏱️  训练总耗时: ${TRAIN_HOURS}小时${TRAIN_MINUTES}分钟${TRAIN_SECONDS}秒"

# 检查训练结果
echo "🔍 检查训练结果..."
if [ -d "${OUTPUT_PATH}" ]; then
    echo "✅ 输出目录存在: ${OUTPUT_PATH}"
    
    # 列出生成的checkpoint文件
    echo "📁 生成的checkpoint文件:"
    if ls ${OUTPUT_PATH}/*.safetensors 1> /dev/null 2>&1; then
        ls -la ${OUTPUT_PATH}/*.safetensors
        echo "✅ 找到checkpoint文件"
    else
        echo "⚠️  未找到.safetensors文件"
    fi
    
    # 计算输出目录大小
    OUTPUT_SIZE=$(du -sh ${OUTPUT_PATH} | cut -f1)
    echo "📊 输出目录大小: ${OUTPUT_SIZE}"
    
    # 检查是否有有效的checkpoint
    if ls ${OUTPUT_PATH}/*.safetensors 1> /dev/null 2>&1; then
        echo "✅ LoRA训练成功完成！模型保存在: ${OUTPUT_PATH}"
        
        # 显示最新的checkpoint信息
        LATEST_CHECKPOINT=$(ls -t ${OUTPUT_PATH}/*.safetensors | head -1)
        CHECKPOINT_SIZE=$(du -sh "${LATEST_CHECKPOINT}" | cut -f1)
        echo "📄 最新checkpoint: $(basename ${LATEST_CHECKPOINT}) (${CHECKPOINT_SIZE})"
    else
        echo "❌ 训练失败，未生成有效的checkpoint文件"
        exit 1
    fi
else
    echo "❌ 输出目录不存在，训练失败"
    exit 1
fi

echo "🎉 Wan2.1-T2V-1.3B LoRA多卡微调训练完成！"

# 显示GPU显存使用情况
echo "📊 当前GPU显存使用情况:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits
