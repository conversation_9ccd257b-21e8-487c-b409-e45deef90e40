#!/usr/bin/env python3
"""
Accelerate多卡配置分析和优化建议
"""

import torch
import yaml
import os
from pathlib import Path

def analyze_hardware():
    """分析硬件配置"""
    
    print("=== 硬件配置分析 ===")
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✅ CUDA可用")
        print(f"📊 GPU数量: {gpu_count}")
        
        total_memory = 0
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            total_memory += gpu_memory
            print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        print(f"📊 总GPU显存: {total_memory:.1f} GB")
        
        # 计算理论训练能力
        if gpu_count == 2 and "RTX 3090" in torch.cuda.get_device_name(0):
            print(f"\n💡 RTX 3090双卡配置优势:")
            print(f"  - 单卡显存: 24GB")
            print(f"  - 总显存: 48GB")
            print(f"  - 显存带宽: 936 GB/s × 2")
            print(f"  - CUDA核心: 10496 × 2")
            print(f"  - 适合大模型训练")
        
        return gpu_count, total_memory
    else:
        print("❌ CUDA不可用")
        return 0, 0

def analyze_accelerate_config(config_path="accelerate_config.yaml"):
    """分析Accelerate配置"""
    
    print(f"\n=== Accelerate配置分析 ===")
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print(f"📋 配置文件: {config_path}")
    print(f"📊 配置内容:")
    
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 配置验证和建议
    print(f"\n🔍 配置验证:")
    
    # 检查分布式类型
    if config.get('distributed_type') == 'MULTI_GPU':
        print(f"  ✅ 分布式类型: MULTI_GPU (正确)")
    else:
        print(f"  ⚠️  分布式类型: {config.get('distributed_type')} (建议MULTI_GPU)")
    
    # 检查混合精度
    if config.get('mixed_precision') == 'bf16':
        print(f"  ✅ 混合精度: bf16 (推荐，RTX 3090支持)")
    elif config.get('mixed_precision') == 'fp16':
        print(f"  ✅ 混合精度: fp16 (可用)")
    else:
        print(f"  ⚠️  混合精度: {config.get('mixed_precision')} (建议bf16)")
    
    # 检查进程数
    num_processes = config.get('num_processes', 1)
    gpu_count = torch.cuda.device_count()
    if num_processes == gpu_count:
        print(f"  ✅ 进程数: {num_processes} (匹配GPU数量)")
    else:
        print(f"  ⚠️  进程数: {num_processes} (GPU数量: {gpu_count}，建议匹配)")
    
    # 检查GPU ID
    gpu_ids = config.get('gpu_ids', 'all')
    if gpu_ids == 'all':
        print(f"  ✅ GPU ID: all (使用所有GPU)")
    else:
        print(f"  📊 GPU ID: {gpu_ids}")
    
    return config

def estimate_training_performance(gpu_count, total_memory, config):
    """估算训练性能"""
    
    print(f"\n=== 训练性能估算 ===")
    
    # 模型大小估算
    model_size_gb = 16.34  # Wan2.1-T2V-1.3B模型大小
    print(f"📊 基础模型大小: {model_size_gb:.1f} GB")
    
    # LoRA参数估算
    lora_rank = 32  # 默认LoRA rank
    lora_params = estimate_lora_params(lora_rank)
    lora_size_gb = lora_params * 4 / (1024**3)  # float32
    print(f"📊 LoRA参数量: {lora_params:,} ({lora_size_gb:.2f} GB)")
    
    # 显存需求估算
    per_gpu_memory = total_memory / gpu_count
    
    # 训练时显存需求 (模型 + 梯度 + 优化器状态 + 激活值)
    training_memory_per_gpu = model_size_gb / gpu_count + lora_size_gb * 3  # 梯度+优化器
    activation_memory = 4.0  # 估算激活值显存
    total_memory_per_gpu = training_memory_per_gpu + activation_memory
    
    print(f"📊 显存需求分析 (每GPU):")
    print(f"  - 模型分片: {model_size_gb/gpu_count:.1f} GB")
    print(f"  - LoRA参数: {lora_size_gb:.2f} GB")
    print(f"  - 梯度缓存: {lora_size_gb:.2f} GB")
    print(f"  - 优化器状态: {lora_size_gb:.2f} GB")
    print(f"  - 激活值: {activation_memory:.1f} GB")
    print(f"  - 总需求: {total_memory_per_gpu:.1f} GB")
    print(f"  - 可用显存: {per_gpu_memory:.1f} GB")
    
    if total_memory_per_gpu < per_gpu_memory * 0.8:  # 留20%余量
        print(f"  ✅ 显存充足 (利用率: {total_memory_per_gpu/per_gpu_memory*100:.1f}%)")
        memory_ok = True
    else:
        print(f"  ⚠️  显存紧张 (利用率: {total_memory_per_gpu/per_gpu_memory*100:.1f}%)")
        memory_ok = False
    
    # 训练速度估算
    if gpu_count == 2:
        speedup = 1.8  # 考虑通信开销
        print(f"📊 双卡训练加速比: {speedup:.1f}x")
    else:
        speedup = gpu_count * 0.9
        print(f"📊 多卡训练加速比: {speedup:.1f}x")
    
    return memory_ok, speedup

def estimate_lora_params(rank=32):
    """估算LoRA参数量"""
    
    # Wan2.1-T2V-1.3B DiT模型的注意力层参数估算
    # 假设有多个Transformer层，每层有q,k,v,o,ffn.0,ffn.2
    
    num_layers = 28  # 估算层数
    hidden_dim = 1152  # 估算隐藏维度
    
    # 每层LoRA参数: (q,k,v,o) + (ffn.0,ffn.2)
    attention_params = 4 * (hidden_dim * rank + rank * hidden_dim)  # q,k,v,o
    ffn_params = 2 * (hidden_dim * rank + rank * hidden_dim * 4)    # ffn.0,ffn.2
    
    params_per_layer = attention_params + ffn_params
    total_params = num_layers * params_per_layer
    
    return total_params

def generate_optimization_suggestions(gpu_count, memory_ok, config):
    """生成优化建议"""
    
    print(f"\n=== 优化建议 ===")
    
    print(f"🚀 多卡训练优化策略:")
    
    # 数据并行优化
    print(f"📊 数据并行优化:")
    print(f"  - 使用DDP (DistributedDataParallel)")
    print(f"  - 每GPU独立处理batch")
    print(f"  - 梯度同步优化")
    
    # 显存优化
    if not memory_ok:
        print(f"⚠️  显存优化建议:")
        print(f"  - 减小batch size")
        print(f"  - 启用gradient checkpointing")
        print(f"  - 使用DeepSpeed ZeRO")
        print(f"  - 降低LoRA rank")
    else:
        print(f"✅ 显存优化:")
        print(f"  - 可适当增加batch size")
        print(f"  - 启用混合精度训练")
    
    # 通信优化
    print(f"🔗 通信优化:")
    print(f"  - 使用NCCL后端")
    print(f"  - 梯度压缩")
    print(f"  - 重叠计算和通信")
    
    # 训练参数建议
    print(f"⚙️  训练参数建议:")
    batch_size_per_gpu = 2 if memory_ok else 1
    total_batch_size = batch_size_per_gpu * gpu_count
    print(f"  - batch_size_per_gpu: {batch_size_per_gpu}")
    print(f"  - total_batch_size: {total_batch_size}")
    print(f"  - learning_rate: 1e-4")
    print(f"  - mixed_precision: bf16")
    print(f"  - gradient_accumulation_steps: 2")

def main():
    """主函数"""
    
    print("=== Wan2.1-T2V-1.3B Accelerate多卡配置分析 ===")
    
    # 硬件分析
    gpu_count, total_memory = analyze_hardware()
    
    if gpu_count == 0:
        print("❌ 无可用GPU，无法进行多卡训练")
        return
    
    # 配置分析
    config = analyze_accelerate_config()
    
    if config is None:
        print("❌ 配置文件分析失败")
        return
    
    # 性能估算
    memory_ok, speedup = estimate_training_performance(gpu_count, total_memory, config)
    
    # 优化建议
    generate_optimization_suggestions(gpu_count, memory_ok, config)
    
    # 总结
    print(f"\n=== 配置总结 ===")
    print(f"✅ GPU数量: {gpu_count}")
    print(f"✅ 总显存: {total_memory:.1f} GB")
    print(f"✅ 预期加速比: {speedup:.1f}x")
    print(f"✅ 显存状态: {'充足' if memory_ok else '紧张'}")
    print(f"✅ 配置状态: 已优化")

if __name__ == "__main__":
    main()
