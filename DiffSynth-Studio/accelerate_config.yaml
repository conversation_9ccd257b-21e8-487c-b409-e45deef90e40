compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all                    # 使用所有GPU，或指定如 "0,1"
machine_rank: 0
main_training_function: main
mixed_precision: bf16           # 推荐使用bf16混合精度
num_machines: 1
num_processes: 2                # 根据GPU数量调整：2卡=2，4卡=4，8卡=8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
