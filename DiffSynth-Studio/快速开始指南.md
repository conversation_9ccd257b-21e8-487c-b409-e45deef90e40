# Wan2.1-T2V-1.3B LoRA训练 - 快速开始指南

## 🚀 5分钟快速开始

### 前提条件
- ✅ 2x RTX 3090 (24GB显存) 或同等GPU
- ✅ 64GB+ 系统内存
- ✅ 100GB+ 可用存储空间
- ✅ 稳定的网络连接

### 一键启动训练

```bash
# 1. 克隆项目
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 2. 创建环境
conda create -n wan_video_env python=3.12 -y
conda activate wan_video_env

# 3. 安装依赖
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
pip install accelerate transformers diffusers tensorboard matplotlib psutil opencv-python pillow
pip install -e .

# 4. 一键启动训练
./start_complete_training.sh
```

### 监控训练进度

训练启动后，访问以下地址查看详细监控：

- **完整流水线**: http://localhost:6009 - 查看从环境搭建到训练完成的全过程
- **实时监控**: http://localhost:6008 - 查看GPU、CPU、内存实时使用情况  
- **基础监控**: http://localhost:6007 - 查看训练损失、学习率等基础指标

### 预期结果

- **训练时间**: 约50分钟
- **输出位置**: `./models/train/Wan2.1-T2V-1.3B_lora/`
- **日志位置**: `./logs/`
- **TensorBoard**: `./tensorboard_logs/`

---

## 📊 详细监控内容

### 1. 完整流水线监控 (端口6009)

在TensorBoard的**TEXT**页面查看：

#### 🔧 环境搭建详细记录
- 系统环境信息 (Python、CUDA、GPU型号)
- 依赖包安装步骤
- 环境验证结果

#### 📥 模型下载详细过程  
- Wan2.1-T2V-1.3B模型组件说明
- 各文件大小和用途 (扩散模型2.8GB + 文本编码器4.2GB + 视频VAE1.1GB)
- 下载命令和存储路径

#### 📊 数据集准备详细过程
- 数据集目录结构
- metadata.csv格式说明
- 数据预处理步骤
- 数据增强配置

#### ⚡ Accelerate多GPU配置详解
- 配置文件完整内容
- 各参数详细说明 (双GPU、混合精度、梯度同步)
- GPU内存分配策略

#### 🎯 LoRA微调配置详解
- LoRA原理和数学公式
- 训练参数详细说明 (rank=16, 目标模块q,k,v,o,ffn.0,ffn.2)
- 参数量对比 (21.8M可训练参数 vs 1.3B总参数)
- 内存使用估算

### 2. 实时系统监控 (端口6008)

在TensorBoard的**SCALARS**页面查看：

#### 系统资源监控
- `System/CPU_Usage_%` - CPU使用率
- `System/Memory_Usage_%` - 内存使用率
- `System/Memory_Used_GB` - 内存使用量

#### GPU详细监控
- `GPU/gpu_0/Memory_Allocated_GB` - GPU 0显存分配
- `GPU/gpu_1/Memory_Allocated_GB` - GPU 1显存分配
- `GPU/gpu_0/Memory_Utilization_%` - GPU 0显存利用率
- `GPU/gpu_1/Memory_Utilization_%` - GPU 1显存利用率

#### 状态报告
- `Status/System_Monitor` - 每30秒更新的系统状态报告
- 训练进程PID信息
- 实时时间戳

### 3. 基础训练监控 (端口6007)

#### 训练指标
- `Loss/Train` - 训练损失曲线
- `Learning_Rate` - 学习率变化
- `Gradient_Norm` - 梯度范数
- `Memory/GPU_*/Allocated_GB` - GPU显存使用

---

## 🔍 训练过程详解

### 训练流程8个步骤

1. **环境检查** (5秒)
   - 检查Python、CUDA、依赖包
   - 验证GPU可用性和数量

2. **模型下载** (2分钟)
   - 自动下载Wan2.1-T2V-1.3B模型文件
   - 验证文件完整性

3. **数据准备** (10秒)
   - 创建示例数据集
   - 生成metadata.csv文件

4. **配置设置** (8秒)
   - 创建Accelerate配置文件
   - 设置LoRA参数

5. **训练初始化** (30秒)
   - 加载模型和优化器
   - 初始化分布式训练

6. **训练执行** (50分钟)
   - 执行5个epoch的LoRA微调
   - 实时记录训练指标

7. **结果验证** (15秒)
   - 检查输出文件
   - 验证LoRA权重

8. **报告生成** (5秒)
   - 生成训练报告
   - 保存日志文件

### 关键参数说明

```python
# LoRA配置
lora_rank = 16                    # 低秩维度，影响模型容量
lora_target_modules = "q,k,v,o,ffn.0,ffn.2"  # 目标注意力和前馈层
learning_rate = 1e-4              # 学习率，LoRA通常用较小值

# 训练配置  
num_epochs = 5                    # 训练轮数
batch_size = 1                    # 批大小，受显存限制
gradient_accumulation_steps = 8   # 梯度累积，等效batch_size=8
dataset_repeat = 500              # 数据重复，小数据集常用

# 视频配置
height = 320                      # 视频高度
width = 576                       # 视频宽度
num_frames = 16                   # 帧数
```

---

## 📁 输出文件说明

### 训练完成后的文件结构

```
./models/train/Wan2.1-T2V-1.3B_lora/
├── pytorch_lora_weights.safetensors    # LoRA权重文件 (~87MB)
├── adapter_config.json                 # LoRA配置文件
├── training_args.json                  # 训练参数记录
└── logs/                              # 详细训练日志

./logs/
├── 01_environment_check_*.log          # 环境检查日志
├── 02_model_check_*.log               # 模型检查日志
├── 07_training_output_*.log           # 训练输出日志
└── final_report_*.md                  # 最终报告

./tensorboard_logs/
├── complete_training_*/               # 完整训练监控
├── real_time_monitor_*/              # 实时系统监控
└── wan_lora_real_*/                  # 基础训练监控
```

---

## 🧪 测试训练结果

### 快速推理测试

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

# 加载基础模型
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda"
)

# 加载训练好的LoRA权重
pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora")

# 生成测试视频
prompt = "from sunset to night, a small town, light, house, river"
video = pipe(
    prompt=prompt,
    height=320,
    width=576,
    num_frames=16,
    num_inference_steps=50
)

# 保存视频
video.save("test_output.mp4")
print("✅ 测试视频已生成: test_output.mp4")
```

---

## ❓ 常见问题

### Q: 训练过程中GPU显存不足怎么办？
A: 可以尝试以下方法：
- 减少`--lora_rank`到8或4
- 增加`--gradient_accumulation_steps`到16
- 确保启用了`--use_gradient_checkpointing_offload`

### Q: 模型下载失败怎么办？
A: 模型会在训练时自动下载，如果网络问题导致下载失败，可以：
- 检查网络连接
- 重新运行训练脚本，会自动重试下载
- 使用代理或VPN

### Q: 如何查看详细的训练进度？
A: 有多种方式：
- 访问TensorBoard界面查看图形化监控
- 查看终端输出的实时日志
- 查看`logs/`目录下的详细日志文件

### Q: 训练完成后如何使用模型？
A: 使用上面的推理测试代码，或者：
- 合并LoRA权重到基础模型
- 部署为API服务
- 集成到其他应用中

---

## 🎉 总结

通过本快速指南，您可以：

✅ **5分钟内启动训练** - 一键脚本自动化所有步骤
✅ **实时监控训练过程** - 多层次TensorBoard可视化
✅ **了解每个步骤详情** - 从环境搭建到模型输出
✅ **快速验证结果** - 简单的推理测试代码

**立即开始**:
```bash
./start_complete_training.sh
```

**查看监控**:
- http://localhost:6009 (完整流水线)
- http://localhost:6008 (实时监控)  
- http://localhost:6007 (基础监控)

祝您训练成功！🚀
