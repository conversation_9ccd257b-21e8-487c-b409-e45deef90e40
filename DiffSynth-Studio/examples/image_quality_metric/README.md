# Image Quality Metric

The image quality assessment functionality has been integrated into Diffsynth. We support the following models:

* [ImageReward](https://github.com/THUDM/ImageReward)
* [Aesthetic](https://github.com/christo<PERSON><PERSON><PERSON>/improved-aesthetic-predictor)
* [PickScore](https://github.com/yuvalkirstain/pickscore)
* [CLIP](https://github.com/openai/CLIP)
* [HPSv2](https://github.com/tgxs002/HPSv2)
* [HPSv2.1](https://github.com/tgxs002/HPSv2)
* [MPS](https://github.com/Kwai-Kolors/MPS)

## Usage

See [`./image_quality_evaluation.py`](./image_quality_evaluation.py) for more details.
