ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
Height and width are fixed. Setting `dynamic_resolution` to False.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:58:02,014 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
2025-07-22 14:58:02,083 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:58:05,528 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
2025-07-22 14:58:06,114 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
2025-07-22 14:58:12,696 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:58:13,751 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
2025-07-22 14:58:14,320 - modelscope - INFO - Target directory already exists, skipping creation.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
2025-07-22 14:58:16,063 - modelscope - INFO - Target directory already exists, skipping creation.
NCCL version 2.26.2+cuda12.2

  0%|          | 0/25 [00:00<?, ?it/s]
  0%|          | 0/25 [00:00<?, ?it/s]
  4%|▍         | 1/25 [00:14<05:41, 14.24s/it]
  4%|▍         | 1/25 [00:14<05:41, 14.25s/it]
  8%|▊         | 2/25 [00:25<04:40, 12.20s/it]
  8%|▊         | 2/25 [00:25<04:40, 12.21s/it]
 12%|█▏        | 3/25 [00:35<04:14, 11.55s/it]
 12%|█▏        | 3/25 [00:35<04:14, 11.58s/it]
 16%|█▌        | 4/25 [00:46<03:55, 11.23s/it]
 16%|█▌        | 4/25 [00:46<03:56, 11.25s/it]
 20%|██        | 5/25 [00:57<03:40, 11.05s/it]
 20%|██        | 5/25 [00:57<03:41, 11.06s/it]
 24%|██▍       | 6/25 [01:08<03:28, 10.96s/it]
 24%|██▍       | 6/25 [01:08<03:28, 10.96s/it]
 28%|██▊       | 7/25 [01:18<03:16, 10.91s/it]
 28%|██▊       | 7/25 [01:18<03:16, 10.92s/it]
 32%|███▏      | 8/25 [01:29<03:04, 10.88s/it]
 32%|███▏      | 8/25 [01:29<03:05, 10.89s/it]
 36%|███▌      | 9/25 [01:40<02:53, 10.87s/it]
 36%|███▌      | 9/25 [01:40<02:53, 10.87s/it]
 40%|████      | 10/25 [01:51<02:42, 10.86s/it]
 40%|████      | 10/25 [01:51<02:43, 10.87s/it]
 44%|████▍     | 11/25 [02:02<02:31, 10.85s/it]
 44%|████▍     | 11/25 [02:02<02:32, 10.86s/it]
 48%|████▊     | 12/25 [02:13<02:21, 10.85s/it]
 48%|████▊     | 12/25 [02:13<02:21, 10.88s/it]
 52%|█████▏    | 13/25 [02:23<02:10, 10.84s/it]
 52%|█████▏    | 13/25 [02:24<02:10, 10.87s/it]
 56%|█████▌    | 14/25 [02:34<01:59, 10.84s/it]
 56%|█████▌    | 14/25 [02:34<01:59, 10.86s/it]
 60%|██████    | 15/25 [02:45<01:48, 10.85s/it]
 60%|██████    | 15/25 [02:45<01:48, 10.86s/it]
 64%|██████▍   | 16/25 [02:56<01:37, 10.83s/it]
 64%|██████▍   | 16/25 [02:56<01:37, 10.86s/it]
 68%|██████▊   | 17/25 [03:07<01:26, 10.84s/it]
 68%|██████▊   | 17/25 [03:07<01:26, 10.86s/it]
 72%|███████▏  | 18/25 [03:18<01:15, 10.85s/it]
 72%|███████▏  | 18/25 [03:18<01:15, 10.85s/it]
 76%|███████▌  | 19/25 [03:28<01:05, 10.85s/it]
 76%|███████▌  | 19/25 [03:29<01:05, 10.87s/it]
 80%|████████  | 20/25 [03:39<00:54, 10.85s/it]
 80%|████████  | 20/25 [03:40<00:54, 10.86s/it]
 84%|████████▍ | 21/25 [03:50<00:43, 10.86s/it]
 84%|████████▍ | 21/25 [03:50<00:43, 10.86s/it]
 88%|████████▊ | 22/25 [04:01<00:32, 10.85s/it]
 88%|████████▊ | 22/25 [04:01<00:32, 10.85s/it]
 92%|█████████▏| 23/25 [04:12<00:21, 10.84s/it]
 92%|█████████▏| 23/25 [04:12<00:21, 10.87s/it]
 96%|█████████▌| 24/25 [04:23<00:10, 10.86s/it]
 96%|█████████▌| 24/25 [04:23<00:10, 10.87s/it]
100%|██████████| 25/25 [04:32<00:00, 10.28s/it]
100%|██████████| 25/25 [04:32<00:00, 10.89s/it]
/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once

100%|██████████| 25/25 [04:32<00:00, 10.24s/it]
100%|██████████| 25/25 [04:32<00:00, 10.89s/it]
/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once

  0%|          | 0/25 [00:00<?, ?it/s][2025-07-22 15:02:53,944] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-22 15:02:55,520] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False

  0%|          | 0/25 [00:00<?, ?it/s]
  4%|▍         | 1/25 [00:13<05:15, 13.13s/it]
  4%|▍         | 1/25 [00:15<06:02, 15.09s/it]
  8%|▊         | 2/25 [00:23<04:30, 11.77s/it]
  8%|▊         | 2/25 [00:25<04:49, 12.59s/it]
 12%|█▏        | 3/25 [00:34<04:09, 11.33s/it]
 12%|█▏        | 3/25 [00:36<04:19, 11.79s/it]
 16%|█▌        | 4/25 [00:45<03:53, 11.12s/it]
 16%|█▌        | 4/25 [00:47<03:59, 11.41s/it]
 20%|██        | 5/25 [00:56<03:40, 11.05s/it]
 20%|██        | 5/25 [00:58<03:44, 11.21s/it]
 24%|██▍       | 6/25 [01:07<03:28, 10.98s/it]
 24%|██▍       | 6/25 [01:09<03:30, 11.07s/it]
 28%|██▊       | 7/25 [01:18<03:16, 10.93s/it]
 28%|██▊       | 7/25 [01:20<03:18, 11.00s/it]
 32%|███▏      | 8/25 [01:28<03:05, 10.90s/it]
 32%|███▏      | 8/25 [01:30<03:06, 10.95s/it]
 36%|███▌      | 9/25 [01:39<02:54, 10.88s/it]
 36%|███▌      | 9/25 [01:41<02:54, 10.89s/it]
 40%|████      | 10/25 [01:50<02:42, 10.86s/it]
 40%|████      | 10/25 [01:52<02:43, 10.87s/it]
 44%|████▍     | 11/25 [02:01<02:32, 10.86s/it]
 44%|████▍     | 11/25 [02:03<02:32, 10.86s/it]
 48%|████▊     | 12/25 [02:12<02:21, 10.85s/it]
 48%|████▊     | 12/25 [02:14<02:20, 10.84s/it]
 52%|█████▏    | 13/25 [02:23<02:10, 10.86s/it]
 52%|█████▏    | 13/25 [02:24<02:09, 10.82s/it]
 56%|█████▌    | 14/25 [02:33<01:59, 10.82s/it]
 56%|█████▌    | 14/25 [02:35<01:59, 10.84s/it]
 60%|██████    | 15/25 [02:44<01:48, 10.83s/it]
 60%|██████    | 15/25 [02:46<01:48, 10.84s/it]
 64%|██████▍   | 16/25 [02:55<01:37, 10.84s/it]
 64%|██████▍   | 16/25 [02:57<01:37, 10.83s/it]
 68%|██████▊   | 17/25 [03:06<01:26, 10.84s/it]
 68%|██████▊   | 17/25 [03:08<01:26, 10.85s/it]
 72%|███████▏  | 18/25 [03:17<01:15, 10.85s/it]
 72%|███████▏  | 18/25 [03:19<01:15, 10.83s/it]
 76%|███████▌  | 19/25 [03:29<01:04, 10.83s/it]
 76%|███████▌  | 19/25 [03:28<01:05, 10.86s/it]
 80%|████████  | 20/25 [03:40<00:54, 10.83s/it]
 80%|████████  | 20/25 [03:39<00:54, 10.86s/it]
 84%|████████▍ | 21/25 [03:49<00:43, 10.86s/it]
 84%|████████▍ | 21/25 [03:51<00:43, 10.86s/it]
 88%|████████▊ | 22/25 [04:02<00:32, 10.85s/it]
 88%|████████▊ | 22/25 [04:00<00:32, 10.87s/it]
 92%|█████████▏| 23/25 [04:11<00:21, 10.85s/it]
 92%|█████████▏| 23/25 [04:13<00:21, 10.86s/it]
 96%|█████████▌| 24/25 [04:22<00:10, 10.84s/it]
 96%|█████████▌| 24/25 [04:24<00:10, 10.84s/it]
100%|██████████| 25/25 [04:32<00:00, 10.20s/it]
100%|██████████| 25/25 [04:32<00:00, 10.92s/it]

100%|██████████| 25/25 [04:31<00:00, 10.23s/it]
100%|██████████| 25/25 [04:31<00:00, 10.85s/it]
/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once

  0%|          | 0/25 [00:00<?, ?it/s]
  0%|          | 0/25 [00:00<?, ?it/s]
  4%|▍         | 1/25 [00:13<05:15, 13.15s/it]
  4%|▍         | 1/25 [00:13<05:18, 13.28s/it]
  8%|▊         | 2/25 [00:24<04:31, 11.80s/it]
  8%|▊         | 2/25 [00:24<04:32, 11.85s/it]
 12%|█▏        | 3/25 [00:34<04:10, 11.37s/it]
 12%|█▏        | 3/25 [00:34<04:10, 11.39s/it]
 16%|█▌        | 4/25 [00:45<03:54, 11.16s/it]
 16%|█▌        | 4/25 [00:45<03:55, 11.19s/it]
 20%|██        | 5/25 [00:56<03:41, 11.06s/it]
 20%|██        | 5/25 [00:56<03:41, 11.06s/it]
 24%|██▍       | 6/25 [01:07<03:28, 10.99s/it]
 24%|██▍       | 6/25 [01:07<03:28, 10.99s/it]
 28%|██▊       | 7/25 [01:18<03:17, 10.95s/it]
 28%|██▊       | 7/25 [01:18<03:17, 10.96s/it]
 32%|███▏      | 8/25 [01:29<03:05, 10.93s/it]
 32%|███▏      | 8/25 [01:29<03:05, 10.94s/it]
 36%|███▌      | 9/25 [01:40<02:54, 10.91s/it]
 36%|███▌      | 9/25 [01:40<02:54, 10.92s/it]
 40%|████      | 10/25 [01:50<02:43, 10.91s/it]
 40%|████      | 10/25 [01:51<02:43, 10.90s/it]
 44%|████▍     | 11/25 [02:01<02:32, 10.89s/it]
 44%|████▍     | 11/25 [02:01<02:32, 10.92s/it]
 48%|████▊     | 12/25 [02:12<02:21, 10.89s/it]
 48%|████▊     | 12/25 [02:12<02:21, 10.90s/it]
 52%|█████▏    | 13/25 [02:23<02:10, 10.86s/it]
 52%|█████▏    | 13/25 [02:23<02:10, 10.90s/it]
 56%|█████▌    | 14/25 [02:34<01:59, 10.85s/it]
 56%|█████▌    | 14/25 [02:34<01:59, 10.88s/it]
 60%|██████    | 15/25 [02:45<01:48, 10.85s/it]
 60%|██████    | 15/25 [02:45<01:48, 10.89s/it]
 64%|██████▍   | 16/25 [02:56<01:37, 10.86s/it]
 64%|██████▍   | 16/25 [02:56<01:37, 10.87s/it]
 68%|██████▊   | 17/25 [03:07<01:26, 10.86s/it]
 68%|██████▊   | 17/25 [03:07<01:26, 10.87s/it]
 72%|███████▏  | 18/25 [03:17<01:15, 10.85s/it]
 72%|███████▏  | 18/25 [03:17<01:15, 10.85s/it]
 76%|███████▌  | 19/25 [03:28<01:05, 10.85s/it]
 76%|███████▌  | 19/25 [03:28<01:05, 10.85s/it]
 80%|████████  | 20/25 [03:39<00:54, 10.86s/it]
 80%|████████  | 20/25 [03:39<00:54, 10.86s/it]
 84%|████████▍ | 21/25 [03:50<00:43, 10.86s/it]
 84%|████████▍ | 21/25 [03:50<00:43, 10.87s/it]
 88%|████████▊ | 22/25 [04:01<00:32, 10.87s/it]
 88%|████████▊ | 22/25 [04:01<00:32, 10.86s/it]
 92%|█████████▏| 23/25 [04:12<00:21, 10.87s/it]
 92%|█████████▏| 23/25 [04:12<00:21, 10.86s/it]
 96%|█████████▌| 24/25 [04:23<00:10, 10.86s/it]
 96%|█████████▌| 24/25 [04:23<00:10, 10.86s/it]
100%|██████████| 25/25 [04:31<00:00, 10.24s/it]
100%|██████████| 25/25 [04:31<00:00, 10.88s/it]

100%|██████████| 25/25 [04:31<00:00, 10.23s/it]
100%|██████████| 25/25 [04:31<00:00, 10.87s/it]
