ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
Height and width are fixed. Setting `dynamic_resolution` to False.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:51:43,752 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
2025-07-22 14:51:43,766 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:51:47,156 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
2025-07-22 14:51:47,565 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:51:54,777 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
2025-07-22 14:51:55,165 - modelscope - INFO - Target directory already exists, skipping creation.
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
2025-07-22 14:51:56,374 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 14:51:56,888 - modelscope - INFO - Target directory already exists, skipping creation.
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
ffb29306e477:9425:9425 [0] NCCL INFO Bootstrap: Using eth0:**********<0>
ffb29306e477:9425:9425 [0] NCCL INFO cudaDriverVersion 12020
ffb29306e477:9425:9425 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
ffb29306e477:9425:9425 [0] NCCL INFO Comm config Blocking set to 1
ffb29306e477:9426:9426 [1] NCCL INFO cudaDriverVersion 12020
ffb29306e477:9426:9426 [1] NCCL INFO Bootstrap: Using eth0:**********<0>
ffb29306e477:9426:9426 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
ffb29306e477:9426:9426 [1] NCCL INFO Comm config Blocking set to 1
ffb29306e477:9425:9559 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
ffb29306e477:9425:9559 [0] NCCL INFO NET/IB : No device found.
ffb29306e477:9425:9559 [0] NCCL INFO NET/IB : Using [RO]; OOB eth0:**********<0>
ffb29306e477:9425:9559 [0] NCCL INFO NET/Socket : Using [0]eth0:**********<0>
ffb29306e477:9425:9559 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
ffb29306e477:9425:9559 [0] NCCL INFO Using network Socket
ffb29306e477:9425:9559 [0] NCCL INFO ncclCommInitRankConfig comm 0x60570600 rank 0 nranks 2 cudaDev 0 nvmlDev 0 busId 65000 commId 0xc7c761ed55dbf03d - Init START
ffb29306e477:9426:9560 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
ffb29306e477:9426:9560 [1] NCCL INFO NET/IB : No device found.
ffb29306e477:9426:9560 [1] NCCL INFO NET/IB : Using [RO]; OOB eth0:**********<0>
ffb29306e477:9426:9560 [1] NCCL INFO NET/Socket : Using [0]eth0:**********<0>
ffb29306e477:9426:9560 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
ffb29306e477:9426:9560 [1] NCCL INFO Using network Socket
ffb29306e477:9426:9560 [1] NCCL INFO ncclCommInitRankConfig comm 0x5eff0d40 rank 1 nranks 2 cudaDev 1 nvmlDev 1 busId b1000 commId 0xc7c761ed55dbf03d - Init START
ffb29306e477:9426:9560 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
ffb29306e477:9425:9559 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
ffb29306e477:9425:9559 [0] NCCL INFO Bootstrap timings total 0.007857 (create 0.000025, send 0.000080, recv 0.007283, ring 0.000024, delay 0.000000)
ffb29306e477:9426:9560 [1] NCCL INFO Bootstrap timings total 0.000870 (create 0.000023, send 0.000077, recv 0.000413, ring 0.000022, delay 0.000000)
ffb29306e477:9425:9559 [0] NCCL INFO Setting affinity for GPU 0 to 0fffff,ff000000,0fffffff
ffb29306e477:9426:9560 [1] NCCL INFO Setting affinity for GPU 1 to ffff,fff00000,00ffffff,f0000000
ffb29306e477:9426:9560 [1] NCCL INFO comm 0x5eff0d40 rank 1 nRanks 2 nNodes 1 localRanks 2 localRank 1 MNNVL 0
ffb29306e477:9425:9559 [0] NCCL INFO comm 0x60570600 rank 0 nRanks 2 nNodes 1 localRanks 2 localRank 0 MNNVL 0
ffb29306e477:9426:9560 [1] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] -1/-1/-1->1->0
ffb29306e477:9426:9560 [1] NCCL INFO P2P Chunksize set to 131072
ffb29306e477:9425:9559 [0] NCCL INFO Channel 00/02 : 0 1
ffb29306e477:9425:9559 [0] NCCL INFO Channel 01/02 : 0 1
ffb29306e477:9425:9559 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1
ffb29306e477:9425:9559 [0] NCCL INFO P2P Chunksize set to 131072
ffb29306e477:9425:9559 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 0 directMode 0
ffb29306e477:9426:9565 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 88
ffb29306e477:9425:9564 [0] NCCL INFO [Proxy Service] Device 0 CPU core 2
ffb29306e477:9426:9563 [1] NCCL INFO [Proxy Service] Device 1 CPU core 87
ffb29306e477:9425:9566 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 59
ffb29306e477:9426:9560 [1] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
ffb29306e477:9426:9560 [1] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
ffb29306e477:9425:9559 [0] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
ffb29306e477:9425:9559 [0] NCCL INFO 2 coll channels, 2 collnet channels, 0 nvls channels, 2 p2p channels, 2 p2p channels per peer
ffb29306e477:9425:9559 [0] NCCL INFO CC Off, workFifoBytes 1048576
ffb29306e477:9426:9560 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
ffb29306e477:9425:9559 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
ffb29306e477:9426:9560 [1] NCCL INFO ncclCommInitRankConfig comm 0x5eff0d40 rank 1 nranks 2 cudaDev 1 nvmlDev 1 busId b1000 commId 0xc7c761ed55dbf03d - Init COMPLETE
ffb29306e477:9425:9559 [0] NCCL INFO ncclCommInitRankConfig comm 0x60570600 rank 0 nranks 2 cudaDev 0 nvmlDev 0 busId 65000 commId 0xc7c761ed55dbf03d - Init COMPLETE
ffb29306e477:9426:9560 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 2 total 0.18 (kernels 0.16, alloc 0.00, bootstrap 0.00, allgathers 0.00, topo 0.02, graphs 0.00, connections 0.00, rest 0.00)
ffb29306e477:9425:9559 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 2 total 0.19 (kernels 0.16, alloc 0.00, bootstrap 0.01, allgathers 0.00, topo 0.01, graphs 0.00, connections 0.00, rest 0.00)
ffb29306e477:9426:9567 [1] NCCL INFO Channel 00 : 1[1] -> 0[0] via SHM/direct/direct
ffb29306e477:9426:9567 [1] NCCL INFO Channel 01 : 1[1] -> 0[0] via SHM/direct/direct
ffb29306e477:9425:9568 [0] NCCL INFO Channel 00 : 0[0] -> 1[1] via SHM/direct/direct
ffb29306e477:9425:9568 [0] NCCL INFO Channel 01 : 0[0] -> 1[1] via SHM/direct/direct
ffb29306e477:9425:9568 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
ffb29306e477:9426:9567 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1

  0%|          | 0/50 [00:00<?, ?it/s]
  0%|          | 0/50 [00:00<?, ?it/s]
  0%|          | 0/50 [00:16<?, ?it/s]
[rank0]: Traceback (most recent call last):
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/examples/wanvideo/model_training/train.py", line 116, in <module>
[rank0]:     launch_training_task(
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/trainers/utils.py", line 392, in launch_training_task
[rank0]:     loss = model(data)
[rank0]:            ^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/utils/operations.py", line 818, in forward
[rank0]:     return model_forward(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/utils/operations.py", line 806, in __call__
[rank0]:     return convert_to_fp32(self.model_forward(*args, **kwargs))
[rank0]:                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/amp/autocast_mode.py", line 44, in decorate_autocast
[rank0]:     return func(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/examples/wanvideo/model_training/train.py", line 92, in forward
[rank0]:     loss = self.pipe.training_loss(**models, **inputs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/pipelines/wan_video_new.py", line 267, in training_loss
[rank0]:     noise_pred = self.model_fn(**inputs, timestep=timestep)
[rank0]:                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/pipelines/wan_video_new.py", line 1183, in model_fn_wan_video
[rank0]:     x = torch.utils.checkpoint.checkpoint(
[rank0]:         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/_compile.py", line 51, in inner
[rank0]:     return disable_fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/utils/checkpoint.py", line 495, in checkpoint
[rank0]:     ret = function(*args, **kwargs)
[rank0]:           ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/pipelines/wan_video_new.py", line 1171, in custom_forward
[rank0]:     return module(*inputs)
[rank0]:            ^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/models/wan_video_dit.py", line 222, in forward
[rank0]:     x = self.gate(x, gate_mlp, self.ffn(input_x))
[rank0]:                                ^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/container.py", line 240, in forward
[rank0]:     input = module(input)
[rank0]:             ^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/peft/tuners/lora/layer.py", line 769, in forward
[rank0]:     result = result + lora_B(lora_A(dropout(x))) * scaling
[rank0]:                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~
[rank0]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 560.00 MiB. GPU 0 has a total capacity of 23.69 GiB of which 398.81 MiB is free. Process 3054511 has 23.29 GiB memory in use. Of the allocated memory 20.05 GiB is allocated by PyTorch, and 2.83 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

  0%|          | 0/50 [00:17<?, ?it/s]
[rank1]: Traceback (most recent call last):
[rank1]:   File "/root/sj-tmp/DiffSynth-Studio/examples/wanvideo/model_training/train.py", line 116, in <module>
[rank1]:     launch_training_task(
[rank1]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/trainers/utils.py", line 393, in launch_training_task
[rank1]:     accelerator.backward(loss)
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/accelerator.py", line 2578, in backward
[rank1]:     loss.backward(**kwargs)
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/_tensor.py", line 648, in backward
[rank1]:     torch.autograd.backward(
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/autograd/__init__.py", line 353, in backward
[rank1]:     _engine_run_backward(
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
[rank1]:     return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/utils/checkpoint.py", line 1124, in unpack_hook
[rank1]:     frame.recompute_fn(*args)
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/utils/checkpoint.py", line 1518, in recompute_fn
[rank1]:     fn(*args, **kwargs)
[rank1]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/pipelines/wan_video_new.py", line 1171, in custom_forward
[rank1]:     return module(*inputs)
[rank1]:            ^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/sj-tmp/DiffSynth-Studio/diffsynth/models/wan_video_dit.py", line 222, in forward
[rank1]:     x = self.gate(x, gate_mlp, self.ffn(input_x))
[rank1]:                                ^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/container.py", line 240, in forward
[rank1]:     input = module(input)
[rank1]:             ^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/peft/tuners/lora/layer.py", line 755, in forward
[rank1]:     result = self.base_layer(x, *args, **kwargs)
[rank1]:              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]:   File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/linear.py", line 125, in forward
[rank1]:     return F.linear(input, self.weight, self.bias)
[rank1]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank1]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 560.00 MiB. GPU 1 has a total capacity of 23.69 GiB of which 448.81 MiB is free. Process 3054512 has 23.25 GiB memory in use. Of the allocated memory 21.12 GiB is allocated by PyTorch, and 1.70 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank0]:[W722 14:52:23.663380011 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
ffb29306e477:9425:10247 [0] NCCL INFO misc/socket.cc:64 -> 3
ffb29306e477:9425:10247 [0] NCCL INFO misc/socket.cc:80 -> 3
ffb29306e477:9425:10247 [0] NCCL INFO misc/socket.cc:829 -> 3
ffb29306e477:9425:9564 [0] NCCL INFO misc/socket.cc:881 -> 3
ffb29306e477:9426:10249 [1] NCCL INFO misc/socket.cc:64 -> 3
ffb29306e477:9426:10249 [1] NCCL INFO misc/socket.cc:80 -> 3
ffb29306e477:9426:9563 [1] NCCL INFO misc/socket.cc:881 -> 3
ffb29306e477:9426:10249 [1] NCCL INFO misc/socket.cc:829 -> 3
ffb29306e477:9425:10247 [0] NCCL INFO comm 0x60570600 rank 0 nranks 2 cudaDev 0 busId 65000 - Abort COMPLETE
ffb29306e477:9426:10249 [1] NCCL INFO comm 0x5eff0d40 rank 1 nranks 2 cudaDev 1 busId b1000 - Abort COMPLETE
W0722 14:52:24.425000 9338 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 9426 closing signal SIGTERM
E0722 14:52:24.540000 9338 site-packages/torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 0 (pid: 9425) of binary: /root/miniconda3/envs/wan_video_env/bin/python3.12
Traceback (most recent call last):
  File "/root/miniconda3/envs/wan_video_env/bin/accelerate", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/commands/launch.py", line 1190, in launch_command
    multi_gpu_launcher(args)
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/accelerate/commands/launch.py", line 815, in multi_gpu_launcher
    distrib_run.run(args)
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
examples/wanvideo/model_training/train.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-07-22_14:52:24
  host      : ffb29306e477
  rank      : 0 (local_rank: 0)
  exitcode  : 1 (pid: 9425)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
