ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
2025-07-22 15:12:14,234 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:17,931 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:25,509 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:27,210 - modelscope - INFO - Target directory already exists, skipping creation.
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B

  0%|          | 0/10 [00:00<?, ?it/s]
 10%|█         | 1/10 [00:12<01:53, 12.60s/it]
 20%|██        | 2/10 [00:21<01:23, 10.47s/it]
 30%|███       | 3/10 [00:31<01:10, 10.03s/it]
 40%|████      | 4/10 [00:40<00:58,  9.73s/it]
 50%|█████     | 5/10 [00:49<00:48,  9.62s/it]
 60%|██████    | 6/10 [00:58<00:37,  9.27s/it]
 70%|███████   | 7/10 [01:07<00:27,  9.27s/it]
 80%|████████  | 8/10 [01:16<00:18,  9.28s/it]
 90%|█████████ | 9/10 [01:25<00:09,  9.18s/it]
100%|██████████| 10/10 [01:33<00:00,  8.60s/it]
100%|██████████| 10/10 [01:33<00:00,  9.32s/it]
[2025-07-22 15:14:03,941] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-22 15:14:05,466] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
