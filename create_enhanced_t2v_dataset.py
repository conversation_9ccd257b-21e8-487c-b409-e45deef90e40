#!/usr/bin/env python3
"""
增强版文生视频(T2V)自定义数据集创建工具
为Wan2.1-T2V-1.3B微调生成高质量的真实视频数据集
支持多种分辨率、帧率和动画效果
"""

import os
import json
import pandas as pd
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import cv2
from datetime import datetime
import math
import random
import argparse

class EnhancedT2VDatasetCreator:
    def __init__(self, output_dir="data/enhanced_t2v_dataset", resolution="576x320", fps=8, duration=3.0):
        self.output_dir = Path(output_dir)
        self.videos_dir = self.output_dir / "videos"
        self.metadata_file = self.output_dir / "metadata.csv"
        
        # 解析分辨率
        self.width, self.height = map(int, resolution.split('x'))
        self.fps = fps
        self.duration = duration
        self.total_frames = int(duration * fps)
        
        # 创建目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.videos_dir.mkdir(exist_ok=True)
        
        # 数据记录
        self.dataset_records = []
        
        print(f"🎬 增强版文生视频(T2V)数据集创建器初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   视频目录: {self.videos_dir}")
        print(f"   分辨率: {self.width}x{self.height}")
        print(f"   帧率: {self.fps}fps")
        print(f"   时长: {self.duration}秒")
        print(f"   总帧数: {self.total_frames}")
    
    def create_enhanced_video(self, scene_config, output_path=None):
        """创建增强版动画视频"""
        if output_path is None:
            output_path = f"/tmp/temp_video_{scene_config['name']}.mp4"

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, self.fps, (self.width, self.height))

        print(f"   创建视频: {scene_config['name']} ({self.total_frames}帧, {self.fps}fps, {self.width}x{self.height})")

        for frame_idx in range(self.total_frames):
            # 创建帧
            frame = self.create_enhanced_frame(scene_config, frame_idx)
            # 转换PIL图像到OpenCV格式
            frame_cv = cv2.cvtColor(np.array(frame), cv2.COLOR_RGB2BGR)
            out.write(frame_cv)

        out.release()
        return output_path
    
    def create_enhanced_frame(self, scene_config, frame_idx):
        """创建增强版单个动画帧"""
        progress = frame_idx / self.total_frames
        
        # 创建基础图像
        img = Image.new('RGB', (self.width, self.height), color=scene_config['colors'][0])
        draw = ImageDraw.Draw(img)
        
        # 根据场景类型添加动画效果
        animation_func = getattr(self, f"animate_{scene_config['name']}", None)
        if animation_func:
            animation_func(draw, progress, scene_config['colors'])
        
        # 添加后处理效果
        if scene_config.get('blur', False):
            img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        if scene_config.get('brightness', 1.0) != 1.0:
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(scene_config['brightness'])
        
        return img
    
    def animate_sunset_ocean(self, draw, progress, colors):
        """日落海洋动画 - 适配不同分辨率"""
        # 渐变背景 - 从日落天空到海洋
        for y in range(self.height):
            ratio = y / self.height
            if y < self.height * 0.6:  # 天空部分
                # 日落色彩渐变
                r = int(255 - 120 * ratio + 50 * math.sin(progress * math.pi))
                g = int(140 + 100 * ratio - 30 * math.sin(progress * math.pi))
                b = int(30 + 50 * ratio)
            else:  # 海洋部分
                ocean_ratio = (y - self.height*0.6) / (self.height*0.4)
                r = int(20 + 30 * ocean_ratio)
                g = int(50 + 80 * ocean_ratio)
                b = int(120 + 80 * ocean_ratio)
            draw.line([(0, y), (self.width, y)], fill=(r, g, b))
        
        # 太阳
        sun_x = int(self.width * 0.7)
        sun_y = int(self.height * 0.3 + 20 * math.sin(progress * math.pi * 0.5))
        sun_radius = int(min(self.width, self.height) * 0.08)
        draw.ellipse([sun_x-sun_radius, sun_y-sun_radius, sun_x+sun_radius, sun_y+sun_radius], 
                    fill=(255, 200, 100))
        
        # 动态波浪
        wave_base = int(self.height * 0.6)
        wave_count = max(3, self.width // 150)
        for wave_num in range(wave_count):
            wave_offset = (progress * self.width * 0.3 + wave_num * 80) % (self.width * 0.2)
            for x in range(0, self.width, max(8, self.width // 100)):
                wave_height = 8 + 6 * math.sin((x + wave_offset) * 0.02 + wave_num)
                wave_y = wave_base + wave_num * 15 + int(wave_height)
                if wave_y < self.height:
                    wave_width = max(12, self.width // 50)
                    draw.ellipse([x, wave_y, x+wave_width, wave_y+6], fill=(255, 255, 255, 150))
    
    def animate_forest_morning(self, draw, progress, colors):
        """森林晨光动画"""
        # 渐变背景 - 晨光效果
        for y in range(self.height):
            ratio = y / self.height
            r = int(100 + 100 * (1-ratio) + 30 * math.sin(progress * math.pi))
            g = int(150 + 80 * (1-ratio) + 20 * math.sin(progress * math.pi))
            b = int(50 + 30 * ratio)
            draw.line([(0, y), (self.width, y)], fill=(r, g, b))
        
        # 摇摆的树木
        tree_count = max(5, self.width // 100)
        for tree_idx in range(tree_count):
            tree_x = tree_idx * (self.width // tree_count) + (self.width // tree_count // 2)
            sway = int(8 * math.sin(progress * math.pi * 2 + tree_idx * 0.5))
            
            # 树干
            trunk_x = tree_x + sway
            trunk_width = max(8, self.width // 80)
            trunk_height = int(self.height * 0.4)
            draw.rectangle([trunk_x, self.height-trunk_height, trunk_x+trunk_width, self.height], 
                         fill=(101, 67, 33))
            
            # 树冠
            crown_x = trunk_x - trunk_width*2 + int(6 * math.sin(progress * math.pi * 3 + tree_idx))
            crown_y = self.height - trunk_height - int(self.height * 0.15)
            crown_size = int(min(self.width, self.height) * 0.12)
            draw.ellipse([crown_x, crown_y, crown_x+crown_size, crown_y+crown_size], fill=colors[1])
    
    def animate_city_night(self, draw, progress, colors):
        """城市夜景动画"""
        # 夜空背景
        for y in range(self.height//2):
            ratio = y / (self.height//2)
            r = int(15 + 25 * ratio)
            g = int(15 + 25 * ratio)
            b = int(50 + 80 * ratio)
            draw.line([(0, y), (self.width, y)], fill=(r, g, b))
        
        # 建筑物
        building_count = max(4, self.width // 120)
        for building in range(building_count):
            building_x = building * (self.width // building_count)
            building_width = max(60, self.width // building_count - 10)
            building_height = int(self.height * (0.3 + 0.4 * random.random()))
            draw.rectangle([building_x, self.height-building_height, 
                          building_x+building_width, self.height], fill=(30, 30, 30))
            
            # 窗户灯光
            window_rows = building_height // 25
            window_cols = building_width // 20
            for row in range(window_rows):
                for col in range(window_cols):
                    if random.random() > 0.4:  # 60%的窗户有灯光
                        window_x = building_x + 5 + col * 20
                        window_y = self.height - building_height + row * 25 + 5
                        brightness = int(150 + 100 * math.sin(progress * math.pi * 2 + building + row))
                        draw.rectangle([window_x, window_y, window_x+12, window_y+15], 
                                     fill=(brightness, brightness, 80))
    
    def get_enhanced_scenes(self):
        """获取增强版场景配置"""
        return [
            {
                'name': 'sunset_ocean',
                'prompt': 'Beautiful sunset over calm ocean waves, golden hour lighting, peaceful seascape with warm colors',
                'colors': [(255, 140, 30), (20, 80, 150), (255, 200, 100)],
                'category': 'nature',
                'brightness': 1.1
            },
            {
                'name': 'forest_morning',
                'prompt': 'Misty forest in early morning with sunlight filtering through trees, peaceful woodland scene',
                'colors': [(100, 150, 50), (34, 139, 34), (255, 255, 200)],
                'category': 'nature',
                'blur': True
            },
            {
                'name': 'city_night',
                'prompt': 'Modern city skyline at night with illuminated windows and urban atmosphere, cinematic view',
                'colors': [(15, 15, 50), (255, 215, 0), (30, 30, 30)],
                'category': 'urban'
            },
            {
                'name': 'mountain_dawn',
                'prompt': 'Snow-capped mountains at dawn with pink sky and morning mist, majestic landscape',
                'colors': [(255, 182, 193), (255, 255, 255), (100, 100, 100)],
                'category': 'landscape',
                'brightness': 1.2
            },
            {
                'name': 'desert_wind',
                'prompt': 'Desert landscape with sand dunes and wind-blown particles, golden hour atmosphere',
                'colors': [(238, 203, 173), (255, 218, 185), (210, 180, 140)],
                'category': 'landscape'
            }
        ]
    
    def create_dataset(self, num_variants=3):
        """创建完整的增强版数据集"""
        print(f"🎬 创建增强版文生视频(T2V)数据集...")
        
        scenes = self.get_enhanced_scenes()
        
        for scene_idx, scene in enumerate(scenes):
            for variant in range(num_variants):
                print(f"\n🎨 处理场景 {scene_idx+1}/{len(scenes)}, 变体 {variant+1}/{num_variants}: {scene['name']}")
                
                # 设置随机种子
                random.seed(scene_idx * 100 + variant)
                np.random.seed(scene_idx * 100 + variant)
                
                # 创建视频文件名
                video_filename = f"{scene['name']}_v{variant+1}_{scene_idx:02d}.mp4"
                final_video_path = self.videos_dir / video_filename
                
                # 创建视频
                self.create_enhanced_video(scene, str(final_video_path))
                
                # 记录数据
                record = {
                    'video': f"videos/{video_filename}",
                    'prompt': scene['prompt'],
                    'category': scene['category'],
                    'scene_name': scene['name'],
                    'variant': variant + 1,
                    'resolution': f"{self.width}x{self.height}",
                    'fps': self.fps,
                    'duration': self.duration
                }
                self.dataset_records.append(record)
                
                print(f"   ✅ 视频: {video_filename}")
        
        print(f"\n✅ 创建了 {len(self.dataset_records)} 个视频样本")
    
    def save_metadata(self):
        """保存元数据文件"""
        if not self.dataset_records:
            print("⚠️  没有数据记录，跳过保存")
            return
        
        # 保存训练用的CSV格式
        df = pd.DataFrame(self.dataset_records)
        training_df = df[['video', 'prompt']].copy()
        training_df.to_csv(self.metadata_file, index=False)
        
        # 保存完整的JSON格式
        json_file = self.output_dir / "metadata_full.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset_records, f, ensure_ascii=False, indent=2)
        
        # 创建统计信息
        categories = {}
        for record in self.dataset_records:
            cat = record['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        stats = {
            'total_samples': len(self.dataset_records),
            'unique_scenes': len(set(record['scene_name'] for record in self.dataset_records)),
            'variants_per_scene': 3,
            'categories': categories,
            'video_specs': {
                'resolution': f'{self.width}x{self.height}',
                'fps': self.fps,
                'duration_seconds': self.duration,
                'total_frames': self.total_frames
            },
            'creation_time': datetime.now().isoformat()
        }
        
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 元数据已保存:")
        print(f"   训练CSV: {self.metadata_file}")
        print(f"   完整JSON: {json_file}")
        print(f"   统计文件: {stats_file}")

def main():
    parser = argparse.ArgumentParser(description='增强版文生视频数据集创建工具')
    parser.add_argument('--output_dir', default='data/enhanced_t2v_dataset', help='输出目录')
    parser.add_argument('--resolution', default='576x320', help='视频分辨率 (默认: 576x320)')
    parser.add_argument('--fps', type=int, default=8, help='帧率 (默认: 8)')
    parser.add_argument('--duration', type=float, default=3.0, help='视频时长秒数 (默认: 3.0)')
    parser.add_argument('--variants', type=int, default=3, help='每个场景的变体数量 (默认: 3)')
    
    args = parser.parse_args()
    
    print("🎬 增强版Wan2.1-T2V-1.3B 文生视频数据集创建工具")
    print("=" * 70)
    
    # 创建增强版T2V数据集创建器
    creator = EnhancedT2VDatasetCreator(
        output_dir=args.output_dir,
        resolution=args.resolution,
        fps=args.fps,
        duration=args.duration
    )
    
    # 创建视频数据集
    creator.create_dataset(num_variants=args.variants)
    
    # 保存元数据
    creator.save_metadata()
    
    print(f"\n🎉 增强版文生视频数据集创建完成!")
    print(f"📁 数据集位置: {creator.output_dir}")
    print(f"📝 训练用CSV: {creator.metadata_file}")
    
    print(f"\n🚀 使用方法:")
    print(f"   # 使用自定义数据集训练")
    print(f"   accelerate launch examples/wanvideo/model_training/train.py \\")
    print(f"     --dataset_base_path {args.output_dir} \\")
    print(f"     --dataset_metadata_path {args.output_dir}/metadata.csv \\")
    print(f"     --height {args.resolution.split('x')[1]} \\")
    print(f"     --width {args.resolution.split('x')[0]} \\")
    print(f"     [其他训练参数...]")

if __name__ == "__main__":
    main()
