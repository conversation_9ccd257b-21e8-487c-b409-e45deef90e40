#!/bin/bash
# 完整的Wan2.1-T2V-1.3B LoRA训练启动脚本
# 包含从环境检查到训练完成的全过程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="./logs"
TENSORBOARD_DIR="./tensorboard_logs/complete_training_${TIMESTAMP}"

# 创建目录
mkdir -p "$LOG_DIR"
mkdir -p "$TENSORBOARD_DIR"

log_info "🚀 开始完整的Wan2.1-T2V-1.3B LoRA训练流水线"
log_info "📅 时间戳: $TIMESTAMP"
log_info "📁 日志目录: $LOG_DIR"
log_info "📊 TensorBoard: $TENSORBOARD_DIR"

# 步骤1: 环境检查
log_info "📋 步骤1/8: 环境检查"
echo "检查Python环境、CUDA、依赖包..." | tee "$LOG_DIR/01_environment_check_${TIMESTAMP}.log"

# 检查conda环境
if ! command -v conda &> /dev/null; then
    log_error "Conda未安装或未在PATH中"
    exit 1
fi

# 检查wan_video_env环境
if ! conda env list | grep -q "wan_video_env"; then
    log_error "wan_video_env环境不存在，请先创建环境"
    exit 1
fi

# 激活环境并检查Python包
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

python -c "
import torch
import sys
print(f'Python版本: {sys.version.split()[0]}')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
" >> "$LOG_DIR/01_environment_check_${TIMESTAMP}.log"

log_success "环境检查完成"

# 步骤2: 模型检查
log_info "📋 步骤2/8: 模型文件检查"
echo "检查Wan2.1-T2V-1.3B模型文件..." | tee "$LOG_DIR/02_model_check_${TIMESTAMP}.log"

MODEL_DIR="./models/Wan-AI/Wan2.1-T2V-1.3B"
REQUIRED_FILES=(
    "diffusion_pytorch_model.safetensors"
    "models_t5_umt5-xxl-enc-bf16.pth"
    "Wan2.1_VAE.pth"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$MODEL_DIR/$file" ]; then
        file_size=$(du -h "$MODEL_DIR/$file" | cut -f1)
        echo "✅ $file ($file_size)" >> "$LOG_DIR/02_model_check_${TIMESTAMP}.log"
    else
        echo "❌ $file (缺失)" >> "$LOG_DIR/02_model_check_${TIMESTAMP}.log"
        log_warning "模型文件 $file 不存在，训练时将自动下载"
    fi
done

log_success "模型检查完成"

# 步骤3: 数据集准备
log_info "📋 步骤3/8: 数据集准备"
echo "准备训练数据集..." | tee "$LOG_DIR/03_dataset_prep_${TIMESTAMP}.log"

DATASET_DIR="./data/example_video_dataset"
mkdir -p "$DATASET_DIR"

# 创建或检查metadata.csv
if [ ! -f "$DATASET_DIR/metadata.csv" ]; then
    echo "video,prompt" > "$DATASET_DIR/metadata.csv"
    echo 'video1.mp4,"from sunset to night, a small town, light, house, river"' >> "$DATASET_DIR/metadata.csv"
    echo "创建示例数据集metadata.csv" >> "$LOG_DIR/03_dataset_prep_${TIMESTAMP}.log"
else
    echo "数据集metadata.csv已存在" >> "$LOG_DIR/03_dataset_prep_${TIMESTAMP}.log"
fi

log_success "数据集准备完成"

# 步骤4: Accelerate配置检查
log_info "📋 步骤4/8: Accelerate配置检查"
echo "检查Accelerate配置..." | tee "$LOG_DIR/04_accelerate_config_${TIMESTAMP}.log"

if [ ! -f "accelerate_config.yaml" ]; then
    log_warning "accelerate_config.yaml不存在，使用默认配置"
    cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF
    echo "创建默认accelerate_config.yaml" >> "$LOG_DIR/04_accelerate_config_${TIMESTAMP}.log"
else
    echo "accelerate_config.yaml已存在" >> "$LOG_DIR/04_accelerate_config_${TIMESTAMP}.log"
fi

log_success "Accelerate配置检查完成"

# 步骤5: 创建TensorBoard监控
log_info "📋 步骤5/8: 启动TensorBoard监控"
echo "启动TensorBoard服务..." | tee "$LOG_DIR/05_tensorboard_start_${TIMESTAMP}.log"

# 启动TensorBoard (后台运行)
nohup tensorboard --logdir "$TENSORBOARD_DIR" --port 6010 --host 0.0.0.0 > "$LOG_DIR/tensorboard_${TIMESTAMP}.log" 2>&1 &
TENSORBOARD_PID=$!
echo "TensorBoard PID: $TENSORBOARD_PID" >> "$LOG_DIR/05_tensorboard_start_${TIMESTAMP}.log"

log_success "TensorBoard已启动 (PID: $TENSORBOARD_PID)"
log_info "🌐 访问地址: http://localhost:6010"

# 步骤6: 启动训练监控脚本
log_info "📋 步骤6/8: 启动训练监控"

# 创建训练监控脚本
cat > "training_monitor_${TIMESTAMP}.py" << 'EOF'
import torch
from torch.utils.tensorboard import SummaryWriter
import time
import os
import sys
from datetime import datetime

# 获取TensorBoard目录
tensorboard_dir = sys.argv[1] if len(sys.argv) > 1 else "./tensorboard_logs/default"
writer = SummaryWriter(log_dir=tensorboard_dir)

# 记录训练开始
start_time = datetime.now()
writer.add_text("Training/Start_Time", start_time.strftime('%Y-%m-%d %H:%M:%S'), 0)

# 记录系统信息
system_info = f"""
# 训练系统信息

## 硬件配置
- GPU数量: {torch.cuda.device_count()}
- GPU型号: {', '.join([torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]) if torch.cuda.is_available() else 'N/A'}
- 总显存: {sum([torch.cuda.get_device_properties(i).total_memory for i in range(torch.cuda.device_count())]) / (1024**3):.1f}GB

## 训练配置
- 模型: Wan2.1-T2V-1.3B
- LoRA rank: 16
- 学习率: 1e-4
- 训练轮数: 5
- 批大小: 1
- 梯度累积: 8步

## 开始时间
{start_time.strftime('%Y-%m-%d %H:%M:%S')}
"""

writer.add_text("Training/System_Info", system_info, 0)
writer.close()
print(f"训练监控信息已记录到: {tensorboard_dir}")
EOF

python "training_monitor_${TIMESTAMP}.py" "$TENSORBOARD_DIR"
log_success "训练监控已启动"

# 步骤7: 启动实际训练
log_info "📋 步骤7/8: 启动LoRA训练"
echo "开始Wan2.1-T2V-1.3B LoRA微调训练..." | tee "$LOG_DIR/07_training_start_${TIMESTAMP}.log"

# 训练命令
TRAIN_CMD="accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 \
  --width 576 \
  --dataset_repeat 500 \
  --model_id_with_origin_paths 'Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth' \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt 'pipe.dit.' \
  --output_path './models/train/Wan2.1-T2V-1.3B_lora' \
  --lora_base_model 'dit' \
  --lora_target_modules 'q,k,v,o,ffn.0,ffn.2' \
  --lora_rank 16 \
  --use_gradient_checkpointing_offload"

echo "训练命令: $TRAIN_CMD" >> "$LOG_DIR/07_training_start_${TIMESTAMP}.log"

log_info "🚀 正在启动训练进程..."
log_info "📝 训练日志将保存到: $LOG_DIR/07_training_output_${TIMESTAMP}.log"
log_info "⏱️ 预计训练时间: 1-2小时"

# 执行训练命令并记录输出
eval $TRAIN_CMD 2>&1 | tee "$LOG_DIR/07_training_output_${TIMESTAMP}.log"
TRAIN_EXIT_CODE=${PIPESTATUS[0]}

# 步骤8: 训练结果验证
log_info "📋 步骤8/8: 训练结果验证"

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    log_success "训练成功完成!"
    
    # 检查输出文件
    OUTPUT_DIR="./models/train/Wan2.1-T2V-1.3B_lora"
    if [ -d "$OUTPUT_DIR" ]; then
        echo "训练输出文件:" > "$LOG_DIR/08_results_${TIMESTAMP}.log"
        find "$OUTPUT_DIR" -type f -exec ls -lh {} \; >> "$LOG_DIR/08_results_${TIMESTAMP}.log"
        log_success "训练结果已保存到: $OUTPUT_DIR"
    else
        log_warning "输出目录不存在: $OUTPUT_DIR"
    fi
else
    log_error "训练失败，退出码: $TRAIN_EXIT_CODE"
fi

# 清理和总结
log_info "🧹 清理临时文件"
rm -f "training_monitor_${TIMESTAMP}.py"

# 生成最终报告
log_info "📊 生成最终报告"
cat > "$LOG_DIR/final_report_${TIMESTAMP}.md" << EOF
# Wan2.1-T2V-1.3B LoRA训练完整报告

## 训练概览
- 开始时间: $(date)
- 训练状态: $([ $TRAIN_EXIT_CODE -eq 0 ] && echo "成功" || echo "失败")
- 退出码: $TRAIN_EXIT_CODE

## 文件位置
- 日志目录: $LOG_DIR
- TensorBoard: $TENSORBOARD_DIR
- 模型输出: ./models/train/Wan2.1-T2V-1.3B_lora

## 访问链接
- TensorBoard: http://localhost:6010

## 后续步骤
1. 查看TensorBoard监控数据
2. 验证训练结果
3. 测试模型推理
4. 合并LoRA权重（可选）

EOF

log_success "🎉 完整训练流水线执行完成!"
log_info "📊 最终报告: $LOG_DIR/final_report_${TIMESTAMP}.md"
log_info "📁 所有日志文件: $LOG_DIR/"
log_info "🌐 TensorBoard: http://localhost:6010"

# 如果训练成功，保持TensorBoard运行
if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    log_info "🔄 TensorBoard将继续运行，PID: $TENSORBOARD_PID"
    log_info "💡 停止TensorBoard: kill $TENSORBOARD_PID"
else
    log_info "🛑 由于训练失败，停止TensorBoard"
    kill $TENSORBOARD_PID 2>/dev/null || true
fi

exit $TRAIN_EXIT_CODE
