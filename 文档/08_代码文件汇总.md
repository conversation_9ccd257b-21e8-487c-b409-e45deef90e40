# 代码文件汇总

## 核心训练脚本

### 1. LoRA训练脚本 - `train_lora_optimized.py`

```python
#!/usr/bin/env python3
"""
Wan2.1-T2V-1.3B 优化版LoRA训练脚本
支持双卡训练，320x576分辨率，rank=16
"""

import os
import torch
import time
from datetime import datetime
from accelerate import Accelerator
from diffsynth import ModelManager
from diffsynth.models.lora import LoRAManager

def main():
    print("🚀 开始优化版LoRA训练...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化Accelerator
    accelerator = Accelerator(
        mixed_precision="bf16",
        gradient_accumulation_steps=1,
        log_with="tensorboard",
        project_dir="./logs"
    )
    
    print(f"🖥️ 设备: {accelerator.device}")
    print(f"🔢 进程数: {accelerator.num_processes}")
    print(f"🏷️ 进程排名: {accelerator.process_index}")
    
    # 模型配置
    model_config = {
        "model_path": "./models/Wan-AI/Wan2.1-T2V-1.3B",
        "lora_rank": 16,
        "lora_alpha": 32,
        "target_modules": ["q", "k", "v", "o"],
        "lora_dropout": 0.1,
    }
    
    # 训练配置
    train_config = {
        "num_epochs": 3,
        "batch_size": 1,
        "learning_rate": 1e-4,
        "weight_decay": 0.01,
        "warmup_steps": 5,
        "save_steps": 25,
        "logging_steps": 1,
        "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_optimized",
    }
    
    # 视频配置
    video_config = {
        "width": 320,
        "height": 576,
        "frames": 16,
        "fps": 8,
    }
    
    # 加载模型
    print("📥 加载基础模型...")
    model_manager = ModelManager()
    model_manager.load_model(model_config["model_path"])
    
    # 配置LoRA
    print("🔧 配置LoRA...")
    lora_manager = LoRAManager()
    lora_manager.add_lora(
        model_manager.model,
        rank=model_config["lora_rank"],
        alpha=model_config["lora_alpha"],
        target_modules=model_config["target_modules"],
        dropout=model_config["lora_dropout"]
    )
    
    # 准备数据
    print("📂 准备数据集...")
    from diffsynth.data import VideoDataset
    dataset = VideoDataset(
        data_path="./data/metadata.csv",
        width=video_config["width"],
        height=video_config["height"],
        frames=video_config["frames"],
        fps=video_config["fps"]
    )
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=train_config["batch_size"],
        shuffle=True,
        num_workers=2
    )
    
    # 优化器
    optimizer = torch.optim.AdamW(
        lora_manager.get_trainable_parameters(),
        lr=train_config["learning_rate"],
        weight_decay=train_config["weight_decay"]
    )
    
    # 学习率调度器
    from transformers import get_cosine_schedule_with_warmup
    scheduler = get_cosine_schedule_with_warmup(
        optimizer,
        num_warmup_steps=train_config["warmup_steps"],
        num_training_steps=len(dataloader) * train_config["num_epochs"]
    )
    
    # 使用accelerator包装
    model, optimizer, dataloader, scheduler = accelerator.prepare(
        model_manager.model, optimizer, dataloader, scheduler
    )
    
    # 训练循环
    print("🏋️ 开始训练...")
    global_step = 0
    
    for epoch in range(train_config["num_epochs"]):
        model.train()
        epoch_loss = 0
        
        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{train_config['num_epochs']}")
        
        for step, batch in enumerate(progress_bar):
            with accelerator.accumulate(model):
                # 前向传播
                outputs = model(**batch)
                loss = outputs.loss
                
                # 反向传播
                accelerator.backward(loss)
                optimizer.step()
                scheduler.step()
                optimizer.zero_grad()
                
                epoch_loss += loss.item()
                global_step += 1
                
                # 日志记录
                if global_step % train_config["logging_steps"] == 0:
                    accelerator.log({
                        "train_loss": loss.item(),
                        "learning_rate": scheduler.get_last_lr()[0],
                        "epoch": epoch,
                        "step": global_step
                    }, step=global_step)
                
                # 保存检查点
                if global_step % train_config["save_steps"] == 0:
                    if accelerator.is_main_process:
                        save_path = os.path.join(
                            train_config["output_dir"], 
                            f"checkpoint-{global_step}"
                        )
                        lora_manager.save_lora(save_path)
                        print(f"💾 保存检查点: {save_path}")
                
                progress_bar.set_postfix({
                    "loss": f"{loss.item():.4f}",
                    "lr": f"{scheduler.get_last_lr()[0]:.2e}"
                })
        
        # 每轮结束保存
        if accelerator.is_main_process:
            save_path = os.path.join(
                train_config["output_dir"], 
                f"epoch-{epoch}.safetensors"
            )
            lora_manager.save_lora(save_path)
            print(f"💾 保存轮次模型: {save_path}")
        
        avg_loss = epoch_loss / len(dataloader)
        print(f"📊 Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
    
    print("✅ 训练完成!")
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
```

### 2. 极限版训练脚本 - `train_lora_minimal.py`

```python
#!/usr/bin/env python3
"""
Wan2.1-T2V-1.3B 极限版LoRA训练脚本
单卡训练，256x448分辨率，rank=8
"""

import os
import torch
import time
from datetime import datetime
from diffsynth import ModelManager
from diffsynth.models.lora import LoRAManager

def main():
    print("🚀 开始极限版LoRA训练...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置单卡
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"🖥️ 使用设备: {device}")
    
    # 清理显存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🧹 清理显存完成")
    
    # 模型配置 (极限版)
    model_config = {
        "model_path": "./models/Wan-AI/Wan2.1-T2V-1.3B",
        "lora_rank": 8,          # 更小的rank
        "lora_alpha": 16,        # 更小的alpha
        "target_modules": ["q", "k"],  # 只训练q和k
        "lora_dropout": 0.05,    # 更小的dropout
    }
    
    # 训练配置 (快速训练)
    train_config = {
        "num_epochs": 1,         # 只训练1轮
        "batch_size": 1,
        "learning_rate": 5e-5,   # 更小的学习率
        "weight_decay": 0.01,
        "warmup_steps": 2,       # 更少的预热步数
        "save_steps": 10,        # 更频繁保存
        "logging_steps": 1,
        "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_minimal",
    }
    
    # 视频配置 (极限分辨率)
    video_config = {
        "width": 256,            # 最小宽度
        "height": 448,           # 最小高度
        "frames": 16,
        "fps": 8,
    }
    
    # 加载模型
    print("📥 加载基础模型...")
    model_manager = ModelManager()
    model_manager.load_model(model_config["model_path"])
    model_manager.model.to(device)
    
    # 配置LoRA
    print("🔧 配置LoRA...")
    lora_manager = LoRAManager()
    lora_manager.add_lora(
        model_manager.model,
        rank=model_config["lora_rank"],
        alpha=model_config["lora_alpha"],
        target_modules=model_config["target_modules"],
        dropout=model_config["lora_dropout"]
    )
    
    # 统计可训练参数
    trainable_params = sum(p.numel() for p in model_manager.model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model_manager.model.parameters())
    print(f"📊 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
    
    # 准备数据
    print("📂 准备数据集...")
    from diffsynth.data import VideoDataset
    dataset = VideoDataset(
        data_path="./data/metadata.csv",
        width=video_config["width"],
        height=video_config["height"],
        frames=video_config["frames"],
        fps=video_config["fps"]
    )
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=train_config["batch_size"],
        shuffle=True,
        num_workers=1  # 减少工作进程
    )
    
    # 优化器
    optimizer = torch.optim.AdamW(
        lora_manager.get_trainable_parameters(),
        lr=train_config["learning_rate"],
        weight_decay=train_config["weight_decay"]
    )
    
    # 训练循环
    print("🏋️ 开始训练...")
    model_manager.model.train()
    
    for epoch in range(train_config["num_epochs"]):
        epoch_loss = 0
        step_count = 0
        
        for step, batch in enumerate(dataloader):
            start_time = time.time()
            
            # 移动数据到设备
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            optimizer.zero_grad()
            outputs = model_manager.model(**batch)
            loss = outputs.loss
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            step_count += 1
            step_time = time.time() - start_time
            
            # 日志记录
            if step % train_config["logging_steps"] == 0:
                print(f"步骤 {step+1}/{len(dataloader)}: "
                      f"损失={loss.item():.4f}, "
                      f"时间={step_time:.2f}s")
            
            # 保存检查点
            if (step + 1) % train_config["save_steps"] == 0:
                save_path = os.path.join(
                    train_config["output_dir"], 
                    f"checkpoint-step-{step+1}.safetensors"
                )
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                lora_manager.save_lora(save_path)
                print(f"💾 保存检查点: {save_path}")
        
        # 轮次结束保存
        save_path = os.path.join(
            train_config["output_dir"], 
            f"epoch-{epoch}.safetensors"
        )
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        lora_manager.save_lora(save_path)
        print(f"💾 保存轮次模型: {save_path}")
        
        avg_loss = epoch_loss / step_count
        print(f"📊 Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
    
    print("✅ 训练完成!")
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
```

## 配置文件

### 3. Accelerate配置 - `configs/accelerate_config.yaml`

```yaml
# Accelerate多卡训练配置文件
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 4. 训练配置 - `configs/training_config.py`

```python
"""
训练配置文件
包含所有训练相关的超参数和路径配置
"""

# 基础模型配置
MODEL_CONFIG = {
    "model_id": "Wan-AI/Wan2.1-T2V-1.3B",
    "model_path": "./models/Wan-AI/Wan2.1-T2V-1.3B",
    "cache_dir": "./models_cache",
}

# 优化版LoRA配置
OPTIMIZED_LORA_CONFIG = {
    "r": 16,                      # LoRA秩
    "lora_alpha": 32,             # LoRA alpha参数
    "target_modules": ["q", "k", "v", "o"],  # 目标模块
    "lora_dropout": 0.1,          # LoRA dropout
    "bias": "none",               # 偏置处理
    "task_type": "FEATURE_EXTRACTION",  # 任务类型
    "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_optimized",
}

# 极限版LoRA配置
MINIMAL_LORA_CONFIG = {
    "r": 8,                       # 更小的秩
    "lora_alpha": 16,             # 更小的alpha
    "target_modules": ["q", "k"], # 只训练q和k
    "lora_dropout": 0.05,         # 更小的dropout
    "bias": "none",
    "task_type": "FEATURE_EXTRACTION",
    "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_minimal",
}

# 训练超参数
TRAINING_ARGS = {
    # 基础训练参数
    "num_train_epochs": 3,        # 训练轮数
    "per_device_train_batch_size": 1,  # 批大小
    "gradient_accumulation_steps": 1,  # 梯度累积
    "learning_rate": 1e-4,        # 学习率
    "lr_scheduler_type": "cosine", # 学习率调度器
    "warmup_steps": 5,            # 预热步数
    "max_steps": -1,              # 最大步数 (-1表示使用epochs)
    
    # 优化器参数
    "optim": "adamw_torch",       # 优化器类型
    "weight_decay": 0.01,         # 权重衰减
    "adam_beta1": 0.9,            # Adam beta1
    "adam_beta2": 0.999,          # Adam beta2
    "adam_epsilon": 1e-8,         # Adam epsilon
    "max_grad_norm": 1.0,         # 梯度裁剪
    
    # 保存和日志
    "output_dir": "./models/train",  # 输出目录
    "logging_dir": "./logs",      # 日志目录
    "logging_steps": 1,           # 日志间隔
    "save_steps": 25,             # 保存间隔
    "save_total_limit": 3,        # 最多保存检查点数
    "evaluation_strategy": "no",   # 评估策略
    
    # 混合精度和性能
    "bf16": True,                 # 使用BF16
    "fp16": False,                # 不使用FP16
    "dataloader_num_workers": 2,  # 数据加载工作进程
    "dataloader_pin_memory": True, # 固定内存
    "remove_unused_columns": False, # 保留所有列
    
    # 其他参数
    "seed": 42,                   # 随机种子
    "report_to": "tensorboard",   # 报告工具
    "run_name": None,             # 运行名称
}

# 视频处理参数
VIDEO_PARAMS = {
    "optimized": {
        "width": 320,             # 视频宽度
        "height": 576,            # 视频高度
        "frames": 16,             # 帧数
        "fps": 8,                 # 帧率
        "max_duration": 2.0,      # 最大时长(秒)
    },
    
    "minimal": {
        "width": 256,             # 更小的宽度
        "height": 448,            # 更小的高度
        "frames": 16,             # 帧数
        "fps": 8,                 # 帧率
        "max_duration": 2.0,      # 最大时长(秒)
    }
}

# 数据集配置
DATASET_CONFIG = {
    "data_dir": "./data",
    "metadata_file": "metadata.csv",
    "video_dir": "videos",
    "cache_dir": "./data_cache",
    "preprocessing": {
        "normalize": True,
        "center_crop": True,
        "random_flip": False,     # 视频通常不随机翻转
    }
}

# 环境配置
ENVIRONMENT_CONFIG = {
    "cuda_visible_devices": None,  # None表示使用所有GPU
    "pytorch_cuda_alloc_conf": "expandable_segments:True,max_split_size_mb:128",
    "cuda_launch_blocking": "1",
    "nccl_debug": "INFO",
    "nccl_ib_disable": "1",
}
```

## 工具脚本

### 5. 模型下载脚本 - `download_model.py`

```python
#!/usr/bin/env python3
"""
模型下载脚本
自动下载Wan2.1-T2V-1.3B模型并验证完整性
"""

import os
import time
from datetime import datetime
from modelscope import snapshot_download

def download_wan_model():
    """下载Wan2.1-T2V-1.3B模型"""
    
    print("🚀 开始下载Wan2.1-T2V-1.3B模型...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置下载参数
    model_id = "Wan-AI/Wan2.1-T2V-1.3B"
    cache_dir = "./models"
    local_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    # 创建目录
    os.makedirs(cache_dir, exist_ok=True)
    os.makedirs(local_dir, exist_ok=True)
    
    try:
        # 下载模型
        print(f"📥 正在下载模型: {model_id}")
        print(f"📁 保存路径: {local_dir}")
        
        model_path = snapshot_download(
            model_id=model_id,
            cache_dir=cache_dir,
            local_dir=local_dir,
            local_dir_use_symlinks=False  # 不使用符号链接
        )
        
        print(f"✅ 模型下载成功: {model_path}")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return model_path
        
    except Exception as e:
        print(f"❌ 模型下载失败: {str(e)}")
        return None

def verify_model_files():
    """验证模型文件完整性"""
    
    print("\n🔍 验证模型文件...")
    
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    # 预期文件和大小 (GB)
    expected_files = {
        "diffusion_pytorch_model.safetensors": 5.29,
        "models_t5_umt5-xxl-enc-bf16.pth": 10.58,
        "Wan2.1_VAE.pth": 0.47,
        "config.json": 0.001,
        "model_index.json": 0.001,
    }
    
    all_valid = True
    total_size = 0
    
    for filename, expected_size in expected_files.items():
        filepath = os.path.join(model_dir, filename)
        
        if os.path.exists(filepath):
            actual_size = os.path.getsize(filepath) / (1024**3)  # GB
            total_size += actual_size
            
            if abs(actual_size - expected_size) < 0.1:  # 允许0.1GB误差
                print(f"  ✅ {filename}: {actual_size:.2f}GB")
            else:
                print(f"  ⚠️  {filename}: {actual_size:.2f}GB (预期{expected_size:.2f}GB)")
                all_valid = False
        else:
            print(f"  ❌ {filename}: 文件不存在")
            all_valid = False
    
    print(f"\n📊 总大小: {total_size:.2f}GB")
    
    if all_valid:
        print("✅ 所有文件验证通过")
    else:
        print("❌ 部分文件验证失败")
    
    return all_valid

def test_model_loading():
    """测试模型加载"""
    
    print("\n🧪 测试模型加载...")
    
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    try:
        # 测试SafeTensors文件
        from safetensors.torch import load_file
        dit_path = os.path.join(model_dir, "diffusion_pytorch_model.safetensors")
        if os.path.exists(dit_path):
            dit_weights = load_file(dit_path)
            print(f"  ✅ DiT模型加载成功: {len(dit_weights)}个权重张量")
        
        # 测试PyTorch文件
        import torch
        t5_path = os.path.join(model_dir, "models_t5_umt5-xxl-enc-bf16.pth")
        if os.path.exists(t5_path):
            t5_weights = torch.load(t5_path, map_location='cpu')
            print(f"  ✅ T5编码器加载成功")
        
        vae_path = os.path.join(model_dir, "Wan2.1_VAE.pth")
        if os.path.exists(vae_path):
            vae_weights = torch.load(vae_path, map_location='cpu')
            print(f"  ✅ VAE编码器加载成功")
        
        print("✅ 模型加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False

def main():
    """主函数"""
    
    # 下载模型
    model_path = download_wan_model()
    
    if model_path:
        # 验证文件
        files_ok = verify_model_files()
        
        if files_ok:
            # 测试加载
            loading_ok = test_model_loading()
            
            if loading_ok:
                print("\n🎉 模型下载和验证全部完成！")
            else:
                print("\n⚠️ 模型文件验证通过，但加载测试失败")
        else:
            print("\n⚠️ 模型文件验证失败，请检查下载")
    else:
        print("\n❌ 模型下载失败")

if __name__ == "__main__":
    main()
```

### 6. 权重合并脚本 - `merge_lora_weights.py`

```python
#!/usr/bin/env python3
"""
LoRA权重合并脚本
将训练好的LoRA权重合并到基础模型中
"""

import os
import torch
from safetensors.torch import load_file, save_file
import time
from datetime import datetime

def merge_lora_to_base_model(base_model_path, lora_path, output_path, alpha=1.0):
    """
    将LoRA权重合并到基础模型
    
    Args:
        base_model_path: 基础模型路径
        lora_path: LoRA权重路径  
        output_path: 输出模型路径
        alpha: LoRA权重缩放因子
    """
    
    print(f"🔄 开始合并LoRA权重...")
    print(f"📥 基础模型: {base_model_path}")
    print(f"📥 LoRA权重: {lora_path}")
    print(f"📤 输出路径: {output_path}")
    print(f"⚖️  Alpha值: {alpha}")
    
    # 加载基础模型
    print("📥 加载基础模型...")
    base_weights = load_file(base_model_path)
    print(f"✅ 基础模型加载完成，包含 {len(base_weights)} 个权重")
    
    # 加载LoRA权重
    print("📥 加载LoRA权重...")
    lora_weights = load_file(lora_path)
    print(f"✅ LoRA权重加载完成，包含 {len(lora_weights)} 个权重")
    
    # 分析LoRA权重结构
    lora_pairs = {}
    for key in lora_weights.keys():
        if '.lora_A.' in key:
            base_key = key.replace('.lora_A.default.weight', '')
            if base_key not in lora_pairs:
                lora_pairs[base_key] = {}
            lora_pairs[base_key]['A'] = key
        elif '.lora_B.' in key:
            base_key = key.replace('.lora_B.default.weight', '')
            if base_key not in lora_pairs:
                lora_pairs[base_key] = {}
            lora_pairs[base_key]['B'] = key
    
    print(f"📊 找到 {len(lora_pairs)} 个LoRA层对")
    
    # 合并权重
    merged_weights = base_weights.copy()
    merged_count = 0
    
    print("🔄 开始权重合并...")
    for base_key, pair in lora_pairs.items():
        if 'A' in pair and 'B' in pair:
            # 构建对应的基础模型权重键名
            base_weight_key = f"{base_key}.weight"
            
            if base_weight_key in merged_weights:
                # 获取LoRA权重
                lora_A = lora_weights[pair['A']]  # [rank, in_features]
                lora_B = lora_weights[pair['B']]  # [out_features, rank]
                
                # 计算LoRA增量: B @ A
                lora_delta = torch.mm(lora_B, lora_A) * alpha
                
                # 合并到基础权重
                merged_weights[base_weight_key] = merged_weights[base_weight_key] + lora_delta
                merged_count += 1
                
                if merged_count <= 5:  # 显示前几个合并的层
                    print(f"  ✅ 合并层: {base_key}")
                    print(f"     LoRA_A: {lora_A.shape}, LoRA_B: {lora_B.shape}")
                    print(f"     增量: {lora_delta.shape}, 范围: [{lora_delta.min():.6f}, {lora_delta.max():.6f}]")
    
    print(f"✅ 权重合并完成，共合并 {merged_count} 个层")
    
    # 保存合并后的模型
    print("💾 保存合并后的模型...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    save_file(merged_weights, output_path)
    
    # 检查输出文件
    output_size = os.path.getsize(output_path) / (1024**3)  # GB
    print(f"✅ 模型保存成功: {output_path}")
    print(f"📊 文件大小: {output_size:.2f} GB")
    
    return True

def main():
    """主函数"""
    
    print("🚀 开始LoRA权重合并...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 配置路径
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    lora_optimized_path = "./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors"
    lora_minimal_path = "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors"
    
    # 合并配置
    merge_configs = [
        # 优化版LoRA
        {
            "name": "optimized",
            "lora_path": lora_optimized_path,
            "alphas": [0.5, 1.0, 1.5]
        },
        # 极限版LoRA
        {
            "name": "minimal",
            "lora_path": lora_minimal_path,
            "alphas": [0.5, 1.0, 1.5]
        }
    ]
    
    success_count = 0
    total_count = 0
    
    for config in merge_configs:
        if not os.path.exists(config["lora_path"]):
            print(f"⚠️ LoRA文件不存在: {config['lora_path']}")
            continue
        
        print(f"\n{'='*60}")
        print(f"🧪 合并LoRA: {config['name']}")
        print(f"📄 LoRA路径: {config['lora_path']}")
        
        for alpha in config["alphas"]:
            total_count += 1
            output_path = f"./models/merged/Wan2.1-T2V-1.3B_{config['name']}_alpha_{alpha}.safetensors"
            
            print(f"\n🔄 合并 Alpha={alpha}...")
            
            try:
                success = merge_lora_to_base_model(
                    base_model_path=base_model_path,
                    lora_path=config["lora_path"],
                    output_path=output_path,
                    alpha=alpha
                )
                
                if success:
                    success_count += 1
                    print(f"✅ Alpha={alpha} 合并成功")
                else:
                    print(f"❌ Alpha={alpha} 合并失败")
                    
            except Exception as e:
                print(f"❌ Alpha={alpha} 合并失败: {str(e)}")
    
    # 生成合并报告
    print(f"\n{'='*60}")
    print("📋 权重合并报告")
    print(f"📊 合并统计:")
    print(f"  - 总任务数: {total_count}")
    print(f"  - 成功数量: {success_count}")
    print(f"  - 失败数量: {total_count - success_count}")
    print(f"  - 成功率: {success_count/total_count*100:.1f}%")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count > 0:
        print(f"🎉 LoRA权重合并完成！生成了 {success_count} 个合并模型")
    else:
        print(f"❌ LoRA权重合并失败")

if __name__ == "__main__":
    main()
```

## 启动脚本

### 7. 一键启动脚本 - `run_training.sh`

```bash
#!/bin/bash
"""
一键启动训练脚本
自动执行完整的训练流程
"""

set -e  # 遇到错误立即退出

echo "🚀 开始Wan2.1-T2V-1.3B LoRA微调流程..."
echo "⏰ 开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 检查环境
echo "🔍 检查环境..."
if ! command -v conda &> /dev/null; then
    echo "❌ Conda未安装"
    exit 1
fi

if ! nvidia-smi &> /dev/null; then
    echo "❌ NVIDIA驱动未安装或GPU不可用"
    exit 1
fi

# 激活环境
echo "🔧 激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate wan_video_env

# 检查Python环境
python -c "import torch; print(f'✅ PyTorch: {torch.__version__}'); print(f'✅ CUDA可用: {torch.cuda.is_available()}'); print(f'✅ GPU数量: {torch.cuda.device_count()}')"

# 设置环境变量
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=1
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=1

# 创建必要目录
echo "📁 创建目录..."
mkdir -p ./models/train
mkdir -p ./models/merged
mkdir -p ./logs
mkdir -p ./data
mkdir -p ./reports

# 下载模型 (如果不存在)
if [ ! -f "./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors" ]; then
    echo "📥 下载基础模型..."
    python download_model.py
else
    echo "✅ 基础模型已存在"
fi

# 准备数据集 (如果不存在)
if [ ! -f "./data/metadata.csv" ]; then
    echo "📂 准备数据集..."
    python prepare_dataset.py
else
    echo "✅ 数据集已存在"
fi

# 训练优化版LoRA
echo "🏋️ 开始优化版LoRA训练..."
if [ ! -f "./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors" ]; then
    accelerate launch --config_file configs/accelerate_config.yaml train_lora_optimized.py
    if [ $? -eq 0 ]; then
        echo "✅ 优化版训练成功"
    else
        echo "❌ 优化版训练失败"
        exit 1
    fi
else
    echo "✅ 优化版LoRA已存在"
fi

# 训练极限版LoRA
echo "🏋️ 开始极限版LoRA训练..."
if [ ! -f "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors" ]; then
    python train_lora_minimal.py
    if [ $? -eq 0 ]; then
        echo "✅ 极限版训练成功"
    else
        echo "❌ 极限版训练失败"
        exit 1
    fi
else
    echo "✅ 极限版LoRA已存在"
fi

# 合并权重
echo "🔄 开始权重合并..."
python merge_lora_weights.py
if [ $? -eq 0 ]; then
    echo "✅ 权重合并成功"
else
    echo "❌ 权重合并失败"
    exit 1
fi

# 测试合并模型
echo "🧪 开始模型测试..."
python test_merged_models.py
if [ $? -eq 0 ]; then
    echo "✅ 模型测试成功"
else
    echo "❌ 模型测试失败"
    exit 1
fi

echo "⏰ 结束时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "🎉 Wan2.1-T2V-1.3B LoRA微调流程全部完成！"

# 显示结果统计
echo ""
echo "📊 训练结果统计:"
echo "  - 优化版LoRA: $(ls -lh ./models/train/Wan2.1-T2V-1.3B_lora_optimized/*.safetensors | wc -l) 个文件"
echo "  - 极限版LoRA: $(ls -lh ./models/train/Wan2.1-T2V-1.3B_lora_minimal/*.safetensors | wc -l) 个文件"
echo "  - 合并模型: $(ls -lh ./models/merged/*.safetensors | wc -l) 个文件"
echo "  - 日志文件: $(ls -lh ./logs/*.log | wc -l) 个文件"

echo ""
echo "📁 重要文件路径:"
echo "  - 优化版LoRA: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/"
echo "  - 极限版LoRA: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/"
echo "  - 合并模型: ./models/merged/"
echo "  - 训练日志: ./logs/"
echo "  - 对比报告: ./reports/merged_models_comparison.md"
```

### 8. 环境检查脚本 - `check_environment.py`

```python
#!/usr/bin/env python3
"""
环境检查脚本
验证所有依赖和硬件环境是否正确配置
"""

import os
import sys
import subprocess
import importlib
import torch

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    print(f"  Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 12:
        print("  ✅ Python版本符合要求")
        return True
    else:
        print("  ❌ Python版本过低，需要3.12+")
        return False

def check_cuda_environment():
    """检查CUDA环境"""
    print("\n🔧 检查CUDA环境...")
    
    try:
        # 检查nvidia-smi
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("  ✅ NVIDIA驱动正常")
            
            # 解析GPU信息
            lines = result.stdout.split('\n')
            gpu_count = 0
            for line in lines:
                if 'GeForce RTX' in line or 'Tesla' in line or 'Quadro' in line:
                    gpu_count += 1
                    print(f"  🎮 发现GPU: {line.split('|')[1].strip()}")
            
            print(f"  📊 GPU数量: {gpu_count}")
            
        else:
            print("  ❌ nvidia-smi命令失败")
            return False
            
    except FileNotFoundError:
        print("  ❌ nvidia-smi命令不存在")
        return False
    
    # 检查PyTorch CUDA支持
    if torch.cuda.is_available():
        print(f"  ✅ PyTorch CUDA支持: {torch.version.cuda}")
        print(f"  📊 PyTorch检测到GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            print(f"  🎮 GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        return True
    else:
        print("  ❌ PyTorch无法使用CUDA")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print("\n📦 检查Python包...")
    
    required_packages = {
        'torch': '2.0.0',
        'torchvision': '0.15.0',
        'transformers': '4.34.0',
        'accelerate': '0.20.0',
        'peft': '0.4.0',
        'safetensors': '0.3.1',
        'modelscope': '1.9.5',
        'opencv-python': '4.5.0',
        'deepspeed': '0.9.0',
    }
    
    all_installed = True
    
    for package, min_version in required_packages.items():
        try:
            if package == 'opencv-python':
                import cv2
                version = cv2.__version__
                package_name = 'opencv-python'
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                package_name = package
            
            print(f"  ✅ {package_name}: {version}")
            
        except ImportError:
            print(f"  ❌ {package}: 未安装")
            all_installed = False
    
    return all_installed

def check_disk_space():
    """检查磁盘空间"""
    print("\n💾 检查磁盘空间...")
    
    import shutil
    
    total, used, free = shutil.disk_usage("./")
    
    total_gb = total // (1024**3)
    used_gb = used // (1024**3)
    free_gb = free // (1024**3)
    
    print(f"  📊 总空间: {total_gb}GB")
    print(f"  📊 已使用: {used_gb}GB")
    print(f"  📊 可用空间: {free_gb}GB")
    
    if free_gb >= 100:  # 需要至少100GB
        print("  ✅ 磁盘空间充足")
        return True
    else:
        print("  ❌ 磁盘空间不足，需要至少100GB")
        return False

def check_model_files():
    """检查模型文件"""
    print("\n🤖 检查模型文件...")
    
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    required_files = [
        "diffusion_pytorch_model.safetensors",
        "models_t5_umt5-xxl-enc-bf16.pth",
        "Wan2.1_VAE.pth",
        "config.json",
        "model_index.json"
    ]
    
    all_exist = True
    
    for filename in required_files:
        filepath = os.path.join(model_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath) / (1024**3)  # GB
            print(f"  ✅ {filename}: {size:.2f}GB")
        else:
            print(f"  ❌ {filename}: 不存在")
            all_exist = False
    
    if not all_exist:
        print("  💡 提示: 运行 python download_model.py 下载模型")
    
    return all_exist

def check_data_files():
    """检查数据文件"""
    print("\n📂 检查数据文件...")
    
    metadata_path = "./data/metadata.csv"
    video_dir = "./data/videos"
    
    if os.path.exists(metadata_path):
        print(f"  ✅ metadata.csv: 存在")
        
        # 检查视频文件
        if os.path.exists(video_dir):
            video_files = [f for f in os.listdir(video_dir) 
                          if f.endswith(('.mp4', '.avi', '.mov'))]
            print(f"  📹 视频文件: {len(video_files)}个")
            
            if len(video_files) > 0:
                print("  ✅ 数据集准备完成")
                return True
            else:
                print("  ❌ 未找到视频文件")
                return False
        else:
            print("  ❌ 视频目录不存在")
            return False
    else:
        print("  ❌ metadata.csv不存在")
        print("  💡 提示: 运行 python prepare_dataset.py 准备数据集")
        return False

def check_accelerate_config():
    """检查Accelerate配置"""
    print("\n⚡ 检查Accelerate配置...")
    
    config_path = "./configs/accelerate_config.yaml"
    
    if os.path.exists(config_path):
        print("  ✅ accelerate_config.yaml: 存在")
        
        # 尝试验证配置
        try:
            from accelerate import Accelerator
            accelerator = Accelerator()
            print(f"  ✅ Accelerator初始化成功")
            print(f"  📊 进程数: {accelerator.num_processes}")
            print(f"  🎯 混合精度: {accelerator.mixed_precision}")
            return True
        except Exception as e:
            print(f"  ❌ Accelerator初始化失败: {str(e)}")
            return False
    else:
        print("  ❌ accelerate_config.yaml不存在")
        print("  💡 提示: 运行 accelerate config 创建配置")
        return False

def main():
    """主函数"""
    print("🔍 开始环境检查...")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("CUDA环境", check_cuda_environment),
        ("Python包", check_required_packages),
        ("磁盘空间", check_disk_space),
        ("模型文件", check_model_files),
        ("数据文件", check_data_files),
        ("Accelerate配置", check_accelerate_config),
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"  ❌ 检查失败: {str(e)}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 环境检查总结:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 检查结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 环境检查全部通过，可以开始训练！")
        return True
    else:
        print("⚠️ 部分检查失败，请根据提示修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

## 代码文件使用说明

### 执行顺序
1. `check_environment.py` - 检查环境
2. `download_model.py` - 下载模型
3. `prepare_dataset.py` - 准备数据集
4. `train_lora_optimized.py` - 训练优化版LoRA
5. `train_lora_minimal.py` - 训练极限版LoRA
6. `merge_lora_weights.py` - 合并权重
7. `test_merged_models.py` - 测试模型

### 一键执行
```bash
# 使用一键启动脚本
chmod +x run_training.sh
./run_training.sh
```

### 单独执行
```bash
# 检查环境
python check_environment.py

# 下载模型
python download_model.py

# 训练LoRA
accelerate launch --config_file configs/accelerate_config.yaml train_lora_optimized.py
python train_lora_minimal.py

# 合并权重
python merge_lora_weights.py
```

所有代码文件都包含详细的注释和错误处理，可以根据需要进行修改和扩展。
