# 模型下载详细指南

## 模型概述

### Wan2.1-T2V-1.3B 模型信息
- **模型名称**: Wan2.1-T2V-1.3B (文本到视频生成模型)
- **参数规模**: 1.3B (13亿参数)
- **模型类型**: Diffusion Transformer (DiT)
- **总大小**: 约17GB
- **来源**: ModelScope平台

### 模型组件
1. **DiT主模型**: `diffusion_pytorch_model.safetensors` (5.29GB)
2. **T5文本编码器**: `models_t5_umt5-xxl-enc-bf16.pth` (10.58GB)
3. **VAE编码器**: `Wan2.1_VAE.pth` (0.47GB)
4. **配置文件**: `config.json`, `model_index.json` 等

## 下载方法

### 方法一: 使用ModelScope SDK (推荐)

```python
# 创建下载脚本 下载模型.py
from modelscope import snapshot_download
import os

def download_wan_model():
    """下载Wan2.1-T2V-1.3B模型"""
    
    print("🚀 开始下载Wan2.1-T2V-1.3B模型...")
    
    # 设置下载目录
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    os.makedirs(model_dir, exist_ok=True)
    
    try:
        # 下载模型
        model_path = snapshot_download(
            model_id="Wan-AI/Wan2.1-T2V-1.3B",
            cache_dir="./models",
            local_dir=model_dir
        )
        
        print(f"✅ 模型下载成功: {model_path}")
        return model_path
        
    except Exception as e:
        print(f"❌ 模型下载失败: {str(e)}")
        return None

if __name__ == "__main__":
    download_wan_model()
```

运行下载:
```bash
python 下载模型.py
```

### 方法二: 使用Git LFS

```bash
# 安装git-lfs
git lfs install

# 克隆模型仓库
git clone https://www.modelscope.cn/Wan-AI/Wan2.1-T2V-1.3B.git ./models/Wan-AI/Wan2.1-T2V-1.3B
```

### 方法三: 手动下载 (备用方案)

如果自动下载失败，可以手动下载各个文件:

```bash
# 创建目录
mkdir -p ./models/Wan-AI/Wan2.1-T2V-1.3B

# 使用wget下载 (需要替换为实际URL)
wget -O ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors \
     "https://modelscope.cn/api/v1/models/Wan-AI/Wan2.1-T2V-1.3B/repo/files/download?file_path=diffusion_pytorch_model.safetensors"
```

## 下载验证

### 创建验证脚本

```python
# 创建 验证模型.py
import os
from safetensors.torch import load_file
import torch

def verify_model_files():
    """验证模型文件完整性"""
    
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    # 预期文件列表
    expected_files = {
        "diffusion_pytorch_model.safetensors": 5.29,  # GB
        "models_t5_umt5-xxl-enc-bf16.pth": 10.58,
        "Wan2.1_VAE.pth": 0.47,
        "config.json": 0.001,
        "model_index.json": 0.001
    }
    
    print("🔍 验证模型文件...")
    
    all_valid = True
    total_size = 0
    
    for filename, expected_size in expected_files.items():
        filepath = os.path.join(model_dir, filename)
        
        if os.path.exists(filepath):
            actual_size = os.path.getsize(filepath) / (1024**3)  # GB
            total_size += actual_size
            
            if abs(actual_size - expected_size) < 0.1:  # 允许0.1GB误差
                print(f"  ✅ {filename}: {actual_size:.2f}GB")
            else:
                print(f"  ⚠️  {filename}: {actual_size:.2f}GB (预期{expected_size:.2f}GB)")
                all_valid = False
        else:
            print(f"  ❌ {filename}: 文件不存在")
            all_valid = False
    
    print(f"\n📊 总大小: {total_size:.2f}GB")
    
    if all_valid:
        print("✅ 所有文件验证通过")
    else:
        print("❌ 部分文件验证失败")
    
    return all_valid

def test_model_loading():
    """测试模型加载"""
    
    model_dir = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    
    print("\n🧪 测试模型加载...")
    
    try:
        # 测试SafeTensors文件
        dit_path = os.path.join(model_dir, "diffusion_pytorch_model.safetensors")
        if os.path.exists(dit_path):
            dit_weights = load_file(dit_path)
            print(f"  ✅ DiT模型加载成功: {len(dit_weights)}个权重张量")
        
        # 测试PyTorch文件
        t5_path = os.path.join(model_dir, "models_t5_umt5-xxl-enc-bf16.pth")
        if os.path.exists(t5_path):
            t5_weights = torch.load(t5_path, map_location='cpu')
            print(f"  ✅ T5编码器加载成功")
        
        vae_path = os.path.join(model_dir, "Wan2.1_VAE.pth")
        if os.path.exists(vae_path):
            vae_weights = torch.load(vae_path, map_location='cpu')
            print(f"  ✅ VAE编码器加载成功")
        
        print("✅ 模型加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 验证文件
    files_ok = verify_model_files()
    
    # 测试加载
    if files_ok:
        loading_ok = test_model_loading()
    else:
        print("⚠️ 跳过加载测试 (文件验证失败)")
```

运行验证:
```bash
python 验证模型.py
```

**预期输出:**
```
🔍 验证模型文件...
  ✅ diffusion_pytorch_model.safetensors: 5.29GB
  ✅ models_t5_umt5-xxl-enc-bf16.pth: 10.58GB
  ✅ Wan2.1_VAE.pth: 0.47GB
  ✅ config.json: 0.00GB
  ✅ model_index.json: 0.00GB

📊 总大小: 16.34GB
✅ 所有文件验证通过

🧪 测试模型加载...
  ✅ DiT模型加载成功: 825个权重张量
  ✅ T5编码器加载成功
  ✅ VAE编码器加载成功
✅ 模型加载测试通过
```

## 下载优化

### 网络优化

```bash
# 设置下载超时
export MODELSCOPE_DOWNLOAD_TIMEOUT=3600

# 使用多线程下载
export MODELSCOPE_DOWNLOAD_PARALLEL=4

# 设置缓存目录
export MODELSCOPE_CACHE_DIR=./models_cache
```

### 断点续传

ModelScope SDK支持断点续传，如果下载中断，重新运行下载脚本即可继续。

### 存储空间检查

```python
import shutil

def check_disk_space():
    """检查磁盘空间"""
    
    total, used, free = shutil.disk_usage("./")
    
    print(f"磁盘空间:")
    print(f"  总空间: {total // (1024**3)} GB")
    print(f"  已使用: {used // (1024**3)} GB") 
    print(f"  可用空间: {free // (1024**3)} GB")
    
    if free < 20 * (1024**3):  # 20GB
        print("⚠️ 可用空间不足20GB，建议清理磁盘")
        return False
    else:
        print("✅ 磁盘空间充足")
        return True

check_disk_space()
```

## 常见问题解决

### 问题1: 下载速度慢
**解决方案:**
```bash
# 使用代理
export https_proxy=http://proxy:port
export http_proxy=http://proxy:port

# 或使用镜像站点
pip install modelscope -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题2: 下载中断
**解决方案:**
- ModelScope SDK支持断点续传，直接重新运行即可
- 检查网络连接和磁盘空间
- 尝试分批下载单个文件

### 问题3: 文件损坏
**解决方案:**
```bash
# 删除损坏文件重新下载
rm ./models/Wan-AI/Wan2.1-T2V-1.3B/damaged_file.safetensors

# 重新运行下载脚本
python 下载模型.py
```

### 问题4: 权限问题
**解决方案:**
```bash
# 修改目录权限
chmod -R 755 ./models/

# 确保有写入权限
ls -la ./models/
```

## 模型文件结构

下载完成后的目录结构:
```
./models/Wan-AI/Wan2.1-T2V-1.3B/
├── diffusion_pytorch_model.safetensors    # 主要的DiT模型权重
├── models_t5_umt5-xxl-enc-bf16.pth       # T5文本编码器
├── Wan2.1_VAE.pth                        # VAE视频编码器
├── config.json                           # 模型配置
├── model_index.json                      # 模型索引
├── README.md                             # 模型说明
└── tokenizer/                            # 分词器文件
    ├── special_tokens_map.json
    ├── tokenizer.json
    └── tokenizer_config.json
```

## 下载日志记录

创建下载日志:
```python
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    filename=f'模型下载_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 在下载函数中添加日志
logging.info("开始下载模型")
logging.info(f"模型下载完成: {model_path}")
```

## 下一步

模型下载完成后，请继续查看:
- `04_数据集准备指南.md`
- `05_多卡配置详解.md`
- `06_LoRA训练详细流程.md`
