# TensorBoard可视化指南

## 概述

TensorBoard是TensorFlow提供的可视化工具，可以用于监控和分析深度学习模型的训练过程。本指南详细介绍如何在Wan2.1-T2V-1.3B LoRA微调项目中使用TensorBoard进行训练可视化。

## 快速开始

### 1. 一键启动训练和TensorBoard

```bash
# 给脚本执行权限
chmod +x start_training_with_tensorboard.sh

# 启动训练和TensorBoard
./start_training_with_tensorboard.sh
```

### 2. 手动启动

```bash
# 启动训练（后台）
python train_lora_with_tensorboard.py &

# 启动TensorBoard
python tensorboard_manager.py start --logdir ./tensorboard_logs
```

### 3. 访问TensorBoard

在浏览器中打开：`http://localhost:6006`

## 可视化内容详解

### 📊 标量指标 (Scalars)

#### 损失监控
- **`Loss/Train`** - 训练损失曲线
- **`Loss/Validation`** - 验证损失曲线  
- **`Loss/Epoch_Average`** - 每轮平均损失

```python
# 记录损失
logger.log_scalar("Loss/Train", loss.item(), step)
logger.log_scalar("Loss/Validation", val_loss.item(), step)
```

#### 学习率监控
- **`Learning_Rate`** - 当前学习率
- **`Learning_Rate/Base`** - 基础学习率
- **`Learning_Rate/Effective`** - 有效学习率

#### 梯度监控
- **`Gradient_Norm`** - 梯度范数
- **`Gradient_Norm/Clipped`** - 裁剪后的梯度范数

#### 性能监控
- **`Performance/Samples_Per_Second`** - 每秒处理样本数
- **`Performance/Steps_Per_Second`** - 每秒训练步数

#### 显存监控
- **`Memory/GPU_0/Allocated_GB`** - GPU显存分配量
- **`Memory/GPU_0/Reserved_GB`** - GPU显存保留量
- **`Memory/GPU_0/Utilization_%`** - GPU显存利用率

### 📈 直方图 (Histograms)

#### 参数分布
- **`Parameters/`** - 模型参数的分布情况
- **`Gradients/`** - 参数梯度的分布情况

```python
# 记录参数直方图
for name, param in model.named_parameters():
    if param.requires_grad:
        logger.log_histogram(f"Parameters/{name}", param.data, step)
        logger.log_histogram(f"Gradients/{name}", param.grad.data, step)
```

### 📊 自定义图表 (Figures)

#### 训练曲线图
- **`Plots/Loss_Curve`** - 损失曲线图
- **`Plots/Learning_Rate`** - 学习率调度图
- **`Plots/Gradient_Norms`** - 梯度范数图

```python
# 创建损失曲线图
fig = create_loss_plot(train_losses, "Training Loss")
logger.log_figure("Plots/Loss_Curve", fig, step)
```

#### 训练总结图
- **`Summary/Training_Summary`** - 综合训练指标图表

### 🎛️ 超参数 (HParams)

记录和比较不同超参数配置的效果：

```python
hparams = {
    "lora_rank": 16,
    "lora_alpha": 32,
    "learning_rate": 1e-4,
    "batch_size": 1,
}

metrics = {
    "final_loss": final_loss,
    "min_loss": min_loss,
    "avg_loss": avg_loss,
}

logger.log_hyperparameters(hparams, metrics)
```

## TensorBoard界面使用

### 1. 标量页面 (Scalars)

- **左侧面板**：选择要显示的指标
- **主面板**：显示指标曲线图
- **设置选项**：
  - `Smoothing`：平滑曲线
  - `Ignore outliers`：忽略异常值
  - `Tooltip sorting`：工具提示排序

### 2. 直方图页面 (Histograms)

- 显示参数和梯度的分布变化
- 可以观察训练过程中权重的演化
- 帮助诊断梯度消失/爆炸问题

### 3. 图像页面 (Images)

- 显示训练过程中的图像数据
- 可以查看生成的视频帧
- 观察模型输出的质量变化

### 4. 图表页面 (Graphs)

- 显示模型的计算图结构
- 帮助理解模型架构
- 调试模型连接问题

### 5. 超参数页面 (HParams)

- 比较不同超参数配置
- 并行坐标图显示参数关系
- 散点图显示参数与指标的关系

## 高级功能

### 1. 多实验对比

```bash
# 启动多个实验的TensorBoard
tensorboard --logdir_spec=exp1:./logs/exp1,exp2:./logs/exp2
```

### 2. 远程访问

```bash
# 绑定到所有网络接口
tensorboard --logdir ./tensorboard_logs --host 0.0.0.0 --port 6006
```

### 3. 数据导出

```python
# 导出标量数据
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

ea = EventAccumulator('./tensorboard_logs/events.out.tfevents.xxx')
ea.Reload()

# 获取损失数据
loss_data = ea.Scalars('Loss/Train')
```

## 常见问题解决

### 1. TensorBoard无法启动

```bash
# 检查端口占用
netstat -tulpn | grep 6006

# 使用其他端口
python tensorboard_manager.py start --port 6007
```

### 2. 数据不更新

```bash
# 重启TensorBoard
python tensorboard_manager.py restart
```

### 3. 显存不足

```python
# 减少日志记录频率
if step % 10 == 0:  # 每10步记录一次
    logger.log_scalar("Loss/Train", loss, step)
```

### 4. 日志文件过大

```bash
# 清理旧日志
find ./tensorboard_logs -name "events.out.tfevents.*" -mtime +7 -delete
```

## 最佳实践

### 1. 合理的记录频率

```python
# 不同指标使用不同频率
LOGGING_FREQUENCY = {
    "every_step": ["loss", "learning_rate"],
    "every_10_steps": ["memory", "gradients"],
    "every_epoch": ["validation_metrics"],
}
```

### 2. 有意义的标签

```python
# 使用层次化的标签名
logger.log_scalar("Loss/Train/CrossEntropy", ce_loss, step)
logger.log_scalar("Loss/Train/Regularization", reg_loss, step)
```

### 3. 添加描述信息

```python
# 记录配置信息
config_text = f"""
## 训练配置
- LoRA Rank: {config['lora_rank']}
- Learning Rate: {config['learning_rate']}
- Batch Size: {config['batch_size']}
"""
logger.writer.add_text("Config/Training", config_text, 0)
```

### 4. 定期保存检查点

```python
# 在重要节点保存状态
if step % save_steps == 0:
    logger.log_scalar("Checkpoints/Saved", 1, step)
    save_checkpoint(model, optimizer, step)
```

## 性能优化

### 1. 减少I/O开销

```python
# 批量记录标量
logger.log_scalars("Training", {
    "loss": loss.item(),
    "lr": scheduler.get_last_lr()[0],
    "grad_norm": grad_norm,
}, step)
```

### 2. 异步记录

```python
import threading

def async_log(logger, tag, value, step):
    def log_func():
        logger.log_scalar(tag, value, step)
    
    thread = threading.Thread(target=log_func)
    thread.start()
```

### 3. 内存管理

```python
# 及时清理图表对象
fig = create_plot(data)
logger.log_figure("Plot", fig, step)
plt.close(fig)  # 重要：释放内存
```

## 脚本使用说明

### TensorBoard管理器

```bash
# 启动TensorBoard
python tensorboard_manager.py start --logdir ./tensorboard_logs

# 停止TensorBoard
python tensorboard_manager.py stop

# 重启TensorBoard
python tensorboard_manager.py restart

# 检查状态
python tensorboard_manager.py status

# 列出日志目录
python tensorboard_manager.py list
```

### 训练脚本

```bash
# 启动带TensorBoard的训练
python train_lora_with_tensorboard.py

# 一键启动（推荐）
./start_training_with_tensorboard.sh
```

## 总结

TensorBoard为LoRA训练提供了强大的可视化能力，通过合理使用各种图表和指标，可以：

1. **实时监控训练进度** - 观察损失下降和收敛情况
2. **诊断训练问题** - 发现梯度爆炸、消失等问题
3. **优化超参数** - 比较不同配置的效果
4. **分析模型行为** - 理解参数和激活的分布变化
5. **记录实验结果** - 保存完整的训练历史

通过本指南提供的脚本和配置，您可以轻松地为Wan2.1-T2V-1.3B LoRA微调项目添加完整的可视化支持。
