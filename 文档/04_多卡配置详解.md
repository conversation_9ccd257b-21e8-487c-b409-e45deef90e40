# 多卡配置详解

## Accelerate多卡训练配置

### 配置概述
本项目使用Hugging Face Accelerate库实现多卡训练，支持RTX 3090双卡配置。

### 硬件环境
- **GPU**: 2x NVIDIA GeForce RTX 3090
- **显存**: 24GB × 2 = 48GB总显存
- **通信**: PCIe 4.0 x16
- **拓扑**: 单机多卡

## 配置文件详解

### 创建Accelerate配置

```bash
# 交互式配置 (推荐)
accelerate config

# 或使用预设配置文件
accelerate config --config_file ./configs/accelerate_config.yaml
```

### 标准多卡配置

创建 `configs/accelerate_config.yaml`:

```yaml
# Accelerate多卡训练配置
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU        # 多GPU分布式训练
downcast_bf16: 'no'
gpu_ids: all                       # 使用所有可用GPU
machine_rank: 0                    # 主机排名 (单机为0)
main_training_function: main       # 主训练函数名
mixed_precision: bf16              # BF16混合精度
num_machines: 1                    # 机器数量
num_processes: 2                   # 进程数 (等于GPU数量)
rdzv_backend: static              # 集合后端
same_network: true                # 同一网络
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false                    # 不使用CPU训练
```

### 高级配置选项

```yaml
# 高级多卡配置
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
enable_cpu_affinity: false        # CPU亲和性
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false

# 额外的环境变量
env:
  NCCL_DEBUG: INFO               # NCCL调试信息
  NCCL_TREE_THRESHOLD: 0         # NCCL树形通信阈值
  CUDA_LAUNCH_BLOCKING: 1        # CUDA同步执行
```

## 显存分析与优化

### 显存需求计算

```python
# 显存需求分析脚本
def calculate_memory_requirements():
    """计算训练显存需求"""
    
    # 模型参数
    model_params = 1.3e9  # 1.3B参数
    param_size = 2  # BF16每个参数2字节
    
    # 基础模型显存
    model_memory = model_params * param_size / (1024**3)  # GB
    print(f"模型权重: {model_memory:.2f} GB")
    
    # LoRA参数 (rank=16, 4个模块, 30层)
    lora_params = 16 * 1536 * 2 * 4 * 30  # A和B矩阵
    lora_memory = lora_params * param_size / (1024**3)
    print(f"LoRA参数: {lora_memory:.2f} GB")
    
    # 激活值 (取决于batch size和序列长度)
    batch_size = 1
    sequence_length = 16  # 视频帧数
    hidden_size = 1536
    activation_memory = batch_size * sequence_length * hidden_size * 4 / (1024**3)
    print(f"激活值: {activation_memory:.2f} GB")
    
    # 梯度 (等于LoRA参数大小)
    gradient_memory = lora_memory
    print(f"梯度: {gradient_memory:.2f} GB")
    
    # 优化器状态 (Adam需要2倍参数)
    optimizer_memory = lora_memory * 2
    print(f"优化器状态: {optimizer_memory:.2f} GB")
    
    # 总需求
    total_memory = model_memory + lora_memory + activation_memory + gradient_memory + optimizer_memory
    print(f"总显存需求: {total_memory:.2f} GB")
    
    # 多卡分布
    per_gpu_memory = total_memory / 2  # 双卡
    print(f"单卡需求: {per_gpu_memory:.2f} GB")
    
    return total_memory, per_gpu_memory

calculate_memory_requirements()
```

**输出示例:**
```
模型权重: 2.60 GB
LoRA参数: 0.02 GB
激活值: 0.01 GB
梯度: 0.02 GB
优化器状态: 0.04 GB
总显存需求: 2.69 GB
单卡需求: 1.35 GB
```

### 显存优化策略

#### 1. 分辨率优化 (最关键)
```python
# 不同分辨率的显存需求
resolutions = [
    (256, 448),   # 极限配置
    (320, 576),   # 优化配置  
    (480, 832),   # 标准配置 (显存不足)
]

for h, w in resolutions:
    pixels = h * w
    memory_factor = pixels / (256 * 448)  # 相对于最小分辨率
    print(f"{h}x{w}: 显存需求约为基准的 {memory_factor:.1f}x")
```

#### 2. LoRA配置优化
```python
# 不同LoRA配置的参数量
configs = [
    {"rank": 8, "modules": ["q", "k"], "name": "极限版"},
    {"rank": 16, "modules": ["q", "k", "v", "o"], "name": "优化版"},
    {"rank": 32, "modules": ["q", "k", "v", "o", "gate", "up"], "name": "完整版"},
]

for config in configs:
    rank = config["rank"]
    num_modules = len(config["modules"])
    num_layers = 30
    
    # 每层每模块的参数量: rank * hidden_size * 2 (A和B矩阵)
    params_per_module = rank * 1536 * 2
    total_params = params_per_module * num_modules * num_layers
    memory_mb = total_params * 2 / (1024**2)  # BF16
    
    print(f"{config['name']}: {total_params:,}参数, {memory_mb:.1f}MB")
```

#### 3. 批处理大小优化
```python
# 批处理大小对显存的影响
batch_sizes = [1, 2, 4]
base_memory = 12.0  # GB

for bs in batch_sizes:
    estimated_memory = base_memory * bs
    print(f"Batch Size {bs}: 约{estimated_memory:.1f}GB显存")
    
    if estimated_memory > 24:
        print(f"  ⚠️ 超出单卡显存限制")
    else:
        print(f"  ✅ 在单卡显存范围内")
```

## 通信优化

### NCCL配置
```bash
# 设置NCCL环境变量
export NCCL_DEBUG=INFO
export NCCL_TREE_THRESHOLD=0
export NCCL_IB_DISABLE=1          # 禁用InfiniBand
export NCCL_P2P_DISABLE=1         # 禁用P2P通信 (如果有问题)
export NCCL_SHM_DISABLE=1         # 禁用共享内存 (如果有问题)
```

### 通信拓扑检测
```python
import torch

def check_gpu_topology():
    """检查GPU通信拓扑"""
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    num_gpus = torch.cuda.device_count()
    print(f"🔍 检测到 {num_gpus} 个GPU")
    
    for i in range(num_gpus):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
        print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    
    # 检查P2P通信
    if num_gpus > 1:
        print("\n🔗 P2P通信检查:")
        for i in range(num_gpus):
            for j in range(num_gpus):
                if i != j:
                    can_access = torch.cuda.can_device_access_peer(i, j)
                    print(f"  GPU {i} -> GPU {j}: {'✅' if can_access else '❌'}")

check_gpu_topology()
```

## 训练脚本适配

### 基础训练脚本结构
```python
# train_lora_multi_gpu.py
from accelerate import Accelerator
import torch

def main():
    # 初始化Accelerator
    accelerator = Accelerator(
        mixed_precision="bf16",
        gradient_accumulation_steps=1,
        log_with="tensorboard",
        project_dir="./logs"
    )
    
    # 设备信息
    print(f"🚀 使用设备: {accelerator.device}")
    print(f"📊 进程数: {accelerator.num_processes}")
    print(f"🔢 进程排名: {accelerator.process_index}")
    
    # 模型和数据准备
    model = load_model()
    train_dataloader = create_dataloader()
    optimizer = torch.optim.AdamW(model.parameters())
    
    # 使用accelerator包装
    model, optimizer, train_dataloader = accelerator.prepare(
        model, optimizer, train_dataloader
    )
    
    # 训练循环
    for epoch in range(num_epochs):
        for batch in train_dataloader:
            with accelerator.accumulate(model):
                outputs = model(**batch)
                loss = outputs.loss
                
                accelerator.backward(loss)
                optimizer.step()
                optimizer.zero_grad()
        
        # 保存检查点
        if accelerator.is_main_process:
            accelerator.save_state(f"checkpoint-epoch-{epoch}")

if __name__ == "__main__":
    main()
```

### 启动多卡训练
```bash
# 使用accelerate启动
accelerate launch --config_file configs/accelerate_config.yaml train_lora_multi_gpu.py

# 或使用默认配置
accelerate launch --multi_gpu --num_processes=2 train_lora_multi_gpu.py
```

## 性能监控

### GPU利用率监控
```bash
# 实时监控GPU状态
watch -n 1 nvidia-smi

# 或使用gpustat
pip install gpustat
gpustat -i 1
```

### 训练性能分析
```python
import time
import torch

class PerformanceMonitor:
    def __init__(self):
        self.start_time = None
        self.step_times = []
    
    def start_step(self):
        torch.cuda.synchronize()
        self.start_time = time.time()
    
    def end_step(self):
        torch.cuda.synchronize()
        step_time = time.time() - self.start_time
        self.step_times.append(step_time)
        return step_time
    
    def get_stats(self):
        if not self.step_times:
            return {}
        
        return {
            "avg_step_time": sum(self.step_times) / len(self.step_times),
            "min_step_time": min(self.step_times),
            "max_step_time": max(self.step_times),
            "total_steps": len(self.step_times)
        }

# 在训练循环中使用
monitor = PerformanceMonitor()

for batch in train_dataloader:
    monitor.start_step()
    
    # 训练代码
    outputs = model(**batch)
    loss = outputs.loss
    accelerator.backward(loss)
    optimizer.step()
    optimizer.zero_grad()
    
    step_time = monitor.end_step()
    print(f"步骤时间: {step_time:.2f}秒")
```

## 故障排除

### 常见问题及解决方案

#### 1. NCCL初始化失败
```bash
# 错误: NCCL initialization failed
# 解决: 设置NCCL环境变量
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=1
```

#### 2. 显存不足
```bash
# 错误: CUDA out of memory
# 解决: 降低分辨率或batch size
# 或使用梯度累积
```

#### 3. 进程挂起
```bash
# 错误: 训练进程挂起
# 解决: 检查GPU通信
nvidia-smi topo -m

# 或禁用P2P通信
export NCCL_P2P_DISABLE=1
```

#### 4. 速度慢
```bash
# 检查GPU利用率
nvidia-smi dmon -s pucvmet -d 1

# 检查数据加载
# 增加DataLoader的num_workers
```

## 配置验证

### 验证脚本
```python
# 验证多卡配置
def verify_multi_gpu_setup():
    """验证多卡训练配置"""
    
    from accelerate import Accelerator
    
    accelerator = Accelerator()
    
    print(f"✅ Accelerator初始化成功")
    print(f"📱 设备: {accelerator.device}")
    print(f"🔢 进程数: {accelerator.num_processes}")
    print(f"🏷️ 进程排名: {accelerator.process_index}")
    print(f"🎯 是否主进程: {accelerator.is_main_process}")
    print(f"🔄 混合精度: {accelerator.mixed_precision}")
    
    # 测试简单的多GPU操作
    if accelerator.num_processes > 1:
        x = torch.randn(100, 100, device=accelerator.device)
        y = torch.mm(x, x)
        print(f"✅ GPU计算测试通过")
    
    return True

verify_multi_gpu_setup()
```

## 下一步

多卡配置完成后，请继续查看:
- `05_LoRA训练详细流程.md`
- `06_问题解决指南.md`
- `07_性能优化技巧.md`
