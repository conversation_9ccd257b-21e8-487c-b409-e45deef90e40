# 环境搭建详细指南

## 系统环境要求

### 硬件要求
- **GPU**: NVIDIA RTX 3090 或更高 (建议24GB+ 显存)
- **内存**: 32GB+ 系统内存
- **存储**: 100GB+ 可用空间
- **网络**: 稳定的互联网连接 (用于下载模型)

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **CUDA**: 12.0+ 
- **Python**: 3.12.x
- **Conda**: Miniconda 或 Anaconda

## 详细安装步骤

### 第一步: 检查系统环境

```bash
# 检查CUDA版本
nvidia-smi

# 检查Python版本
python --version

# 检查可用空间
df -h
```

**预期输出示例:**
```
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 550.127.05             Driver Version: 550.127.05     CUDA Version: 12.4  |
|-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
|   0  NVIDIA GeForce RTX 3090        Off |   00000000:01:00.0 Off |                  N/A |
|   1  NVIDIA GeForce RTX 3090        Off |   00000000:41:00.0 Off |                  N/A |
+-----------------------------------------------------------------------------------------+
```

### 第二步: 创建Python环境

```bash
# 创建专用环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env

# 验证Python版本
python --version
# 应该显示: Python 3.12.11
```

### 第三步: 设置网络代理 (如需要)

```bash
# 如果在国内环境，可能需要设置代理
source /path/to/your/proxy.sh
proxy_on
```

### 第四步: 克隆项目代码

```bash
# 克隆DiffSynth-Studio
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 查看项目结构
ls -la
```

### 第五步: 安装核心依赖

```bash
# 安装DiffSynth-Studio
pip install -e .
```

**安装过程中的关键包:**
- torch-2.7.1+cu126
- torchvision-0.20.1+cu126
- transformers-4.53.2
- accelerate-1.9.0
- safetensors-0.5.3
- modelscope-1.28.0
- cupy-cuda12x-13.5.1
- opencv-python-*********

### 第六步: 安装训练专用依赖

```bash
# 安装LoRA训练和多卡训练依赖
pip install deepspeed peft
```

**关键依赖版本:**
- deepspeed-0.17.2
- peft-0.16.0

### 第七步: 验证安装

创建验证脚本 `验证环境.py`:

```python
#!/usr/bin/env python3
import torch
import diffsynth
import accelerate
import deepspeed
import peft

print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')

# 显示GPU信息
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
        print(f'GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)')
```

运行验证:
```bash
python 验证环境.py
```

**预期输出:**
```
✅ 所有依赖安装成功
PyTorch版本: 2.7.1+cu126
CUDA可用: True
GPU数量: 2
GPU 0: NVIDIA GeForce RTX 3090 (23.7 GB)
GPU 1: NVIDIA GeForce RTX 3090 (23.7 GB)
```

## 环境配置优化

### CUDA内存管理优化

在 `~/.bashrc` 中添加:
```bash
# CUDA内存优化
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=1
```

### Python路径配置

```bash
# 添加项目路径到PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

## 常见问题解决

### 问题1: CUDA版本不匹配
**症状**: `RuntimeError: CUDA runtime error`
**解决**: 
```bash
# 检查CUDA版本
nvcc --version
nvidia-smi

# 重新安装对应版本的PyTorch
pip uninstall torch torchvision
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121
```

### 问题2: 显存不足
**症状**: `CUDA out of memory`
**解决**: 
```bash
# 清理显存
python -c "import torch; torch.cuda.empty_cache()"

# 设置显存增长模式
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
```

### 问题3: 网络连接问题
**症状**: 下载超时或连接失败
**解决**:
```bash
# 使用镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ package_name

# 或设置代理
export https_proxy=http://proxy:port
```

### 问题4: 权限问题
**症状**: `Permission denied`
**解决**:
```bash
# 修改文件权限
chmod +x script_name.sh

# 或使用sudo (不推荐用于pip)
sudo chown -R $USER:$USER /path/to/directory
```

## 环境验证清单

- [ ] CUDA驱动正常 (nvidia-smi显示正常)
- [ ] Python 3.12环境创建成功
- [ ] DiffSynth-Studio安装成功
- [ ] PyTorch CUDA支持正常
- [ ] 双GPU识别正常
- [ ] 网络连接正常
- [ ] 存储空间充足 (>100GB)
- [ ] 所有依赖包安装成功

## 性能基准测试

运行简单的GPU测试:
```python
import torch
import time

# 测试GPU性能
device = torch.device('cuda:0')
x = torch.randn(1000, 1000, device=device)
start_time = time.time()
for _ in range(100):
    y = torch.mm(x, x)
end_time = time.time()
print(f'GPU计算时间: {end_time - start_time:.2f}秒')
```

## 环境备份

创建环境备份:
```bash
# 导出环境配置
conda env export > wan_video_env.yml

# 导出pip依赖
pip freeze > requirements.txt
```

恢复环境:
```bash
# 从配置文件恢复
conda env create -f wan_video_env.yml

# 或使用pip
pip install -r requirements.txt
```

## 下一步

环境搭建完成后，请继续查看:
- `03_模型下载指南.md`
- `04_数据集准备指南.md`
- `05_多卡配置详解.md`
