# LoRA训练详细流程

## LoRA微调概述

### 什么是LoRA
LoRA (Low-Rank Adaptation) 是一种参数高效的微调方法，通过添加低秩矩阵来调整预训练模型的权重，而不直接修改原始权重。

### 优势
- **显存高效**: 只需训练少量参数 (原模型的<1%)
- **存储高效**: 微调权重文件很小 (MB级别)
- **部署灵活**: 可以动态加载不同的LoRA权重
- **训练快速**: 训练时间大幅减少

## 训练准备

### 数据集准备

```python
# 创建metadata.csv
import os
import pandas as pd

def prepare_dataset():
    """准备训练数据集"""
    
    # 数据目录
    data_dir = "./data/videos"
    os.makedirs(data_dir, exist_ok=True)
    
    # 检查视频文件
    video_files = [f for f in os.listdir(data_dir) if f.endswith(('.mp4', '.avi', '.mov'))]
    
    if not video_files:
        print("❌ 未找到视频文件")
        return False
    
    # 创建metadata
    data = []
    for video_file in video_files:
        video_path = os.path.join(data_dir, video_file)
        # 这里可以添加视频描述或提示词
        prompt = "a beautiful sunset over the mountains"
        
        data.append({
            "video_path": video_path,
            "prompt": prompt
        })
    
    # 保存为CSV
    df = pd.DataFrame(data)
    csv_path = "./data/metadata.csv"
    df.to_csv(csv_path, index=False)
    
    print(f"✅ 数据集准备完成: {len(data)}个视频")
    print(f"📄 元数据保存至: {csv_path}")
    
    return True

prepare_dataset()
```

### 训练配置

创建训练配置文件 `configs/lora_config.py`:

```python
# LoRA训练配置

# 基础模型配置
MODEL_CONFIG = {
    "model_path": "./models/Wan-AI/Wan2.1-T2V-1.3B",
    "model_type": "Wan2.1-T2V-1.3B",
}

# 优化版LoRA配置
OPTIMIZED_LORA_CONFIG = {
    "r": 16,                      # LoRA秩
    "lora_alpha": 32,             # LoRA alpha
    "target_modules": ["q", "k", "v", "o"],  # 目标模块
    "lora_dropout": 0.1,          # LoRA dropout
    "bias": "none",               # 偏置处理方式
    "modules_to_save": None,      # 额外保存的模块
    "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_optimized",
}

# 极限版LoRA配置
MINIMAL_LORA_CONFIG = {
    "r": 8,                       # 更小的秩
    "lora_alpha": 16,             # 更小的alpha
    "target_modules": ["q", "k"], # 只训练q和k
    "lora_dropout": 0.05,         # 更小的dropout
    "bias": "none",
    "modules_to_save": None,
    "output_dir": "./models/train/Wan2.1-T2V-1.3B_lora_minimal",
}

# 训练超参数
TRAINING_ARGS = {
    # 基础训练参数
    "num_train_epochs": 3,        # 训练轮数
    "per_device_train_batch_size": 1,  # 每个设备的批大小
    "gradient_accumulation_steps": 1,  # 梯度累积步数
    "learning_rate": 1e-4,        # 学习率
    "lr_scheduler_type": "cosine", # 学习率调度器
    "warmup_steps": 5,            # 预热步数
    
    # 优化器参数
    "optim": "adamw_torch",       # 优化器
    "weight_decay": 0.01,         # 权重衰减
    "adam_beta1": 0.9,            # Adam beta1
    "adam_beta2": 0.999,          # Adam beta2
    "adam_epsilon": 1e-8,         # Adam epsilon
    
    # 日志和保存
    "logging_steps": 1,           # 日志记录间隔
    "save_steps": 25,             # 保存间隔
    "save_total_limit": 3,        # 最多保存几个检查点
    
    # 混合精度训练
    "bf16": True,                 # 使用BF16
    "fp16": False,                # 不使用FP16
    
    # 其他参数
    "seed": 42,                   # 随机种子
    "dataloader_num_workers": 2,  # 数据加载器工作进程数
    "remove_unused_columns": False, # 保留所有列
}

# 视频处理参数
VIDEO_PARAMS = {
    # 优化版配置
    "optimized": {
        "width": 320,             # 视频宽度
        "height": 576,            # 视频高度
        "frames": 16,             # 帧数
        "fps": 8,                 # 帧率
    },
    
    # 极限版配置
    "minimal": {
        "width": 256,             # 更小的宽度
        "height": 448,            # 更小的高度
        "frames": 16,             # 帧数
        "fps": 8,                 # 帧率
    }
}
```

## 训练脚本

创建LoRA训练脚本 `train_lora.py`:

```python
#!/usr/bin/env python3
"""
Wan2.1-T2V-1.3B LoRA训练脚本
"""

import os
import torch
import pandas as pd
import numpy as np
from datetime import datetime
from accelerate import Accelerate
from peft import LoraConfig, get_peft_model
from transformers import TrainingArguments, Trainer
from diffsynth.models import load_model
from diffsynth.data import VideoDataset

# 导入配置
from configs.lora_config import (
    MODEL_CONFIG, 
    OPTIMIZED_LORA_CONFIG, 
    MINIMAL_LORA_CONFIG,
    TRAINING_ARGS,
    VIDEO_PARAMS
)

def setup_logging(config_name):
    """设置日志记录"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"logs/training_{config_name}_{timestamp}.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # 简单的日志函数
    def log(message):
        with open(log_file, "a") as f:
            time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{time_str}] {message}\n")
        print(message)
    
    return log

def train_lora(config_name="optimized"):
    """训练LoRA模型"""
    
    # 选择配置
    if config_name == "optimized":
        lora_config = OPTIMIZED_LORA_CONFIG
        video_params = VIDEO_PARAMS["optimized"]
    else:
        lora_config = MINIMAL_LORA_CONFIG
        video_params = VIDEO_PARAMS["minimal"]
    
    # 设置日志
    log = setup_logging(config_name)
    
    log(f"🚀 开始{config_name}版LoRA训练...")
    log(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化Accelerate
    accelerator = Accelerate()
    
    log(f"🖥️ 设备: {accelerator.device}")
    log(f"🔢 进程数: {accelerator.num_processes}")
    log(f"🏷️ 进程排名: {accelerator.process_index}")
    
    # 加载模型
    log("📥 加载基础模型...")
    model = load_model(MODEL_CONFIG["model_path"])
    
    # 配置LoRA
    log("🔧 配置LoRA...")
    lora_config_obj = LoraConfig(
        r=lora_config["r"],
        lora_alpha=lora_config["lora_alpha"],
        target_modules=lora_config["target_modules"],
        lora_dropout=lora_config["lora_dropout"],
        bias=lora_config["bias"],
        modules_to_save=lora_config["modules_to_save"]
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config_obj)
    
    # 打印可训练参数
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    log(f"📊 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
    
    # 准备数据集
    log("📂 准备数据集...")
    metadata_path = "./data/metadata.csv"
    if not os.path.exists(metadata_path):
        log(f"❌ 元数据文件不存在: {metadata_path}")
        return False
    
    metadata = pd.read_csv(metadata_path)
    log(f"📄 加载元数据: {len(metadata)}个样本")
    
    # 创建数据集
    dataset = VideoDataset(
        metadata=metadata,
        width=video_params["width"],
        height=video_params["height"],
        frames=video_params["frames"],
        fps=video_params["fps"]
    )
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=lora_config["output_dir"],
        **TRAINING_ARGS
    )
    
    # 创建Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
    )
    
    # 开始训练
    log("🏋️ 开始训练...")
    trainer.train()
    
    # 保存模型
    log("💾 保存模型...")
    trainer.save_model()
    
    log(f"✅ 训练完成!")
    log(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="LoRA训练脚本")
    parser.add_argument("--config", type=str, default="optimized", 
                        choices=["optimized", "minimal"],
                        help="训练配置: optimized或minimal")
    
    args = parser.parse_args()
    train_lora(args.config)
```

## 训练执行

### 优化版训练 (双卡)

```bash
# 使用Accelerate启动优化版训练
accelerate launch --config_file configs/accelerate_config.yaml train_lora.py --config optimized
```

### 极限版训练 (单卡)

```bash
# 极限版训练 (单卡足够)
python train_lora.py --config minimal
```

## 训练监控

### 实时监控

```bash
# 监控GPU状态
watch -n 1 nvidia-smi

# 监控训练日志
tail -f logs/training_optimized_*.log
```

### TensorBoard监控

```bash
# 启动TensorBoard
tensorboard --logdir ./models/train/

# 在浏览器中打开
# http://localhost:6006/
```

## 训练结果分析

### 检查LoRA权重

```python
# 分析LoRA权重
from safetensors.torch import load_file
import torch
import matplotlib.pyplot as plt
import numpy as np

def analyze_lora_weights(lora_path):
    """分析LoRA权重"""
    
    print(f"🔍 分析LoRA权重: {lora_path}")
    
    # 加载权重
    weights = load_file(lora_path)
    
    # 统计信息
    num_weights = len(weights)
    weight_types = {}
    
    for key in weights.keys():
        # 分类权重
        if ".lora_A." in key:
            weight_type = "lora_A"
        elif ".lora_B." in key:
            weight_type = "lora_B"
        else:
            weight_type = "other"
        
        weight_types[weight_type] = weight_types.get(weight_type, 0) + 1
    
    print(f"📊 权重统计:")
    print(f"  - 总权重数: {num_weights}")
    print(f"  - 权重类型: {weight_types}")
    
    # 计算参数量
    total_params = 0
    for tensor in weights.values():
        total_params += tensor.numel()
    
    print(f"  - 总参数量: {total_params:,}")
    
    # 分析权重分布
    all_values = []
    for tensor in weights.values():
        all_values.extend(tensor.flatten().tolist())
    
    all_values = np.array(all_values)
    
    print(f"  - 权重范围: [{all_values.min():.6f}, {all_values.max():.6f}]")
    print(f"  - 平均值: {all_values.mean():.6f}")
    print(f"  - 标准差: {all_values.std():.6f}")
    
    # 绘制直方图
    plt.figure(figsize=(10, 6))
    plt.hist(all_values, bins=50)
    plt.title(f"LoRA权重分布")
    plt.xlabel("权重值")
    plt.ylabel("频率")
    plt.grid(True, alpha=0.3)
    
    # 保存图表
    plot_path = f"{os.path.splitext(lora_path)[0]}_distribution.png"
    plt.savefig(plot_path)
    print(f"📈 权重分布图已保存: {plot_path}")
    
    return True

# 分析优化版LoRA
analyze_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors")

# 分析极限版LoRA
analyze_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors")
```

### 训练性能分析

```python
# 分析训练日志
import re
import matplotlib.pyplot as plt

def analyze_training_log(log_path):
    """分析训练日志"""
    
    print(f"📊 分析训练日志: {log_path}")
    
    # 读取日志
    with open(log_path, "r") as f:
        log_content = f.read()
    
    # 提取训练时间
    step_times = []
    for line in log_content.split("\n"):
        if "it/s]" in line:
            match = re.search(r"(\d+\.\d+)s/it", line)
            if match:
                step_time = float(match.group(1))
                step_times.append(step_time)
    
    if not step_times:
        print("❌ 未找到训练时间数据")
        return False
    
    # 计算统计信息
    avg_time = sum(step_times) / len(step_times)
    min_time = min(step_times)
    max_time = max(step_times)
    
    print(f"⏱️ 训练时间统计:")
    print(f"  - 平均步骤时间: {avg_time:.2f}秒/步")
    print(f"  - 最短步骤时间: {min_time:.2f}秒/步")
    print(f"  - 最长步骤时间: {max_time:.2f}秒/步")
    print(f"  - 总步骤数: {len(step_times)}")
    
    # 绘制训练时间图表
    plt.figure(figsize=(12, 6))
    plt.plot(step_times)
    plt.title("训练步骤时间")
    plt.xlabel("步骤")
    plt.ylabel("时间 (秒/步)")
    plt.grid(True, alpha=0.3)
    
    # 保存图表
    plot_path = f"{os.path.splitext(log_path)[0]}_performance.png"
    plt.savefig(plot_path)
    print(f"📈 性能图表已保存: {plot_path}")
    
    return True

# 分析优化版训练日志
analyze_training_log("./logs/training_optimized_20250722_150200.log")

# 分析极限版训练日志
analyze_training_log("./logs/training_minimal_20250722_151204.log")
```

## 常见问题解决

### 显存不足

**问题**: `CUDA out of memory`

**解决方案**:
1. 降低分辨率 (最有效)
2. 减小LoRA秩 (r)
3. 减少目标模块数量
4. 使用梯度累积
5. 清理显存缓存

```python
# 清理显存
torch.cuda.empty_cache()

# 设置显存增长模式
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
```

### 训练不稳定

**问题**: 损失值波动大或NaN

**解决方案**:
1. 降低学习率
2. 增加预热步数
3. 使用梯度裁剪
4. 检查数据预处理

```python
# 在训练参数中添加
training_args = TrainingArguments(
    # ...其他参数
    max_grad_norm=1.0,  # 梯度裁剪
    learning_rate=5e-5,  # 降低学习率
    warmup_steps=10,     # 增加预热步数
)
```

### 多卡同步问题

**问题**: 多卡训练时进程卡住

**解决方案**:
1. 检查NCCL配置
2. 禁用P2P通信
3. 使用单卡训练

```bash
# 禁用P2P通信
export NCCL_P2P_DISABLE=1

# 或使用单卡训练
CUDA_VISIBLE_DEVICES=0 python train_lora.py
```

## 下一步

LoRA训练完成后，请继续查看:
- `06_LoRA推理测试指南.md`
- `07_权重合并详解.md`
- `08_合并模型测试指南.md`
