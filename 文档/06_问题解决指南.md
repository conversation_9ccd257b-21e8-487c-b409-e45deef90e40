# 问题解决指南

## 显存相关问题

### 问题1: CUDA out of memory

**症状描述:**
```
RuntimeError: CUDA out of memory. Tried to allocate 2.00 GiB. GPU 0 has a total capacity of 23.69 GiB of which 1.56 GiB is free.
```

**根本原因:**
- 模型参数 + 激活值 + 梯度 + 优化器状态超出GPU显存
- 批处理大小过大
- 视频分辨率过高
- LoRA配置过于复杂

**解决方案 (按优先级排序):**

#### 1. 降低视频分辨率 (最有效)
```python
# 从 480x832 降到 320x576
VIDEO_PARAMS = {
    "width": 320,    # 原来480
    "height": 576,   # 原来832
    "frames": 16,
    "fps": 8,
}

# 极限情况下降到 256x448
MINIMAL_VIDEO_PARAMS = {
    "width": 256,
    "height": 448,
    "frames": 16,
    "fps": 8,
}
```

#### 2. 优化LoRA配置
```python
# 减小LoRA秩
LORA_CONFIG = {
    "r": 8,          # 原来16或32
    "lora_alpha": 16, # 原来32
    "target_modules": ["q", "k"],  # 原来["q", "k", "v", "o"]
}
```

#### 3. 减小批处理大小
```python
TRAINING_ARGS = {
    "per_device_train_batch_size": 1,  # 确保为1
    "gradient_accumulation_steps": 2,  # 增加梯度累积
}
```

#### 4. 设置显存优化环境变量
```bash
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=1
```

#### 5. 清理显存缓存
```python
import torch
import gc

def clear_memory():
    """清理显存和内存"""
    torch.cuda.empty_cache()
    gc.collect()
    
    # 显示当前显存使用
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
            print(f"GPU {i}: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")

# 在训练前调用
clear_memory()
```

### 问题2: 显存碎片化

**症状描述:**
```
CUDA out of memory. Tried to allocate 80.00 MiB. GPU 0 has a total capacity of 23.69 GiB of which 80.81 MiB is free.
```

**解决方案:**
```bash
# 设置显存分配策略
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

# 或在Python中设置
import os
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
```

## 多卡训练问题

### 问题3: NCCL初始化失败

**症状描述:**
```
RuntimeError: NCCL error in: /opt/conda/conda-bld/pytorch_1639180139634/work/torch/csrc/distributed/c10d/ProcessGroupNCCL.cpp:825, unhandled system error
```

**解决方案:**

#### 1. 设置NCCL环境变量
```bash
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1
export NCCL_SHM_DISABLE=1
```

#### 2. 检查GPU拓扑
```python
import torch

def check_gpu_topology():
    """检查GPU通信拓扑"""
    
    num_gpus = torch.cuda.device_count()
    print(f"检测到 {num_gpus} 个GPU")
    
    for i in range(num_gpus):
        for j in range(num_gpus):
            if i != j:
                can_access = torch.cuda.can_device_access_peer(i, j)
                print(f"GPU {i} -> GPU {j}: {'可通信' if can_access else '不可通信'}")

check_gpu_topology()
```

#### 3. 使用单卡训练 (备用方案)
```bash
# 强制使用单卡
CUDA_VISIBLE_DEVICES=0 python train_lora.py

# 或在代码中设置
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
```

### 问题4: 多进程挂起

**症状描述:**
- 训练进程启动后卡住不动
- 没有错误信息，但也没有进度

**解决方案:**

#### 1. 检查进程状态
```bash
# 查看GPU进程
nvidia-smi

# 查看Python进程
ps aux | grep python

# 杀死卡住的进程
pkill -f "train_lora.py"
```

#### 2. 使用超时机制
```python
import signal
import time

class TimeoutHandler:
    def __init__(self, timeout):
        self.timeout = timeout
    
    def __enter__(self):
        signal.signal(signal.SIGALRM, self._timeout_handler)
        signal.alarm(self.timeout)
        return self
    
    def __exit__(self, type, value, traceback):
        signal.alarm(0)
    
    def _timeout_handler(self, signum, frame):
        raise TimeoutError(f"操作超时 ({self.timeout}秒)")

# 使用示例
try:
    with TimeoutHandler(300):  # 5分钟超时
        # 训练代码
        trainer.train()
except TimeoutError:
    print("训练超时，切换到单卡模式")
```

## 模型加载问题

### 问题5: 模型文件损坏

**症状描述:**
```
RuntimeError: Error(s) in loading state_dict for WanModel:
Missing key(s) in state_dict: ...
```

**解决方案:**

#### 1. 验证文件完整性
```python
import hashlib
import os

def verify_file_integrity(file_path, expected_size_gb=None):
    """验证文件完整性"""
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(file_path) / (1024**3)
    print(f"📊 文件大小: {file_size:.2f}GB")
    
    if expected_size_gb and abs(file_size - expected_size_gb) > 0.1:
        print(f"⚠️ 文件大小异常，期望{expected_size_gb:.2f}GB")
        return False
    
    # 尝试加载文件
    try:
        if file_path.endswith('.safetensors'):
            from safetensors.torch import load_file
            weights = load_file(file_path)
            print(f"✅ SafeTensors文件加载成功: {len(weights)}个张量")
        elif file_path.endswith('.pth'):
            import torch
            weights = torch.load(file_path, map_location='cpu')
            print(f"✅ PyTorch文件加载成功")
        else:
            print(f"⚠️ 未知文件格式: {file_path}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件加载失败: {str(e)}")
        return False

# 验证关键模型文件
model_files = [
    ("./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors", 5.29),
    ("./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth", 10.58),
    ("./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth", 0.47),
]

for file_path, expected_size in model_files:
    verify_file_integrity(file_path, expected_size)
```

#### 2. 重新下载损坏文件
```python
from modelscope import snapshot_download

def redownload_model():
    """重新下载模型"""
    
    print("🔄 重新下载模型...")
    
    try:
        model_path = snapshot_download(
            model_id="Wan-AI/Wan2.1-T2V-1.3B",
            cache_dir="./models",
            local_dir="./models/Wan-AI/Wan2.1-T2V-1.3B",
            force_download=True  # 强制重新下载
        )
        print(f"✅ 模型重新下载成功: {model_path}")
        return True
    except Exception as e:
        print(f"❌ 模型重新下载失败: {str(e)}")
        return False
```

### 问题6: 权重键名不匹配

**症状描述:**
```
RuntimeError: Error(s) in loading state_dict for WanModel:
Unexpected key(s) in state_dict: "lora_A.default.weight", "lora_B.default.weight"
```

**解决方案:**

#### 1. 检查LoRA权重结构
```python
def analyze_lora_structure(lora_path):
    """分析LoRA权重结构"""
    
    from safetensors.torch import load_file
    
    weights = load_file(lora_path)
    
    print(f"🔍 LoRA权重结构分析: {lora_path}")
    print(f"📊 总权重数: {len(weights)}")
    
    # 分析键名模式
    key_patterns = {}
    for key in weights.keys():
        # 提取模式
        if ".lora_A." in key:
            pattern = "lora_A"
        elif ".lora_B." in key:
            pattern = "lora_B"
        else:
            pattern = "other"
        
        key_patterns[pattern] = key_patterns.get(pattern, 0) + 1
    
    print(f"📋 键名模式: {key_patterns}")
    
    # 显示前几个键名
    print("🔑 示例键名:")
    for i, key in enumerate(list(weights.keys())[:5]):
        print(f"  {i+1}. {key}")
    
    return weights

# 分析LoRA权重
analyze_lora_structure("./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors")
```

#### 2. 修复权重键名
```python
def fix_lora_keys(lora_path, output_path):
    """修复LoRA权重键名"""
    
    from safetensors.torch import load_file, save_file
    
    weights = load_file(lora_path)
    fixed_weights = {}
    
    for key, tensor in weights.items():
        # 修复键名格式
        if ".lora_A.default.weight" in key:
            new_key = key.replace(".lora_A.default.weight", ".lora_A.weight")
        elif ".lora_B.default.weight" in key:
            new_key = key.replace(".lora_B.default.weight", ".lora_B.weight")
        else:
            new_key = key
        
        fixed_weights[new_key] = tensor
    
    # 保存修复后的权重
    save_file(fixed_weights, output_path)
    print(f"✅ 权重键名修复完成: {output_path}")
    
    return True
```

## 训练过程问题

### 问题7: 训练损失不下降

**症状描述:**
- 损失值一直很高，不下降
- 或者损失值变成NaN

**解决方案:**

#### 1. 检查学习率
```python
# 降低学习率
TRAINING_ARGS = {
    "learning_rate": 5e-5,  # 从1e-4降到5e-5
    "lr_scheduler_type": "cosine",
    "warmup_steps": 10,     # 增加预热步数
}
```

#### 2. 添加梯度裁剪
```python
TRAINING_ARGS = {
    # ...其他参数
    "max_grad_norm": 1.0,   # 梯度裁剪
}
```

#### 3. 检查数据预处理
```python
def validate_dataset(dataset):
    """验证数据集"""
    
    print("🔍 验证数据集...")
    
    for i, sample in enumerate(dataset):
        if i >= 3:  # 只检查前3个样本
            break
        
        print(f"样本 {i+1}:")
        
        # 检查视频数据
        if 'video' in sample:
            video = sample['video']
            print(f"  视频形状: {video.shape}")
            print(f"  视频范围: [{video.min():.3f}, {video.max():.3f}]")
            
            # 检查是否有异常值
            if torch.isnan(video).any():
                print(f"  ⚠️ 发现NaN值")
            if torch.isinf(video).any():
                print(f"  ⚠️ 发现无穷值")
        
        # 检查文本数据
        if 'text' in sample:
            text = sample['text']
            print(f"  文本: {text[:50]}...")
    
    print("✅ 数据集验证完成")

# 验证数据集
validate_dataset(train_dataset)
```

### 问题8: 训练速度过慢

**症状描述:**
- 每个步骤耗时过长 (>30秒/步)
- GPU利用率低

**解决方案:**

#### 1. 检查数据加载
```python
TRAINING_ARGS = {
    # ...其他参数
    "dataloader_num_workers": 4,  # 增加数据加载进程
    "dataloader_pin_memory": True, # 使用固定内存
}
```

#### 2. 优化视频预处理
```python
def optimize_video_preprocessing():
    """优化视频预处理"""
    
    # 使用更快的视频解码
    import cv2
    cv2.setNumThreads(4)  # 设置OpenCV线程数
    
    # 预先调整视频大小
    # 而不是在训练时实时调整
```

#### 3. 监控GPU利用率
```python
import subprocess
import time

def monitor_gpu_usage(duration=60):
    """监控GPU使用率"""
    
    print(f"🔍 监控GPU使用率 ({duration}秒)...")
    
    start_time = time.time()
    gpu_usage = []
    
    while time.time() - start_time < duration:
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            usage = [int(x.strip()) for x in result.stdout.strip().split('\n')]
            gpu_usage.append(usage)
            time.sleep(1)
        except:
            break
    
    # 计算平均使用率
    if gpu_usage:
        avg_usage = [sum(gpu[i] for gpu in gpu_usage) / len(gpu_usage) 
                    for i in range(len(gpu_usage[0]))]
        
        for i, usage in enumerate(avg_usage):
            print(f"GPU {i} 平均使用率: {usage:.1f}%")
    
    return gpu_usage

# 在训练期间监控
monitor_gpu_usage(60)
```

## 环境问题

### 问题9: 依赖版本冲突

**症状描述:**
```
ImportError: cannot import name 'xxx' from 'yyy'
```

**解决方案:**

#### 1. 检查依赖版本
```python
def check_dependencies():
    """检查关键依赖版本"""
    
    import torch
    import transformers
    import accelerate
    import peft
    import diffsynth
    
    print("📦 依赖版本检查:")
    print(f"  PyTorch: {torch.__version__}")
    print(f"  Transformers: {transformers.__version__}")
    print(f"  Accelerate: {accelerate.__version__}")
    print(f"  PEFT: {peft.__version__}")
    print(f"  DiffSynth: {diffsynth.__version__}")
    
    # 检查CUDA版本
    if torch.cuda.is_available():
        print(f"  CUDA: {torch.version.cuda}")
        print(f"  cuDNN: {torch.backends.cudnn.version()}")
    
    return True

check_dependencies()
```

#### 2. 重新安装环境
```bash
# 删除现有环境
conda remove -n wan_video_env --all

# 重新创建环境
conda create -n wan_video_env python=3.12 -y
conda activate wan_video_env

# 按顺序安装依赖
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121
pip install transformers accelerate peft
pip install -e .
```

## 调试技巧

### 启用详细日志
```python
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG)

# 或针对特定模块
logging.getLogger("transformers").setLevel(logging.DEBUG)
logging.getLogger("accelerate").setLevel(logging.DEBUG)
```

### 使用调试模式
```bash
# 启用CUDA错误检查
export CUDA_LAUNCH_BLOCKING=1

# 启用PyTorch调试
export TORCH_SHOW_CPP_STACKTRACES=1
```

### 内存泄漏检测
```python
import tracemalloc

# 启动内存跟踪
tracemalloc.start()

# 训练代码...

# 获取内存使用情况
current, peak = tracemalloc.get_traced_memory()
print(f"当前内存使用: {current / 1024 / 1024:.1f} MB")
print(f"峰值内存使用: {peak / 1024 / 1024:.1f} MB")

tracemalloc.stop()
```

## 下一步

问题解决后，请继续查看:
- `07_权重合并详解.md`
- `08_合并模型测试指南.md`
- `09_性能优化技巧.md`
