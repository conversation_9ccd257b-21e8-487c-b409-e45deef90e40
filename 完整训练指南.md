# Wan2.1-T2V-1.3B LoRA训练完整指南

## 📋 目录

1. [项目概述](#项目概述)
2. [环境搭建](#环境搭建)
3. [模型下载](#模型下载)
4. [数据集准备](#数据集准备)
5. [配置文件](#配置文件)
6. [TensorBoard监控](#tensorboard监控)
7. [训练执行](#训练执行)
8. [结果验证](#结果验证)
9. [故障排除](#故障排除)
10. [附录](#附录)

---

## 🎯 项目概述

### 项目目标
使用LoRA (Low-Rank Adaptation) 技术对Wan2.1-T2V-1.3B视频生成模型进行微调，实现个性化的文本到视频生成。

### 技术栈
- **基础模型**: Wan2.1-T2V-1.3B (1.3B参数的文本到视频扩散模型)
- **微调技术**: LoRA (Low-Rank Adaptation)
- **训练框架**: PyTorch + Accelerate (多GPU分布式训练)
- **监控工具**: TensorBoard + 自定义监控脚本
- **环境管理**: Conda

### 硬件要求
- **GPU**: 2x RTX 3090 (24GB显存) 或同等性能
- **内存**: 64GB+ 系统内存
- **存储**: 100GB+ 可用空间
- **网络**: 稳定的互联网连接（用于模型下载）

---

## 🔧 环境搭建

### 1. 创建Conda环境

```bash
# 创建Python 3.12环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env
```

### 2. 安装PyTorch

```bash
# 安装PyTorch (CUDA 12.1版本)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
```

### 3. 安装依赖包

```bash
# 核心依赖
pip install accelerate transformers diffusers

# 监控和可视化
pip install tensorboard matplotlib psutil

# 图像和视频处理
pip install opencv-python pillow

# 其他工具
pip install tqdm numpy scipy
```

### 4. 验证安装

```python
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
for i in range(torch.cuda.device_count()):
    print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
```

### 5. 克隆项目代码

```bash
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio
pip install -e .
```

---

## 📥 模型下载

### 模型组件说明

Wan2.1-T2V-1.3B模型包含三个主要组件：

1. **扩散模型权重** (`diffusion_pytorch_model.safetensors`)
   - 大小: ~2.8GB
   - 用途: 主要的扩散变换器模型
   - 架构: DiT (Diffusion Transformer)

2. **文本编码器** (`models_t5_umt5-xxl-enc-bf16.pth`)
   - 大小: ~4.2GB
   - 用途: T5-XXL文本编码器，理解文本提示
   - 精度: BFloat16

3. **视频VAE** (`Wan2.1_VAE.pth`)
   - 大小: ~1.1GB
   - 用途: 视频变分自编码器，编码/解码视频
   - 支持分辨率: 320x576, 576x320

### 自动下载方式

模型会在训练时自动下载，无需手动操作：

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

model_configs = [
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="diffusion_pytorch_model*.safetensors"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
    ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="Wan2.1_VAE.pth")
]

pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cpu",
    model_configs=model_configs
)
```

### 存储位置

```
./models/Wan-AI/Wan2.1-T2V-1.3B/
├── diffusion_pytorch_model.safetensors  # 扩散模型
├── models_t5_umt5-xxl-enc-bf16.pth     # 文本编码器
├── Wan2.1_VAE.pth                      # 视频VAE
└── config.json                         # 配置文件
```

---

## 📊 数据集准备

### 数据集类型选择

我们提供了三种数据集创建方式：

1. **示例数据集** - 快速测试用的简单数据集
2. **真实动画数据集** - 程序生成的多样化动画视频
3. **增强版数据集** - 针对Wan2.1-T2V-1.3B优化的高质量数据集

### 方法1: 创建真实动画数据集（推荐）

#### 使用自动化脚本创建

```bash
# 创建基础动画数据集（30个视频，10种场景）
python create_t2v_dataset.py

# 创建增强版数据集（25个视频，5种场景，适配576x320分辨率）
python create_enhanced_t2v_dataset.py --resolution 576x320 --fps 8 --duration 2.5 --variants 5
```

#### 真实数据集特点

- **多样化场景**: 海洋、森林、城市、山脉、沙漠等10种不同场景
- **动画效果**: 波浪、树木摇摆、云朵移动、交通灯光等真实动画
- **高质量提示词**: 详细描述场景内容、氛围和视觉效果
- **标准格式**: 符合Wan2.1-T2V-1.3B训练要求的分辨率和帧率

#### 生成的数据集结构

```
data/enhanced_t2v_dataset/
├── metadata.csv              # 训练用CSV文件
├── metadata_full.json        # 完整元数据（包含分类、变体信息）
├── dataset_stats.json        # 数据集统计信息
└── videos/                   # 视频文件目录
    ├── sunset_ocean_v1_00.mp4
    ├── sunset_ocean_v2_00.mp4
    ├── forest_morning_v1_01.mp4
    ├── city_night_v1_02.mp4
    └── ...
```

#### 数据集统计信息

```json
{
  "total_samples": 25,
  "unique_scenes": 5,
  "variants_per_scene": 5,
  "categories": {
    "nature": 10,
    "landscape": 10,
    "urban": 5
  },
  "video_specs": {
    "resolution": "576x320",
    "fps": 8,
    "duration_seconds": 2.5,
    "total_frames": 20
  }
}
```

### 方法2: 示例数据集（快速测试）

#### 数据集结构

```
data/example_video_dataset/
├── metadata.csv          # 元数据文件（必需）
├── videos/               # 视频文件目录
│   ├── video1.mp4
│   ├── video2.mp4
│   └── ...
└── prompts/              # 提示词文件（可选）
    ├── video1.txt
    ├── video2.txt
    └── ...
```

#### metadata.csv 格式

```csv
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"
video2.mp4,"a cat playing in the garden, sunny day, flowers"
video3.mp4,"city street at night, neon lights, cars passing by"
```

### 视频技术要求

#### 推荐规格（针对Wan2.1-T2V-1.3B优化）

- **分辨率**: 576x320 (推荐) 或 320x576
- **帧率**: 8 FPS (推荐) 或 15-30 FPS
- **时长**: 2.5秒 (推荐) 或 2-10秒
- **格式**: MP4 (H.264编码)
- **总帧数**: 20帧 (2.5秒 × 8fps)

#### 兼容规格

- **格式**: MP4, AVI, MOV
- **分辨率**: 320x576, 576x320, 832x480
- **帧率**: 8-30 FPS
- **时长**: 2-10秒
- **编码**: H.264, H.265 (推荐H.264)

### 文本提示词要求

#### 高质量提示词特征

- **长度**: 15-80个词（推荐40-60词）
- **语言**: 英文（推荐）
- **结构**: 主要内容 + 视觉细节 + 氛围描述
- **关键词**: 包含动作、场景、光照、风格等描述

#### 提示词示例

```
优秀示例:
"Beautiful sunset over calm ocean waves, golden hour lighting, peaceful seascape with warm colors"

"Misty forest in early morning with sunlight filtering through trees, peaceful woodland scene"

"Modern city skyline at night with illuminated windows and urban atmosphere, cinematic view"

避免的示例:
"ocean" (过于简单)
"A very beautiful and amazing sunset over the ocean with incredible waves and fantastic lighting that looks absolutely stunning and breathtaking" (过于冗长)
```

### 创建示例数据集

### 方法3: 自定义数据集创建

#### 完整的真实数据集创建脚本

```bash
#!/bin/bash
# 创建Wan2.1-T2V-1.3B真实动画数据集的完整脚本

echo "🎬 开始创建真实动画数据集..."

# 方法1: 创建基础动画数据集
echo "📋 选项1: 创建基础动画数据集（30个视频，10种场景）"
python create_t2v_dataset.py

# 方法2: 创建增强版数据集（推荐）
echo "📋 选项2: 创建增强版数据集（25个视频，5种场景，优化分辨率）"
python create_enhanced_t2v_dataset.py \
  --resolution 576x320 \
  --fps 8 \
  --duration 2.5 \
  --variants 5

# 方法3: 创建高分辨率数据集
echo "📋 选项3: 创建高分辨率数据集（适合高端GPU）"
python create_enhanced_t2v_dataset.py \
  --resolution 832x480 \
  --fps 15 \
  --duration 3.0 \
  --variants 3

echo "✅ 真实动画数据集创建完成！"
echo "📁 可用数据集:"
echo "   - data/t2v_custom_dataset/ (基础版，30个视频)"
echo "   - data/enhanced_t2v_dataset/ (增强版，25个视频，推荐)"
echo ""
echo "🚀 使用增强版数据集训练:"
echo "   accelerate launch examples/wanvideo/model_training/train.py \\"
echo "     --dataset_base_path data/enhanced_t2v_dataset \\"
echo "     --dataset_metadata_path data/enhanced_t2v_dataset/metadata.csv \\"
echo "     --height 320 --width 576 \\"
echo "     --dataset_repeat 200 \\"
echo "     [其他参数...]"
```

#### 示例数据集创建脚本（用于快速测试）

```bash
#!/bin/bash
# 创建简单示例数据集的脚本

echo "📊 开始创建示例数据集..."

# 创建数据集目录结构
mkdir -p data/example_video_dataset/videos
mkdir -p data/example_video_dataset/prompts

# 创建详细的元数据文件
cat > data/example_video_dataset/metadata.csv << EOF
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"
video2.mp4,"a cat playing in the garden, sunny day, flowers blooming"
video3.mp4,"city street at night, neon lights, cars passing by"
video4.mp4,"ocean waves crashing on the beach, seagulls flying"
video5.mp4,"mountain landscape with snow, pine trees, winter scene"
EOF

# 创建对应的提示词文件
cat > data/example_video_dataset/prompts/video1.txt << EOF
from sunset to night, a small town, light, house, river
EOF

cat > data/example_video_dataset/prompts/video2.txt << EOF
a cat playing in the garden, sunny day, flowers blooming
EOF

cat > data/example_video_dataset/prompts/video3.txt << EOF
city street at night, neon lights, cars passing by
EOF

cat > data/example_video_dataset/prompts/video4.txt << EOF
ocean waves crashing on the beach, seagulls flying
EOF

cat > data/example_video_dataset/prompts/video5.txt << EOF
mountain landscape with snow, pine trees, winter scene
EOF

# 创建数据集配置文件
cat > data/example_video_dataset/dataset_config.json << EOF
{
    "dataset_name": "example_video_dataset",
    "version": "1.0",
    "description": "示例视频数据集用于Wan2.1-T2V-1.3B LoRA训练",
    "video_format": "mp4",
    "resolution": "320x576",
    "fps": 8,
    "duration_range": "2-10 seconds",
    "total_videos": 5,
    "language": "English",
    "created_date": "$(date +%Y-%m-%d)",
    "metadata_format": {
        "video": "视频文件名",
        "prompt": "对应的文本描述"
    }
}
EOF

# 创建数据集统计信息
echo "📈 生成数据集统计信息..."
TOTAL_VIDEOS=$(wc -l < data/example_video_dataset/metadata.csv)
TOTAL_VIDEOS=$((TOTAL_VIDEOS - 1))  # 减去标题行

cat > data/example_video_dataset/dataset_stats.txt << EOF
# 数据集统计信息

## 基本信息
- 数据集名称: example_video_dataset
- 创建时间: $(date +"%Y-%m-%d %H:%M:%S")
- 视频总数: $TOTAL_VIDEOS
- 元数据文件: metadata.csv

## 文件结构
data/example_video_dataset/
├── metadata.csv          # 元数据文件
├── dataset_config.json   # 数据集配置
├── dataset_stats.txt     # 统计信息 (本文件)
├── videos/               # 视频文件目录 (需要手动添加视频文件)
└── prompts/              # 提示词文件目录
    ├── video1.txt
    ├── video2.txt
    ├── video3.txt
    ├── video4.txt
    └── video5.txt

## 训练配置
- 数据重复次数: 500 (通过 --dataset_repeat 参数设置)
- 有效训练样本: $TOTAL_VIDEOS × 500 = $((TOTAL_VIDEOS * 500))
- 预计训练时间: 约1-2小时 (取决于硬件配置)

## 注意事项
1. videos/ 目录中需要手动添加对应的视频文件
2. 视频文件名必须与metadata.csv中的文件名一致
3. 推荐视频格式: MP4, 分辨率: 320x576, 时长: 2-10秒
EOF

# 创建视频文件检查脚本
cat > data/example_video_dataset/check_videos.py << 'EOF'
#!/usr/bin/env python3
"""
检查数据集中的视频文件是否存在和格式是否正确
"""

import os
import csv
from pathlib import Path

def check_dataset():
    """检查数据集完整性"""

    dataset_dir = Path("data/example_video_dataset")
    metadata_file = dataset_dir / "metadata.csv"
    videos_dir = dataset_dir / "videos"

    print("🔍 检查数据集完整性...")

    # 检查元数据文件
    if not metadata_file.exists():
        print("❌ metadata.csv 文件不存在")
        return False

    # 读取元数据
    missing_videos = []
    existing_videos = []

    with open(metadata_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            video_file = videos_dir / row['video']
            if video_file.exists():
                existing_videos.append(row['video'])
                # 检查文件大小
                file_size = video_file.stat().st_size / (1024 * 1024)  # MB
                print(f"✅ {row['video']} ({file_size:.2f}MB)")
            else:
                missing_videos.append(row['video'])
                print(f"❌ {row['video']} (文件不存在)")

    # 打印统计信息
    total_videos = len(existing_videos) + len(missing_videos)
    print(f"\n📊 数据集统计:")
    print(f"- 总视频数: {total_videos}")
    print(f"- 存在视频: {len(existing_videos)}")
    print(f"- 缺失视频: {len(missing_videos)}")

    if missing_videos:
        print(f"\n⚠️ 缺失的视频文件:")
        for video in missing_videos:
            print(f"  - {video}")
        print(f"\n💡 请将视频文件放入 {videos_dir} 目录")

    return len(missing_videos) == 0

if __name__ == "__main__":
    success = check_dataset()
    if success:
        print("\n🎉 数据集检查完成，所有文件都存在!")
    else:
        print("\n⚠️ 数据集不完整，请添加缺失的视频文件")
EOF

chmod +x data/example_video_dataset/check_videos.py

echo "✅ 数据集创建完成!"
echo "📁 数据集位置: data/example_video_dataset/"
echo "📊 统计信息: data/example_video_dataset/dataset_stats.txt"
echo "🔍 检查脚本: python data/example_video_dataset/check_videos.py"
echo ""
echo "⚠️ 注意: 请手动将视频文件放入 data/example_video_dataset/videos/ 目录"
echo "📝 视频文件名必须与 metadata.csv 中的文件名一致"
```

#### 执行数据集创建

```bash
# 保存数据集创建脚本
cat > create_dataset.sh << 'EOF'
# [将上面的完整脚本内容粘贴到这里]
EOF

# 添加执行权限并运行
chmod +x create_dataset.sh
./create_dataset.sh

# 检查数据集完整性
python data/example_video_dataset/check_videos.py
```

#### 数据集验证脚本

```python
#!/usr/bin/env python3
"""
验证数据集格式和内容的详细脚本
"""

import os
import csv
import json
from pathlib import Path
from datetime import datetime

def validate_dataset():
    """详细验证数据集"""

    print("🔍 开始详细验证数据集...")

    dataset_dir = Path("data/example_video_dataset")
    metadata_file = dataset_dir / "metadata.csv"
    config_file = dataset_dir / "dataset_config.json"
    videos_dir = dataset_dir / "videos"
    prompts_dir = dataset_dir / "prompts"

    validation_results = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "dataset_dir": str(dataset_dir),
        "files_checked": [],
        "errors": [],
        "warnings": [],
        "summary": {}
    }

    # 1. 检查目录结构
    required_dirs = [dataset_dir, videos_dir, prompts_dir]
    for dir_path in required_dirs:
        if dir_path.exists():
            validation_results["files_checked"].append(f"✅ 目录存在: {dir_path}")
        else:
            validation_results["errors"].append(f"❌ 目录缺失: {dir_path}")

    # 2. 检查元数据文件
    if metadata_file.exists():
        validation_results["files_checked"].append(f"✅ 元数据文件存在: {metadata_file}")

        # 验证CSV格式
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)

                if 'video' not in reader.fieldnames or 'prompt' not in reader.fieldnames:
                    validation_results["errors"].append("❌ metadata.csv 缺少必需的列: video, prompt")
                else:
                    validation_results["files_checked"].append("✅ CSV格式正确")

                # 检查每一行
                for i, row in enumerate(rows, 1):
                    if not row['video']:
                        validation_results["errors"].append(f"❌ 第{i}行: video字段为空")
                    if not row['prompt']:
                        validation_results["errors"].append(f"❌ 第{i}行: prompt字段为空")

                    # 检查提示词长度
                    if len(row['prompt']) < 10:
                        validation_results["warnings"].append(f"⚠️ 第{i}行: 提示词过短 ({len(row['prompt'])}字符)")
                    elif len(row['prompt']) > 200:
                        validation_results["warnings"].append(f"⚠️ 第{i}行: 提示词过长 ({len(row['prompt'])}字符)")

                validation_results["summary"]["total_entries"] = len(rows)

        except Exception as e:
            validation_results["errors"].append(f"❌ 读取metadata.csv失败: {str(e)}")
    else:
        validation_results["errors"].append(f"❌ 元数据文件不存在: {metadata_file}")

    # 3. 检查配置文件
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            validation_results["files_checked"].append("✅ 配置文件格式正确")
        except Exception as e:
            validation_results["errors"].append(f"❌ 配置文件格式错误: {str(e)}")
    else:
        validation_results["warnings"].append(f"⚠️ 配置文件不存在: {config_file}")

    # 4. 检查视频和提示词文件
    if metadata_file.exists():
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                video_count = 0
                prompt_count = 0

                for row in reader:
                    video_file = videos_dir / row['video']
                    prompt_file = prompts_dir / f"{Path(row['video']).stem}.txt"

                    # 检查视频文件
                    if video_file.exists():
                        video_count += 1
                        file_size = video_file.stat().st_size / (1024 * 1024)
                        validation_results["files_checked"].append(f"✅ 视频文件: {row['video']} ({file_size:.2f}MB)")
                    else:
                        validation_results["errors"].append(f"❌ 视频文件缺失: {row['video']}")

                    # 检查提示词文件
                    if prompt_file.exists():
                        prompt_count += 1
                        validation_results["files_checked"].append(f"✅ 提示词文件: {prompt_file.name}")
                    else:
                        validation_results["warnings"].append(f"⚠️ 提示词文件缺失: {prompt_file.name}")

                validation_results["summary"]["video_files_found"] = video_count
                validation_results["summary"]["prompt_files_found"] = prompt_count

        except Exception as e:
            validation_results["errors"].append(f"❌ 检查文件时出错: {str(e)}")

    # 5. 生成验证报告
    report_file = dataset_dir / f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(validation_results, f, indent=2, ensure_ascii=False)

    # 6. 打印结果
    print(f"\n📊 验证结果:")
    print(f"- 检查项目: {len(validation_results['files_checked'])}")
    print(f"- 错误数量: {len(validation_results['errors'])}")
    print(f"- 警告数量: {len(validation_results['warnings'])}")

    if validation_results['errors']:
        print(f"\n❌ 发现错误:")
        for error in validation_results['errors']:
            print(f"  {error}")

    if validation_results['warnings']:
        print(f"\n⚠️ 警告信息:")
        for warning in validation_results['warnings']:
            print(f"  {warning}")

    print(f"\n📄 详细报告已保存: {report_file}")

    return len(validation_results['errors']) == 0

if __name__ == "__main__":
    success = validate_dataset()
    if success:
        print("\n🎉 数据集验证通过!")
    else:
        print("\n❌ 数据集验证失败，请修复错误后重试")
```

---

## ⚙️ 配置文件

### 1. Accelerate配置 (accelerate_config.yaml)

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. LoRA训练参数

```python
# LoRA配置
lora_base_model = "dit"                    # 目标模型
lora_target_modules = "q,k,v,o,ffn.0,ffn.2"  # 目标层
lora_rank = 16                             # 低秩维度
lora_alpha = 32                            # 缩放因子
lora_dropout = 0.1                         # Dropout率

# 训练配置
learning_rate = 1e-4                       # 学习率
num_epochs = 5                             # 训练轮数
batch_size = 1                             # 批大小
gradient_accumulation_steps = 8            # 梯度累积
dataset_repeat = 500                       # 数据重复次数
```

### 3. 视频参数

```python
height = 320                               # 视频高度
width = 576                                # 视频宽度
num_frames = 16                            # 帧数
fps = 8                                    # 帧率
```

---

## 📊 TensorBoard监控

### 监控系统架构

我们创建了多层次的监控系统：

1. **基础训练监控** - 训练损失、学习率等
2. **系统资源监控** - GPU、CPU、内存使用
3. **完整流水线监控** - 从环境搭建到训练完成

### 启动TensorBoard

```bash
# 启动基础监控
tensorboard --logdir ./tensorboard_logs/wan_lora_real_* --port 6007

# 启动系统监控
tensorboard --logdir ./tensorboard_logs/real_time_monitor_* --port 6008

# 启动完整流水线监控
tensorboard --logdir ./tensorboard_logs/complete_demo_* --port 6009
```

### 监控指标说明

#### 训练指标
- **Loss/Train**: 训练损失值
- **Learning_Rate**: 学习率变化
- **Gradient_Norm**: 梯度范数
- **Training/Epoch**: 当前训练轮次

#### 系统资源
- **System/CPU_Usage_%**: CPU使用率
- **System/Memory_Usage_%**: 内存使用率
- **GPU/gpu_0/Memory_Allocated_GB**: GPU 0显存分配
- **GPU/gpu_1/Memory_Allocated_GB**: GPU 1显存分配
- **GPU/gpu_0/Memory_Utilization_%**: GPU 0显存利用率

#### 性能指标
- **Performance/Step_Time_Seconds**: 每步训练时间
- **Performance/Steps_Per_Second**: 训练速度
- **Progress/ETA_Minutes**: 预计剩余时间

### 访问地址

- **基础监控**: http://localhost:6007
- **系统监控**: http://localhost:6008  
- **完整流水线**: http://localhost:6009

---

## 🚀 训练执行

### 方法1: 使用真实动画数据集训练（推荐）

```bash
# 使用真实动画数据集的完整训练脚本
./train_with_real_dataset.sh
```

这个脚本会自动执行：
1. 环境检查和验证
2. **创建真实动画数据集**（25个高质量视频）
3. 根据数据集大小自动调整训练参数
4. Accelerate配置优化
5. TensorBoard监控启动
6. LoRA训练执行
7. 结果验证和对比测试
8. 详细报告生成

#### 真实数据集的优势

- **高质量动画**: 程序生成的流畅动画效果
- **多样化场景**: 日落海洋、森林晨光、城市夜景、山脉黎明、沙漠风沙
- **优化分辨率**: 576x320，8fps，2.5秒，完美适配Wan2.1-T2V-1.3B
- **智能参数调整**: 根据数据集大小自动调整重复次数和训练轮数
- **详细提示词**: 包含场景、光照、氛围的完整描述

### 方法2: 传统示例数据集训练

```bash
# 使用示例数据集的训练脚本
./start_complete_training.sh
```

这个脚本会自动执行：
1. 环境检查
2. 模型文件验证
3. 简单数据集准备
4. Accelerate配置
5. TensorBoard启动
6. LoRA训练执行
7. 结果验证
8. 报告生成

### 方法2: 手动执行

#### 完整的手动执行脚本

```bash
#!/bin/bash
# 手动执行Wan2.1-T2V-1.3B LoRA训练的完整脚本

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 开始手动执行Wan2.1-T2V-1.3B LoRA训练${NC}"

# 1. 激活环境
echo -e "${YELLOW}📋 步骤1: 激活conda环境${NC}"
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 验证环境
python -c "
import torch
print(f'✅ Python环境已激活')
print(f'✅ PyTorch版本: {torch.__version__}')
print(f'✅ CUDA可用: {torch.cuda.is_available()}')
print(f'✅ GPU数量: {torch.cuda.device_count()}')
for i in range(torch.cuda.device_count()):
    print(f'✅ GPU {i}: {torch.cuda.get_device_name(i)}')
"

# 2. 检查和创建必要目录
echo -e "${YELLOW}📋 步骤2: 检查和创建目录${NC}"
mkdir -p data/example_video_dataset
mkdir -p models/train
mkdir -p logs
mkdir -p tensorboard_logs

# 3. 创建数据集（如果不存在）
echo -e "${YELLOW}📋 步骤3: 准备数据集${NC}"
if [ ! -f "data/example_video_dataset/metadata.csv" ]; then
    echo "video,prompt" > data/example_video_dataset/metadata.csv
    echo 'video1.mp4,"from sunset to night, a small town, light, house, river"' >> data/example_video_dataset/metadata.csv
    echo "✅ 创建示例数据集metadata.csv"
else
    echo "✅ 数据集metadata.csv已存在"
fi

# 4. 创建Accelerate配置（如果不存在）
echo -e "${YELLOW}📋 步骤4: 检查Accelerate配置${NC}"
if [ ! -f "accelerate_config.yaml" ]; then
    cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF
    echo "✅ 创建accelerate_config.yaml"
else
    echo "✅ accelerate_config.yaml已存在"
fi

# 5. 启动TensorBoard监控（后台运行）
echo -e "${YELLOW}📋 步骤5: 启动TensorBoard监控${NC}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TENSORBOARD_DIR="./tensorboard_logs/manual_training_${TIMESTAMP}"
mkdir -p "$TENSORBOARD_DIR"

# 启动TensorBoard
nohup tensorboard --logdir "$TENSORBOARD_DIR" --port 6010 --host 0.0.0.0 > logs/tensorboard_${TIMESTAMP}.log 2>&1 &
TENSORBOARD_PID=$!
echo "✅ TensorBoard已启动 (PID: $TENSORBOARD_PID, 端口: 6010)"
echo "🌐 访问地址: http://localhost:6010"

# 6. 启动训练
echo -e "${YELLOW}📋 步骤6: 启动LoRA训练${NC}"
echo "⏰ 开始时间: $(date)"
echo "📝 训练日志将保存到: logs/training_output_${TIMESTAMP}.log"

# 完整的训练命令
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 \
  --width 576 \
  --dataset_repeat 500 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-T2V-1.3B_lora" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 16 \
  --use_gradient_checkpointing_offload 2>&1 | tee logs/training_output_${TIMESTAMP}.log

TRAIN_EXIT_CODE=${PIPESTATUS[0]}

# 7. 训练结果检查
echo -e "${YELLOW}📋 步骤7: 检查训练结果${NC}"
echo "⏰ 结束时间: $(date)"

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ 训练成功完成!${NC}"

    # 检查输出文件
    if [ -d "./models/train/Wan2.1-T2V-1.3B_lora" ]; then
        echo "📁 输出文件:"
        find "./models/train/Wan2.1-T2V-1.3B_lora" -type f -exec ls -lh {} \;
    fi
else
    echo -e "${RED}❌ 训练失败，退出码: $TRAIN_EXIT_CODE${NC}"
    echo "📝 请检查日志文件: logs/training_output_${TIMESTAMP}.log"
fi

# 8. 生成训练报告
echo -e "${YELLOW}📋 步骤8: 生成训练报告${NC}"
cat > logs/manual_training_report_${TIMESTAMP}.md << EOF
# 手动训练执行报告

## 训练概览
- 执行时间: $(date)
- 训练状态: $([ $TRAIN_EXIT_CODE -eq 0 ] && echo "成功" || echo "失败")
- 退出码: $TRAIN_EXIT_CODE
- TensorBoard PID: $TENSORBOARD_PID

## 文件位置
- 训练日志: logs/training_output_${TIMESTAMP}.log
- TensorBoard: $TENSORBOARD_DIR
- 模型输出: ./models/train/Wan2.1-T2V-1.3B_lora

## 访问链接
- TensorBoard: http://localhost:6010

## 后续操作
1. 查看TensorBoard监控数据
2. 验证LoRA权重文件
3. 测试模型推理效果
4. 停止TensorBoard: kill $TENSORBOARD_PID
EOF

echo "📊 训练报告已生成: logs/manual_training_report_${TIMESTAMP}.md"
echo -e "${GREEN}🎉 手动训练流程执行完成!${NC}"

# 保持TensorBoard运行
if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "🔄 TensorBoard继续运行中..."
    echo "💡 停止TensorBoard: kill $TENSORBOARD_PID"
else
    echo "🛑 由于训练失败，停止TensorBoard"
    kill $TENSORBOARD_PID 2>/dev/null || true
fi
```

#### 保存并执行脚本

```bash
# 将上述脚本保存为文件
cat > manual_training.sh << 'EOF'
# [将上面的完整脚本内容粘贴到这里]
EOF

# 添加执行权限
chmod +x manual_training.sh

# 执行脚本
./manual_training.sh
```

### 训练过程监控

训练过程中可以通过以下方式监控：

1. **终端输出**: 实时查看训练进度
2. **TensorBoard**: 图形化监控界面
3. **系统监控**: GPU使用情况
4. **日志文件**: 详细的训练日志

### 预期训练时间对比

#### 真实动画数据集训练
- **数据集大小**: 25个视频 × 200重复 = 5000个训练样本
- **训练轮数**: 6 epochs
- **总步数**: 约1500步 (5000样本 ÷ 2GPU)
- **每步时间**: 约12秒
- **总训练时间**: 约5小时

#### 示例数据集训练
- **数据集大小**: 1个视频 × 500重复 = 500个训练样本
- **训练轮数**: 5 epochs
- **总步数**: 约250步 (500样本 ÷ 2GPU)
- **每步时间**: 约12秒
- **总训练时间**: 约50分钟

### 训练质量对比

| 特性 | 真实动画数据集 | 示例数据集 |
|------|----------------|------------|
| 场景多样性 | ⭐⭐⭐⭐⭐ (5种场景) | ⭐⭐ (1种场景) |
| 动画质量 | ⭐⭐⭐⭐⭐ (流畅动画) | ⭐⭐⭐ (静态为主) |
| 提示词质量 | ⭐⭐⭐⭐⭐ (详细描述) | ⭐⭐⭐ (简单描述) |
| 训练时间 | ⭐⭐⭐ (5小时) | ⭐⭐⭐⭐⭐ (50分钟) |
| 生成效果 | ⭐⭐⭐⭐⭐ (高质量) | ⭐⭐⭐ (基础质量) |
| 泛化能力 | ⭐⭐⭐⭐⭐ (强) | ⭐⭐ (弱) |

---

## ✅ 结果验证

### 检查输出文件

```bash
# 查看输出目录
ls -la ./models/train/Wan2.1-T2V-1.3B_lora/

# 典型输出文件
./models/train/Wan2.1-T2V-1.3B_lora/
├── pytorch_lora_weights.safetensors    # LoRA权重文件
├── adapter_config.json                 # LoRA配置
├── training_args.json                  # 训练参数
└── logs/                              # 训练日志
```

### 验证LoRA权重

```python
import torch
from safetensors import safe_open

# 加载LoRA权重
lora_path = "./models/train/Wan2.1-T2V-1.3B_lora/pytorch_lora_weights.safetensors"

with safe_open(lora_path, framework="pt", device="cpu") as f:
    for key in f.keys():
        tensor = f.get_tensor(key)
        print(f"{key}: {tensor.shape}")
```

### 测试推理

#### 创建推理测试脚本

```python
#!/usr/bin/env python3
"""
Wan2.1-T2V-1.3B LoRA推理测试脚本
测试训练好的LoRA模型的视频生成效果
"""

import torch
import os
import time
from datetime import datetime
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

def test_lora_inference():
    """测试LoRA推理功能"""

    print("🚀 开始LoRA推理测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 检查CUDA可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行推理测试")
        return False

    print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)}")

    try:
        # 1. 加载基础模型
        print("\n📥 步骤1: 加载基础模型...")
        start_time = time.time()

        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda"
        )

        load_time = time.time() - start_time
        print(f"✅ 基础模型加载完成，耗时: {load_time:.1f}秒")

        # 2. 加载LoRA权重
        print("\n🎯 步骤2: 加载LoRA权重...")
        lora_path = "./models/train/Wan2.1-T2V-1.3B_lora"

        if not os.path.exists(lora_path):
            print(f"❌ LoRA权重路径不存在: {lora_path}")
            return False

        # 检查LoRA文件
        lora_files = []
        for file in os.listdir(lora_path):
            if file.endswith('.safetensors') or file.endswith('.bin'):
                lora_files.append(file)
                print(f"📁 找到LoRA文件: {file}")

        if not lora_files:
            print("❌ 未找到LoRA权重文件")
            return False

        # 加载LoRA权重
        pipe.load_lora_weights(lora_path)
        print("✅ LoRA权重加载完成")

        # 3. 准备测试提示词
        test_prompts = [
            "from sunset to night, a small town, light, house, river",
            "a beautiful sunset over mountains",
            "city street at night, neon lights, cars passing by",
            "a cat playing in the garden, sunny day, flowers"
        ]

        # 4. 生成测试视频
        print(f"\n🎬 步骤3: 生成测试视频 (共{len(test_prompts)}个)")

        results = []
        for i, prompt in enumerate(test_prompts):
            print(f"\n📝 测试 {i+1}/{len(test_prompts)}: {prompt}")

            try:
                # 生成视频
                start_time = time.time()

                video = pipe(
                    prompt=prompt,
                    height=320,
                    width=576,
                    num_frames=16,
                    num_inference_steps=50,
                    guidance_scale=7.5
                )

                generation_time = time.time() - start_time

                # 保存视频
                output_filename = f"test_output_{i+1:02d}.mp4"
                video.save(output_filename)

                # 检查文件大小
                file_size = os.path.getsize(output_filename) / (1024 * 1024)  # MB

                result = {
                    'prompt': prompt,
                    'filename': output_filename,
                    'generation_time': generation_time,
                    'file_size_mb': file_size,
                    'success': True
                }

                results.append(result)

                print(f"✅ 生成成功: {output_filename}")
                print(f"⏱️ 生成时间: {generation_time:.1f}秒")
                print(f"📁 文件大小: {file_size:.2f}MB")

            except Exception as e:
                print(f"❌ 生成失败: {str(e)}")
                results.append({
                    'prompt': prompt,
                    'filename': None,
                    'generation_time': 0,
                    'file_size_mb': 0,
                    'success': False,
                    'error': str(e)
                })

        # 5. 生成测试报告
        print("\n📊 步骤4: 生成测试报告...")

        successful_tests = sum(1 for r in results if r['success'])
        total_tests = len(results)
        success_rate = successful_tests / total_tests * 100

        avg_generation_time = sum(r['generation_time'] for r in results if r['success']) / max(successful_tests, 1)
        total_file_size = sum(r['file_size_mb'] for r in results if r['success'])

        # 创建详细报告
        report = f"""
# LoRA推理测试报告

## 测试概览
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总测试数: {total_tests}
- 成功数量: {successful_tests}
- 成功率: {success_rate:.1f}%

## 性能统计
- 平均生成时间: {avg_generation_time:.1f}秒
- 总文件大小: {total_file_size:.2f}MB
- 平均文件大小: {total_file_size/max(successful_tests, 1):.2f}MB

## 详细结果
"""

        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            report += f"""
### 测试 {i}: {status}
- **提示词**: {result['prompt']}
- **输出文件**: {result.get('filename', 'N/A')}
- **生成时间**: {result['generation_time']:.1f}秒
- **文件大小**: {result['file_size_mb']:.2f}MB
"""
            if not result['success']:
                report += f"- **错误信息**: {result.get('error', 'Unknown error')}\n"

        report += f"""
## 系统信息
- GPU: {torch.cuda.get_device_name(0)}
- CUDA版本: {torch.version.cuda}
- PyTorch版本: {torch.__version__}
- 显存使用: {torch.cuda.memory_allocated(0) / (1024**3):.2f}GB

## 输出文件位置
"""

        for result in results:
            if result['success']:
                report += f"- {result['filename']}\n"

        # 保存报告
        report_filename = f"lora_inference_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📊 测试报告已保存: {report_filename}")

        # 打印总结
        print(f"\n🎉 推理测试完成!")
        print(f"✅ 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ 平均生成时间: {avg_generation_time:.1f}秒")
        print(f"📁 输出文件: {[r['filename'] for r in results if r['success']]}")

        return success_rate > 0

    except Exception as e:
        print(f"❌ 推理测试过程中出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    success = test_lora_inference()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
```

#### 执行推理测试

```bash
# 保存推理测试脚本
cat > test_lora_inference.py << 'EOF'
# [将上面的完整Python脚本内容粘贴到这里]
EOF

# 激活环境并执行测试
conda activate wan_video_env
python test_lora_inference.py
```

#### 简化版推理测试

如果只想快速测试，可以使用简化版本：

```python
# 简化版推理测试
import torch
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

print("🚀 开始简化推理测试...")

# 加载模型和LoRA权重
pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device="cuda")
pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora")

# 生成测试视频
prompt = "from sunset to night, a small town, light, house, river"
video = pipe(prompt=prompt, height=320, width=576, num_frames=16, num_inference_steps=50)

# 保存视频
video.save("quick_test_output.mp4")
print("✅ 测试完成，输出文件: quick_test_output.mp4")
```

#### 真实数据集LoRA推理测试

```bash
# 使用真实数据集训练的LoRA进行推理测试
python test_real_dataset_inference.py
```

这个脚本会：
1. 加载真实数据集训练的LoRA权重
2. 使用训练数据集中的提示词进行测试
3. 生成基础模型对比视频
4. 创建详细的测试报告
5. 分析不同场景类型的生成效果

#### 推理测试对比

```python
# 对比测试脚本 - 同时测试两种LoRA模型
import torch
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

def compare_lora_models():
    """对比不同数据集训练的LoRA模型效果"""

    pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device="cuda")

    test_prompt = "Beautiful sunset over calm ocean waves, golden hour lighting"

    # 测试1: 真实数据集LoRA
    print("🎬 测试真实数据集LoRA...")
    pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_real_dataset_lora")
    video1 = pipe(prompt=test_prompt, height=320, width=576, num_frames=16)
    video1.save("comparison_real_dataset.mp4")

    # 测试2: 示例数据集LoRA
    print("🎬 测试示例数据集LoRA...")
    pipe.load_lora_weights("./models/train/Wan2.1-T2V-1.3B_lora")
    video2 = pipe(prompt=test_prompt, height=320, width=576, num_frames=16)
    video2.save("comparison_example_dataset.mp4")

    # 测试3: 基础模型
    print("🎬 测试基础模型...")
    pipe.unload_lora_weights()
    video3 = pipe(prompt=test_prompt, height=320, width=576, num_frames=16)
    video3.save("comparison_base_model.mp4")

    print("✅ 对比测试完成!")
    print("📁 输出文件:")
    print("   - comparison_real_dataset.mp4 (真实数据集LoRA)")
    print("   - comparison_example_dataset.mp4 (示例数据集LoRA)")
    print("   - comparison_base_model.mp4 (基础模型)")

# 执行对比测试
compare_lora_models()
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. CUDA内存不足 (OOM)

**错误信息**: `RuntimeError: CUDA out of memory`

**解决方案**:
```bash
# 减少batch_size
--batch_size 1

# 增加梯度累积步数
--gradient_accumulation_steps 16

# 启用梯度检查点
--use_gradient_checkpointing_offload

# 使用更小的LoRA rank
--lora_rank 8
```

#### 2. 模型下载失败

**错误信息**: `Connection timeout` 或 `Download failed`

**解决方案**:
```bash
# 设置代理（如果需要）
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port

# 手动下载模型文件
wget https://modelscope.cn/models/Wan-AI/Wan2.1-T2V-1.3B/resolve/master/diffusion_pytorch_model.safetensors

# 或使用git lfs
git lfs clone https://www.modelscope.cn/Wan-AI/Wan2.1-T2V-1.3B.git
```

#### 3. 多GPU训练失败

**错误信息**: `NCCL error` 或 `Distributed training failed`

**解决方案**:
```bash
# 检查GPU状态
nvidia-smi

# 重新配置Accelerate
accelerate config

# 使用单GPU训练
num_processes: 1
```

#### 4. 数据集加载错误

**错误信息**: `FileNotFoundError` 或 `Invalid dataset format`

**解决方案**:
```bash
# 检查数据集路径
ls -la data/example_video_dataset/

# 验证metadata.csv格式
head data/example_video_dataset/metadata.csv

# 检查视频文件
ffprobe data/example_video_dataset/video1.mp4
```

#### 5. TensorBoard无法访问

**错误信息**: `Connection refused` 或页面无法加载

**解决方案**:
```bash
# 检查TensorBoard进程
ps aux | grep tensorboard

# 重启TensorBoard
pkill tensorboard
tensorboard --logdir ./tensorboard_logs --port 6006 --host 0.0.0.0

# 检查防火墙设置
sudo ufw allow 6006
```

### 性能优化建议

#### 1. 内存优化
- 使用混合精度训练 (BF16)
- 启用梯度检查点
- 适当调整batch_size和梯度累积

#### 2. 速度优化
- 使用多GPU训练
- 优化数据加载器
- 使用编译优化

#### 3. 质量优化
- 调整学习率调度
- 增加训练轮数
- 优化LoRA参数

---

## 📚 附录

### A. 完整的文件结构

```
DiffSynth-Studio/
├── data/                                    # 数据集目录
│   └── example_video_dataset/
│       ├── metadata.csv
│       └── videos/
├── models/                                  # 模型目录
│   ├── Wan-AI/Wan2.1-T2V-1.3B/            # 基础模型
│   └── train/Wan2.1-T2V-1.3B_lora/        # 训练输出
├── logs/                                    # 日志目录
│   ├── 01_environment_check_*.log
│   ├── 02_model_check_*.log
│   ├── 07_training_output_*.log
│   └── final_report_*.md
├── tensorboard_logs/                        # TensorBoard日志
│   ├── wan_lora_real_*/
│   ├── real_time_monitor_*/
│   └── complete_demo_*/
├── accelerate_config.yaml                   # Accelerate配置
├── start_complete_training.sh               # 一键启动脚本
├── complete_training_pipeline.py            # 完整流水线脚本
├── demo_complete_pipeline.py                # 演示脚本
├── real_time_monitor.py                     # 实时监控脚本
└── 完整训练指南.md                          # 本文档
```

### B. 重要命令速查

#### 环境管理命令

```bash
# Conda环境管理
conda create -n wan_video_env python=3.12 -y    # 创建环境
conda activate wan_video_env                     # 激活环境
conda deactivate                                 # 退出环境
conda env list                                   # 查看所有环境
conda remove -n wan_video_env --all             # 删除环境

# 依赖包管理
pip list                                         # 查看已安装包
pip install package_name                         # 安装包
pip uninstall package_name                       # 卸载包
pip freeze > requirements.txt                    # 导出依赖列表
pip install -r requirements.txt                  # 安装依赖列表
```

#### 训练相关命令

```bash
# 一键启动训练
./start_complete_training.sh                    # 完整自动化训练
./manual_training.sh                            # 手动执行训练

# Accelerate命令
accelerate config                                # 配置Accelerate
accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py [参数]

# 完整训练命令示例
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 --width 576 --dataset_repeat 500 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 --num_epochs 5 --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-T2V-1.3B_lora" \
  --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 16 --use_gradient_checkpointing_offload
```

#### 监控相关命令

```bash
# TensorBoard管理
tensorboard --logdir ./tensorboard_logs --port 6006 --host 0.0.0.0  # 启动TensorBoard
pkill tensorboard                                                    # 停止所有TensorBoard进程
ps aux | grep tensorboard                                            # 查看TensorBoard进程

# 系统监控
nvidia-smi                                       # 查看GPU状态
nvidia-smi -l 1                                  # 每秒刷新GPU状态
watch -n 1 nvidia-smi                            # 持续监控GPU
htop                                             # 查看CPU和内存使用
free -h                                          # 查看内存使用情况
df -h                                            # 查看磁盘使用情况

# 进程管理
ps aux | grep python                             # 查看Python进程
ps aux | grep train                              # 查看训练进程
kill -9 PID                                      # 强制终止进程
pkill -f "train.py"                              # 终止训练进程
```

#### 文件操作命令

```bash
# 查看文件和目录
ls -la ./models/train/                           # 查看训练输出
ls -la ./logs/                                   # 查看日志文件
ls -la ./tensorboard_logs/                       # 查看TensorBoard日志
find . -name "*.safetensors" -type f             # 查找模型文件
find . -name "*.log" -type f -mtime -1           # 查找最近1天的日志

# 日志查看
tail -f logs/07_training_output_*.log            # 实时查看训练日志
head -n 50 logs/01_environment_check_*.log       # 查看日志前50行
grep "loss" logs/07_training_output_*.log        # 搜索包含"loss"的行
less logs/final_report_*.md                      # 分页查看报告

# 文件大小和统计
du -sh ./models/                                 # 查看模型目录大小
du -sh ./tensorboard_logs/                       # 查看日志目录大小
wc -l data/example_video_dataset/metadata.csv   # 统计数据集行数
```

#### 测试和验证命令

```bash
# 数据集验证
python data/example_video_dataset/check_videos.py              # 检查视频文件
python data/example_video_dataset/validate_dataset.py          # 验证数据集格式

# 模型测试
python test_lora_inference.py                                  # 完整推理测试
python -c "
from diffsynth.pipelines.wan_video_new import WanVideoPipeline
pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device='cuda')
pipe.load_lora_weights('./models/train/Wan2.1-T2V-1.3B_lora')
video = pipe('test prompt', height=320, width=576, num_frames=16)
video.save('test.mp4')
print('✅ 快速测试完成')
"

# 环境测试
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
print(f'GPU: {torch.cuda.device_count()}')
"
```

#### 故障排除命令

```bash
# 清理和重置
rm -rf ./tensorboard_logs/*                     # 清理TensorBoard日志
rm -rf ./logs/*                                 # 清理训练日志
rm -rf ./models/train/*                         # 清理训练输出 (谨慎使用)

# 权限修复
chmod +x *.sh                                   # 修复脚本执行权限
chown -R $USER:$USER ./                         # 修复文件所有权

# 网络和下载
ping modelscope.cn                              # 测试网络连接
wget --spider https://modelscope.cn            # 测试下载连接
export HTTP_PROXY=http://proxy:port             # 设置代理 (如需要)

# 显存清理
python -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('✅ GPU缓存已清理')
"
```

#### 一键脚本集合

```bash
# 创建常用脚本快捷方式
cat > quick_commands.sh << 'EOF'
#!/bin/bash
# 常用命令快捷脚本

case "$1" in
    "status")
        echo "🔍 系统状态检查..."
        nvidia-smi
        ps aux | grep -E "(train|tensorboard)" | grep -v grep
        df -h
        ;;
    "logs")
        echo "📝 查看最新日志..."
        ls -lt logs/ | head -5
        ;;
    "clean")
        echo "🧹 清理临时文件..."
        rm -rf ./tensorboard_logs/tmp_*
        rm -rf ./logs/tmp_*
        echo "✅ 清理完成"
        ;;
    "test")
        echo "🧪 快速环境测试..."
        conda activate wan_video_env
        python -c "import torch; print(f'✅ PyTorch {torch.__version__}, CUDA: {torch.cuda.is_available()}')"
        ;;
    *)
        echo "用法: $0 {status|logs|clean|test}"
        echo "  status - 查看系统状态"
        echo "  logs   - 查看最新日志"
        echo "  clean  - 清理临时文件"
        echo "  test   - 测试环境"
        ;;
esac
EOF

chmod +x quick_commands.sh

# 使用示例
./quick_commands.sh status                       # 查看状态
./quick_commands.sh logs                         # 查看日志
./quick_commands.sh clean                        # 清理文件
./quick_commands.sh test                         # 测试环境
```

### C. 参数调优指南

#### LoRA参数
- **rank**: 4-64，越大模型容量越大，但训练越慢
- **alpha**: 通常设为rank的2倍
- **target_modules**: 根据模型架构选择关键层

#### 训练参数
- **learning_rate**: 1e-5 到 1e-3，LoRA通常用较小值
- **batch_size**: 受显存限制，通常1-4
- **gradient_accumulation_steps**: 用于模拟更大的batch_size

#### 数据参数
- **dataset_repeat**: 小数据集可以设置较大值
- **height/width**: 必须是模型支持的分辨率

### D. 相关资源链接

- **项目主页**: https://github.com/modelscope/DiffSynth-Studio
- **模型页面**: https://modelscope.cn/models/Wan-AI/Wan2.1-T2V-1.3B
- **LoRA论文**: https://arxiv.org/abs/2106.09685
- **Accelerate文档**: https://huggingface.co/docs/accelerate
- **TensorBoard指南**: https://www.tensorflow.org/tensorboard

### E. 版本信息

- **文档版本**: v1.0
- **创建日期**: 2025-07-28
- **适用模型**: Wan2.1-T2V-1.3B
- **Python版本**: 3.12+
- **PyTorch版本**: 2.0+
- **CUDA版本**: 12.1+

---

## 🎉 总结

本指南提供了Wan2.1-T2V-1.3B LoRA训练的完整流程，包括：

1. **详细的环境搭建步骤** - 从Conda环境到依赖安装
2. **完整的模型下载指南** - 自动下载和手动下载方式
3. **数据集准备详解** - 格式要求和创建方法
4. **多层次的监控系统** - TensorBoard + 自定义监控
5. **一键启动脚本** - 自动化整个训练流程
6. **详细的故障排除** - 常见问题和解决方案

通过本指南，您可以：
- ✅ 快速搭建训练环境
- ✅ 理解每个步骤的详细过程
- ✅ 实时监控训练状态
- ✅ 解决常见问题
- ✅ 优化训练性能

**开始训练只需一条命令**:
```bash
./start_complete_training.sh
```

**查看详细过程**:
- 访问 http://localhost:6009 查看完整流水线
- 访问 http://localhost:6008 查看实时监控
- 查看 `logs/` 目录下的详细日志文件

### 🚀 完整执行示例

#### 从零开始的完整执行流程

```bash
#!/bin/bash
# 完整的从零开始执行流程

echo "🎯 开始Wan2.1-T2V-1.3B LoRA训练完整流程"

# 1. 环境准备
echo "📋 步骤1: 环境准备"
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 2. 创建Conda环境
echo "📋 步骤2: 创建Conda环境"
conda create -n wan_video_env python=3.12 -y
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 3. 安装依赖
echo "📋 步骤3: 安装依赖包"
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
pip install accelerate transformers diffusers tensorboard matplotlib psutil opencv-python pillow
pip install -e .

# 4. 验证安装
echo "📋 步骤4: 验证安装"
python -c "
import torch
print(f'✅ PyTorch版本: {torch.__version__}')
print(f'✅ CUDA可用: {torch.cuda.is_available()}')
print(f'✅ GPU数量: {torch.cuda.device_count()}')
for i in range(torch.cuda.device_count()):
    print(f'✅ GPU {i}: {torch.cuda.get_device_name(i)}')
"

# 5. 创建数据集
echo "📋 步骤5: 创建数据集"
./create_dataset.sh

# 6. 启动训练
echo "📋 步骤6: 启动训练"
./start_complete_training.sh

echo "🎉 完整流程执行完成!"
echo "🌐 TensorBoard访问地址:"
echo "  - 完整流水线: http://localhost:6009"
echo "  - 实时监控: http://localhost:6008"
echo "  - 基础监控: http://localhost:6007"
```

#### 训练完成后的验证流程

```bash
#!/bin/bash
# 训练完成后的验证和测试流程

echo "🔍 开始训练结果验证..."

# 1. 检查输出文件
echo "📋 步骤1: 检查输出文件"
if [ -d "./models/train/Wan2.1-T2V-1.3B_lora" ]; then
    echo "✅ 输出目录存在"
    find "./models/train/Wan2.1-T2V-1.3B_lora" -type f -exec ls -lh {} \;
else
    echo "❌ 输出目录不存在"
    exit 1
fi

# 2. 验证LoRA权重
echo "📋 步骤2: 验证LoRA权重"
python -c "
import torch
from safetensors import safe_open
import os

lora_path = './models/train/Wan2.1-T2V-1.3B_lora/pytorch_lora_weights.safetensors'
if os.path.exists(lora_path):
    print('✅ LoRA权重文件存在')
    with safe_open(lora_path, framework='pt', device='cpu') as f:
        keys = list(f.keys())
        print(f'✅ 权重键数量: {len(keys)}')
        for key in keys[:5]:  # 显示前5个键
            tensor = f.get_tensor(key)
            print(f'  {key}: {tensor.shape}')
        if len(keys) > 5:
            print(f'  ... 还有 {len(keys)-5} 个权重')
else:
    print('❌ LoRA权重文件不存在')
    exit(1)
"

# 3. 执行推理测试
echo "📋 步骤3: 执行推理测试"
python test_lora_inference.py

# 4. 生成最终报告
echo "📋 步骤4: 生成最终报告"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
cat > final_validation_report_${TIMESTAMP}.md << EOF
# 训练验证最终报告

## 验证时间
$(date +"%Y-%m-%d %H:%M:%S")

## 文件检查
$(find "./models/train/Wan2.1-T2V-1.3B_lora" -type f -exec ls -lh {} \; 2>/dev/null || echo "输出目录不存在")

## 日志文件
$(ls -la logs/ | tail -5)

## TensorBoard日志
$(ls -la tensorboard_logs/ | tail -3)

## 推理测试
$(ls -la *test_output*.mp4 2>/dev/null || echo "未找到测试输出视频")

## 验证结论
- LoRA权重文件: $([ -f "./models/train/Wan2.1-T2V-1.3B_lora/pytorch_lora_weights.safetensors" ] && echo "✅ 存在" || echo "❌ 不存在")
- 配置文件: $([ -f "./models/train/Wan2.1-T2V-1.3B_lora/adapter_config.json" ] && echo "✅ 存在" || echo "❌ 不存在")
- 推理测试: $([ -f "test_output_01.mp4" ] && echo "✅ 成功" || echo "❌ 失败")

## 下一步建议
1. 如果所有检查都通过，可以开始使用训练好的LoRA模型
2. 如果有问题，请检查对应的日志文件
3. 可以尝试不同的提示词进行推理测试
4. 考虑合并LoRA权重到基础模型（可选）
EOF

echo "📊 最终验证报告: final_validation_report_${TIMESTAMP}.md"
echo "🎉 验证流程完成!"
```

#### 问题诊断和修复脚本

```bash
#!/bin/bash
# 自动诊断和修复常见问题

echo "🔧 开始自动诊断..."

# 1. 检查环境
echo "📋 检查1: 环境状态"
if conda info --envs | grep -q "wan_video_env"; then
    echo "✅ Conda环境存在"
else
    echo "❌ Conda环境不存在，正在创建..."
    conda create -n wan_video_env python=3.12 -y
fi

# 2. 检查CUDA
echo "📋 检查2: CUDA状态"
if nvidia-smi > /dev/null 2>&1; then
    echo "✅ NVIDIA驱动正常"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits
else
    echo "❌ NVIDIA驱动问题"
fi

# 3. 检查Python包
echo "📋 检查3: Python包"
conda activate wan_video_env
REQUIRED_PACKAGES=("torch" "accelerate" "transformers" "diffusers" "tensorboard")
for package in "${REQUIRED_PACKAGES[@]}"; do
    if python -c "import $package" 2>/dev/null; then
        echo "✅ $package 已安装"
    else
        echo "❌ $package 未安装，正在安装..."
        pip install $package
    fi
done

# 4. 检查文件权限
echo "📋 检查4: 文件权限"
for script in *.sh; do
    if [ -x "$script" ]; then
        echo "✅ $script 有执行权限"
    else
        echo "🔧 修复 $script 执行权限"
        chmod +x "$script"
    fi
done

# 5. 检查磁盘空间
echo "📋 检查5: 磁盘空间"
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
REQUIRED_SPACE=102400000  # 100GB in KB
if [ "$AVAILABLE_SPACE" -gt "$REQUIRED_SPACE" ]; then
    echo "✅ 磁盘空间充足 ($(($AVAILABLE_SPACE/1024/1024))GB 可用)"
else
    echo "⚠️ 磁盘空间不足 ($(($AVAILABLE_SPACE/1024/1024))GB 可用，建议100GB+)"
fi

# 6. 清理临时文件
echo "📋 检查6: 清理临时文件"
find . -name "*.tmp" -delete 2>/dev/null
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
echo "✅ 临时文件已清理"

echo "🎉 诊断完成!"
```

祝您训练顺利！🚀
