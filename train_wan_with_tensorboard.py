#!/usr/bin/env python3
"""
基于原始train.py的TensorBoard集成版本
在原始训练流程中添加详细的TensorBoard日志记录
"""

import torch
import os
import json
import time
import numpy as np
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt

# 导入原始训练模块
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from diffsynth.trainers.utils import DiffusionTrainingModule, VideoDataset, ModelLogger, wan_parser, launch_training_task

os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 全局TensorBoard writer
global_tensorboard_writer = None
global_step_counter = 0

class WanTrainingModule(DiffusionTrainingModule):
    def __init__(
        self,
        model_paths=None, model_id_with_origin_paths=None,
        trainable_models=None,
        lora_base_model=None, lora_target_modules="q,k,v,o,ffn.0,ffn.2", lora_rank=32,
        use_gradient_checkpointing=True,
        use_gradient_checkpointing_offload=False,
        extra_inputs=None,
    ):
        super().__init__()
        
        # Load models
        model_configs = []
        if model_paths is not None:
            model_paths = json.loads(model_paths)
            model_configs += [ModelConfig(path=path) for path in model_paths]
        if model_id_with_origin_paths is not None:
            model_id_with_origin_paths = model_id_with_origin_paths.split(",")
            model_configs += [ModelConfig(model_id=i.split(":")[0], origin_file_pattern=i.split(":")[1]) for i in model_id_with_origin_paths]
        
        print("📥 加载Wan2.1-T2V-1.3B模型...")
        self.pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device="cpu", model_configs=model_configs)
        
        # Reset training scheduler
        self.pipe.scheduler.set_timesteps(1000, training=True)
        
        # Freeze untrainable models
        self.pipe.freeze_except([] if trainable_models is None else trainable_models.split(","))
        
        # Add LoRA to the base models
        if lora_base_model is not None:
            print(f"🔧 添加LoRA到 {lora_base_model} (rank={lora_rank}, modules={lora_target_modules})")
            model = self.add_lora_to_model(
                getattr(self.pipe, lora_base_model),
                target_modules=lora_target_modules.split(","),
                lora_rank=lora_rank
            )
            setattr(self.pipe, lora_base_model, model)
            
            # 统计可训练参数
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            total_params = sum(p.numel() for p in model.parameters())
            print(f"📊 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
            
            # 记录到TensorBoard
            if global_tensorboard_writer:
                global_tensorboard_writer.add_scalar("Model/Trainable_Parameters", trainable_params, 0)
                global_tensorboard_writer.add_scalar("Model/Total_Parameters", total_params, 0)
                global_tensorboard_writer.add_scalar("Model/Trainable_Percentage", trainable_params/total_params*100, 0)
        
        # Store other configs
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.use_gradient_checkpointing_offload = use_gradient_checkpointing_offload
        self.extra_inputs = extra_inputs.split(",") if extra_inputs is not None else []
        
    def forward_preprocess(self, data):
        inputs_shared = {}
        inputs_posi = {}
        inputs_nega = {}

        # Prepare inputs - 确保height和width有默认值
        for key in ["prompt", "negative_prompt", "height", "width", "num_frames", "fps"]:
            if key in data:
                if key == "prompt":
                    inputs_posi[key] = data[key]
                elif key == "negative_prompt":
                    inputs_nega[key] = data[key]
                else:
                    inputs_shared[key] = data[key]
            elif key == "height":
                # 如果数据中没有height，使用默认值320
                inputs_shared[key] = 320
            elif key == "width":
                # 如果数据中没有width，使用默认值576
                inputs_shared[key] = 576
            elif key == "num_frames":
                # 如果数据中没有num_frames，使用默认值16
                inputs_shared[key] = 16
            elif key == "fps":
                # 如果数据中没有fps，使用默认值8
                inputs_shared[key] = 8

        # Video
        if "video" in data:
            inputs_shared["video"] = data["video"]

        # Extra inputs
        for extra_input in self.extra_inputs:
            if extra_input in data:
                if extra_input.endswith("_posi"):
                    inputs_posi[extra_input[:-5]] = data[extra_input]
                elif extra_input.endswith("_nega"):
                    inputs_nega[extra_input[:-5]] = data[extra_input]
                else:
                    inputs_shared[extra_input] = data[extra_input]

        # Pipeline units will automatically process the input parameters.
        for unit in self.pipe.units:
            inputs_shared, inputs_posi, inputs_nega = self.pipe.unit_runner(unit, self.pipe, inputs_shared, inputs_posi, inputs_nega)
        return {**inputs_shared, **inputs_posi}
    
    def forward(self, data, inputs=None):
        global global_step_counter, global_tensorboard_writer
        
        if inputs is None: 
            inputs = self.forward_preprocess(data)
        models = {name: getattr(self.pipe, name) for name in self.pipe.in_iteration_models}
        loss = self.pipe.training_loss(**models, **inputs)
        
        # TensorBoard日志记录
        if global_tensorboard_writer and global_step_counter % 10 == 0:
            # 记录损失
            global_tensorboard_writer.add_scalar("Loss/Train", loss.item(), global_step_counter)
            
            # 记录显存使用
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                    memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                    total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                    
                    global_tensorboard_writer.add_scalar(f"Memory/GPU_{i}/Allocated_GB", memory_allocated, global_step_counter)
                    global_tensorboard_writer.add_scalar(f"Memory/GPU_{i}/Reserved_GB", memory_reserved, global_step_counter)
                    global_tensorboard_writer.add_scalar(f"Memory/GPU_{i}/Utilization_%", memory_allocated/total_memory*100, global_step_counter)
            
            # 记录模型参数统计（每50步）
            if global_step_counter % 50 == 0:
                for name, param in self.named_parameters():
                    if param.requires_grad and param.grad is not None:
                        # 参数统计
                        global_tensorboard_writer.add_histogram(f"Parameters/{name}", param.data, global_step_counter)
                        global_tensorboard_writer.add_scalar(f"ParamStats/{name}/mean", param.data.mean().item(), global_step_counter)
                        global_tensorboard_writer.add_scalar(f"ParamStats/{name}/std", param.data.std().item(), global_step_counter)
                        
                        # 梯度统计
                        if param.grad is not None:
                            global_tensorboard_writer.add_histogram(f"Gradients/{name}", param.grad.data, global_step_counter)
                            global_tensorboard_writer.add_scalar(f"GradStats/{name}/norm", param.grad.data.norm().item(), global_step_counter)
        
        global_step_counter += 1
        return loss

def create_tensorboard_logger(args):
    """创建TensorBoard记录器"""
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tensorboard_dir = f"./tensorboard_logs/wan_lora_real_{timestamp}"
    os.makedirs(tensorboard_dir, exist_ok=True)
    
    # 创建writer
    writer = SummaryWriter(log_dir=tensorboard_dir)
    
    # 记录配置信息
    config_text = f"""
# Wan2.1-T2V-1.3B LoRA真实训练配置

## 模型配置
- Model ID: Wan-AI/Wan2.1-T2V-1.3B
- LoRA Base Model: {args.lora_base_model}
- LoRA Target Modules: {args.lora_target_modules}
- LoRA Rank: {args.lora_rank}

## 训练配置
- Learning Rate: {args.learning_rate}
- Num Epochs: {args.num_epochs}
- Gradient Accumulation Steps: {args.gradient_accumulation_steps}
- Dataset Repeat: {args.dataset_repeat}

## 视频配置
- Height: {args.height}
- Width: {args.width}
- Dataset Path: {args.dataset_base_path}

## 硬件配置
- Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}
- GPU Count: {torch.cuda.device_count()}
- CUDA Version: {torch.version.cuda if torch.cuda.is_available() else 'N/A'}

## 训练开始时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    writer.add_text("Config/Training_Configuration", config_text, 0)
    
    print(f"📊 TensorBoard日志目录: {tensorboard_dir}")
    print(f"💡 启动TensorBoard: tensorboard --logdir {tensorboard_dir}")
    
    return writer, tensorboard_dir

def main():
    """主函数"""
    global global_tensorboard_writer
    
    print("🚀 开始Wan2.1-T2V-1.3B LoRA训练 (集成TensorBoard)")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 解析参数
    parser = wan_parser()
    args = parser.parse_args()
    
    # 创建TensorBoard记录器
    global_tensorboard_writer, tensorboard_dir = create_tensorboard_logger(args)
    
    # 创建数据集
    print("📂 准备数据集...")
    dataset = VideoDataset(args=args)
    print(f"📊 数据集大小: {len(dataset)}")
    
    # 创建模型
    print("🤖 初始化模型...")
    model = WanTrainingModule(
        model_paths=args.model_paths,
        model_id_with_origin_paths=args.model_id_with_origin_paths,
        trainable_models=args.trainable_models,
        lora_base_model=args.lora_base_model,
        lora_target_modules=args.lora_target_modules,
        lora_rank=args.lora_rank,
        use_gradient_checkpointing_offload=args.use_gradient_checkpointing_offload,
        extra_inputs=args.extra_inputs,
    )
    
    # 创建模型记录器
    model_logger = ModelLogger(
        args.output_path,
        remove_prefix_in_ckpt=args.remove_prefix_in_ckpt
    )
    
    # 创建优化器和调度器
    print("⚙️ 配置优化器...")
    optimizer = torch.optim.AdamW(model.trainable_modules(), lr=args.learning_rate)
    scheduler = torch.optim.lr_scheduler.ConstantLR(optimizer)
    
    # 记录优化器配置
    global_tensorboard_writer.add_scalar("Config/Learning_Rate", args.learning_rate, 0)
    global_tensorboard_writer.add_scalar("Config/Num_Epochs", args.num_epochs, 0)
    global_tensorboard_writer.add_scalar("Config/Gradient_Accumulation_Steps", args.gradient_accumulation_steps, 0)
    
    try:
        # 启动训练
        print("🏋️ 开始训练...")
        launch_training_task(
            dataset, model, model_logger, optimizer, scheduler,
            num_epochs=args.num_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
        )
        
        print("✅ 训练完成!")
        
        # 记录训练完成
        global_tensorboard_writer.add_text("Status/Training_Complete", 
                                          f"训练于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 完成", 
                                          global_step_counter)
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        global_tensorboard_writer.add_text("Status/Training_Error", 
                                          f"训练失败: {str(e)}", 
                                          global_step_counter)
        raise
    
    finally:
        # 关闭TensorBoard记录器
        global_tensorboard_writer.close()
        print(f"📊 TensorBoard日志已保存到: {tensorboard_dir}")
        print(f"💡 查看结果: tensorboard --logdir {tensorboard_dir}")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
