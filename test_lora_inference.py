#!/usr/bin/env python3
"""
LoRA推理测试脚本
测试训练后的LoRA权重效果
"""

import os
import torch
from diffsynth import ModelManager, WanVideoPipeline
import time

def test_lora_inference():
    """测试LoRA推理"""
    
    print("=== Wan2.1-T2V-1.3B LoRA推理测试 ===")
    
    # 配置参数
    base_model_path = "./models/Wan-AI/Wan2.1-T2V-1.3B"
    lora_optimized_path = "./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors"
    lora_minimal_path = "./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors"
    output_dir = "./outputs/lora_inference_test"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试提示词
    test_prompts = [
        "from sunset to night, a small town, light, house, river",  # 原训练数据
        "a beautiful sunset over mountains, golden light",          # 相似场景
        "city lights at night, urban landscape, neon signs"        # 不同场景
    ]
    
    print(f"📁 输出目录: {output_dir}")
    print(f"🎯 测试提示词数量: {len(test_prompts)}")
    
    # 检查LoRA文件
    lora_files = []
    if os.path.exists(lora_optimized_path):
        lora_files.append(("optimized", lora_optimized_path))
        print(f"✅ 找到优化版LoRA: {lora_optimized_path}")
    
    if os.path.exists(lora_minimal_path):
        lora_files.append(("minimal", lora_minimal_path))
        print(f"✅ 找到极限版LoRA: {lora_minimal_path}")
    
    if not lora_files:
        print("❌ 未找到LoRA文件，请先完成训练")
        return False
    
    # 测试每个LoRA模型
    for lora_name, lora_path in lora_files:
        print(f"\n{'='*60}")
        print(f"🧪 测试LoRA模型: {lora_name}")
        print(f"📄 LoRA路径: {lora_path}")
        
        try:
            # 加载模型
            print("📥 加载基础模型...")
            model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cuda")
            model_manager.load_models([
                f"{base_model_path}/diffusion_pytorch_model.safetensors",
                f"{base_model_path}/models_t5_umt5-xxl-enc-bf16.pth", 
                f"{base_model_path}/Wan2.1_VAE.pth"
            ])
            
            # 加载LoRA权重
            print("📥 加载LoRA权重...")
            model_manager.load_lora(lora_path, lora_alpha=1.0)
            
            # 创建推理管道
            print("🔧 创建推理管道...")
            pipe = WanVideoPipeline.from_model_manager(model_manager)
            
            # 测试每个提示词
            for i, prompt in enumerate(test_prompts):
                print(f"\n🎬 生成视频 {i+1}/{len(test_prompts)}")
                print(f"📝 提示词: {prompt}")
                
                start_time = time.time()
                
                # 生成视频
                video = pipe(
                    prompt=prompt,
                    height=256,  # 使用较小分辨率加快推理
                    width=448,
                    num_frames=16,  # 较少帧数
                    num_inference_steps=20,  # 较少推理步数
                    guidance_scale=7.5,
                    generator=torch.Generator().manual_seed(42)  # 固定种子便于对比
                )
                
                end_time = time.time()
                inference_time = end_time - start_time
                
                # 保存视频
                output_path = f"{output_dir}/{lora_name}_prompt_{i+1}.mp4"
                video.save(output_path, fps=8)
                
                # 获取文件大小
                file_size = os.path.getsize(output_path) / (1024*1024)  # MB
                
                print(f"✅ 视频已保存: {output_path}")
                print(f"⏱️  推理时间: {inference_time:.1f}秒")
                print(f"📊 文件大小: {file_size:.1f}MB")
            
            print(f"\n✅ {lora_name} LoRA测试完成")
            
            # 清理显存
            del pipe
            del model_manager
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"❌ {lora_name} LoRA测试失败: {str(e)}")
            continue
    
    # 生成测试报告
    print(f"\n{'='*60}")
    print("📋 LoRA推理测试报告")
    
    # 统计生成的视频
    generated_videos = []
    if os.path.exists(output_dir):
        for file in os.listdir(output_dir):
            if file.endswith('.mp4'):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path) / (1024*1024)
                generated_videos.append((file, file_size))
    
    print(f"📊 生成视频统计:")
    print(f"  - 总数量: {len(generated_videos)}")
    
    for video_name, size in generated_videos:
        print(f"  - {video_name}: {size:.1f}MB")
    
    if generated_videos:
        total_size = sum(size for _, size in generated_videos)
        print(f"  - 总大小: {total_size:.1f}MB")
        print(f"\n✅ LoRA推理测试成功完成！")
        print(f"📁 所有视频保存在: {output_dir}")
        return True
    else:
        print(f"\n❌ 未生成任何视频，测试失败")
        return False

def main():
    """主函数"""
    
    print("🚀 开始LoRA推理测试...")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行推理测试")
        return
    
    print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
    
    # 执行测试
    success = test_lora_inference()
    
    print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("🎉 LoRA推理测试全部完成！")
    else:
        print("❌ LoRA推理测试失败")

if __name__ == "__main__":
    main()
