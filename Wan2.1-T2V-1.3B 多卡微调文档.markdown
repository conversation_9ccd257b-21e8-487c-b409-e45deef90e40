# Wan-AI/Wan2.1-T2V-1.3B 多卡微调文档

## 1. 概述

本文档提供了Wan2.1-T2V-1.3B模型从环境搭建、数据准备、多卡微调、权重合并到推理部署的完整端到端流程。基于最新版DiffSynth-Studio，包含所有必要的代码和详细步骤。

## 2. 完整流程概览

```mermaid
flowchart LR
    %% 主流程
    A[环境准备] --> B[模型下载]
    B --> C[数据集准备]
    C --> D[多卡LoRA微调]
    D --> E[LoRA推理测试]
    E --> F[权重合并]
    F --> G[合并模型测试]

    %% 子流程详情
    subgraph S1 [" "]
        A1[Python 3.12<br/>DiffSynth-Studio<br/>训练依赖包]
    end

    subgraph S2 [" "]
        B1[Wan2.1-T2V-1.3B<br/>13.7GB基础模型<br/>3个核心组件]
    end

    subgraph S3 [" "]
        C1[官方示例数据集]
        C2[自定义数据集]
        C1 -.-> C3[metadata.csv验证]
        C2 -.-> C3
    end

    subgraph S4 [" "]
        D1[Accelerate多卡配置]
        D2[LoRA参数调优]
        D3[5轮训练输出]
        D1 --> D2 --> D3
    end

    subgraph S5 [" "]
        E1[加载LoRA权重<br/>生成测试视频<br/>验证训练效果]
    end

    subgraph S6 [" "]
        F1[Alpha=0.8]
        F2[Alpha=1.0]
        F3[Alpha=1.2]
        F4[完整合并模型]
        F1 --> F4
        F2 --> F4
        F3 --> F4
    end

    subgraph S7 [" "]
        G1[效果对比评估<br/>选择最佳Alpha<br/>性能验证]
    end

    %% 连接主流程和子流程
    A -.-> S1
    B -.-> S2
    C -.-> S3
    D -.-> S4
    E -.-> S5
    F -.-> S6
    G -.-> S7

    %% 样式定义
    classDef mainFlow fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    classDef subFlow fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    classDef subDetail fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    classDef option fill:#FFF3E0,stroke:#FF9800,stroke-width:1px

    class A,B,C,D,E,F,G,H mainFlow
    class A1,B1,E1,G1,H1,D1,D2,D3,F4 subFlow
    class C3 subDetail
    class C1,C2,F1,F2,F3 option

    %% 隐藏子图边框
    style S1 fill:none,stroke:none
    style S2 fill:none,stroke:none
    style S3 fill:none,stroke:none
    style S4 fill:none,stroke:none
    style S5 fill:none,stroke:none
    style S6 fill:none,stroke:none
    style S7 fill:none,stroke:none
```

## 3. 环境搭建代码

### 3.1 Conda环境创建

```bash
# 创建Python 3.12环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env

# 验证Python版本
python --version  # 输出: Python 3.12.11
```

### 3.2 代理设置

```bash
# 启用代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
```

### 3.3 核心依赖安装

```bash
# 克隆最新版DiffSynth-Studio仓库
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装DiffSynth-Studio核心库
pip install -e .

# 安装训练相关依赖
pip install deepspeed peft

# 验证安装
python -c "
import torch
import diffsynth
import accelerate
import deepspeed
import peft
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
"
```

## 4. 模型下载代码

### 4.1 基础模型下载

```bash
# 安装ModelScope CLI工具
pip install modelscope

# 下载Wan2.1-T2V-1.3B基础模型
modelscope download --model Wan-AI/Wan2.1-T2V-1.3B --local_dir ./models/Wan-AI/Wan2.1-T2V-1.3B

# 查看下载的模型文件
ls -la models/Wan-AI/Wan2.1-T2V-1.3B/

# 查看模型大小
du -sh models/Wan-AI/Wan2.1-T2V-1.3B/
```

### 4.2 模型文件验证

```bash
# 验证关键模型文件
python -c "
import os
from pathlib import Path

model_path = Path('./models/Wan-AI/Wan2.1-T2V-1.3B')
required_files = [
    'diffusion_pytorch_model.safetensors',
    'models_t5_umt5-xxl-enc-bf16.pth',
    'Wan2.1_VAE.pth',
    'config.json'
]

print('=== 模型文件验证 ===')
for filename in required_files:
    file_path = model_path / filename
    if file_path.exists():
        size_mb = file_path.stat().st_size / (1024*1024)
        print(f'✅ {filename}: {size_mb:.1f} MB')
    else:
        print(f'❌ {filename}: 缺失')
"
```

## 5. 数据集准备代码

### 5.1 方案选择

#### 5.1.1 官方示例数据集（推荐新手）

```bash
# 创建数据目录
mkdir -p data

# 下载官方示例视频数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset

# 验证数据集结构
ls -la data/example_video_dataset/
# 输出:
# metadata.csv
# video1.mp4
# video2.mp4
```

#### 5.1.2 自定义数据集（推荐进阶用户）

```bash
# 创建自定义数据集目录结构
mkdir -p data/custom_video_dataset/videos

# 数据集目录结构
# data/custom_video_dataset/
# ├── metadata.csv
# └── videos/
#     ├── video_001.mp4
#     ├── video_002.mp4
#     └── ...
```

### 5.2 自定义数据集准备脚本

创建 `prepare_custom_dataset.py`：

```python
import os
import csv
import cv2
from pathlib import Path

def create_metadata_csv(dataset_path, video_folder="videos"):
    """创建metadata.csv文件"""
    dataset_path = Path(dataset_path)
    video_folder_path = dataset_path / video_folder

    if not video_folder_path.exists():
        print(f"❌ 视频文件夹 {video_folder_path} 不存在")
        return False

    # 获取所有视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    video_files = []

    for ext in video_extensions:
        video_files.extend(video_folder_path.glob(f'*{ext}'))

    if not video_files:
        print(f"❌ 在 {video_folder_path} 中未找到视频文件")
        return False

    # 创建metadata.csv
    metadata_path = dataset_path / "metadata.csv"

    with open(metadata_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['video', 'text'])  # 注意列名格式

        for i, video_file in enumerate(sorted(video_files)):
            relative_path = f"videos/{video_file.name}"

            # 示例描述文本（实际使用时需要替换为真实描述）
            sample_texts = [
                "纪实摄影风格画面，一只活泼的小狗在绿茵茵的草地上迅速奔跑",
                "一只可爱的小猫在阳光明媚的花园里追逐蝴蝶，花朵盛开，微风轻拂",
                "海浪拍打着岩石，夕阳西下，天空呈现出美丽的橙红色",
                "一朵花在微风中轻柔摇摆，花瓣晶莹剔透，露珠闪闪发光",
                "雨滴落在湖面上，泛起层层涟漪，远山如黛"
            ]

            text = sample_texts[i % len(sample_texts)]
            writer.writerow([relative_path, text])

    print(f"✅ 创建metadata.csv成功，包含 {len(video_files)} 个视频")
    return True

def main():
    dataset_path = "./data/custom_video_dataset"

    # 创建数据集目录
    os.makedirs(dataset_path, exist_ok=True)
    os.makedirs(f"{dataset_path}/videos", exist_ok=True)

    print("📁 自定义数据集目录已创建")
    print("请将视频文件放入 data/custom_video_dataset/videos/ 文件夹中")
    print("然后运行此脚本生成metadata.csv文件")

    # 如果videos文件夹中有文件，则生成metadata.csv
    video_folder = Path(dataset_path) / "videos"
    if any(video_folder.iterdir()):
        create_metadata_csv(dataset_path)
    else:
        print("videos文件夹为空，请先添加视频文件")

if __name__ == "__main__":
    main()
```

### 5.3 数据集验证代码

创建 `validate_dataset.py`：

```python
# 验证数据集完整性
import pandas as pd
import os

def validate_dataset(dataset_path):
    """验证数据集完整性"""
    metadata_path = os.path.join(dataset_path, "metadata.csv")

    if not os.path.exists(metadata_path):
        print(f"❌ metadata.csv 不存在: {metadata_path}")
        return False

    # 读取元数据
    df = pd.read_csv(metadata_path)
    print(f"数据集包含 {len(df)} 个样本")
    print("前5个样本:")
    print(df.head())

    # 验证视频文件存在
    missing_files = []
    for _, row in df.iterrows():
        video_path = os.path.join(dataset_path, row['video'])
        if os.path.exists(video_path):
            print(f"✅ {row['video']}")
        else:
            print(f"❌ {row['video']} 不存在")
            missing_files.append(row['video'])

    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 个视频文件")
        return False
    else:
        print(f"\n✅ 数据集验证通过，共 {len(df)} 个有效样本")
        return True

# 验证官方数据集
print("=== 验证官方示例数据集 ===")
validate_dataset("./data/example_video_dataset")

# 验证自定义数据集（如果存在）
custom_dataset_path = "./data/custom_video_dataset"
if os.path.exists(custom_dataset_path):
    print("\n=== 验证自定义数据集 ===")
    validate_dataset(custom_dataset_path)
```

### 5.4 数据集准备执行

```bash
# 方案1：使用官方示例数据集
python validate_dataset.py

# 方案2：准备自定义数据集
# 1. 将视频文件放入 data/custom_video_dataset/videos/
# 2. 运行准备脚本
python prepare_custom_dataset.py
# 3. 验证数据集
python validate_dataset.py
```

## 6. 多卡LoRA微调代码

### 6.1 Accelerate多卡配置

创建 `accelerate_config.yaml`：

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all                    # 使用所有GPU，或指定如 "0,1,2,3"
machine_rank: 0
main_training_function: main
mixed_precision: bf16           # 推荐使用bf16混合精度
num_machines: 1
num_processes: 4                # 根据GPU数量调整：2卡=2，4卡=4，8卡=8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

```bash
# 初始化accelerate配置
accelerate config --config_file accelerate_config.yaml

# 验证配置
accelerate env
```

### 6.2 LoRA训练脚本

创建 `train_lora.sh`：

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 数据集和模型路径
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-T2V-1.3B_lora"

echo "🚀 开始Wan2.1-T2V-1.3B LoRA训练..."

accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32

echo "✅ LoRA训练完成！模型保存在: ${OUTPUT_PATH}"
```

### 6.3 训练执行

```bash
# 给脚本执行权限
chmod +x train_lora.sh

# 启动训练
./train_lora.sh
```

## 7. LoRA推理测试代码

在权重合并之前，先测试LoRA权重是否正常工作。

### 7.1 LoRA推理测试脚本

创建 `test_lora_inference.py`：

```python
#!/usr/bin/env python3
"""
LoRA推理测试脚本
测试微调后的LoRA权重是否正常工作
"""

import torch
import os
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def test_lora_inference():
    """测试LoRA推理功能"""
  
    # 配置参数
    lora_path = "./models/train/Wan2.1-T2V-1.3B_lora/epoch-3.safetensors"
  
    # 检查LoRA文件是否存在
    if not os.path.exists(lora_path):
        print(f"❌ LoRA权重文件不存在: {lora_path}")
        return False
  
    print(f"✅ 找到LoRA权重文件: {lora_path}")
    print(f"文件大小: {os.path.getsize(lora_path) / (1024*1024):.2f} MB")
  
    try:
        print("\n=== 开始加载模型 ===")
  
        # 创建推理管道
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ],
        )
        print("✅ 基础模型加载成功")
  
        # 加载LoRA权重
        print("\n=== 加载LoRA权重 ===")
        pipe.load_lora(pipe.dit, lora_path, alpha=1.0)
        print("✅ LoRA权重加载成功")
  
        # 启用显存管理
        pipe.enable_vram_management()
        print("✅ 显存管理启用成功")
  
        # 生成测试视频
        test_prompts = [
            "纪实摄影风格画面，一只活泼的小狗在绿茵茵的草地上迅速奔跑",
            "一只可爱的小猫在花园里追逐蝴蝶，花朵盛开，微风轻拂",
            "海浪拍打着岩石，夕阳西下，天空呈现出美丽的橙红色"
        ]
  
        negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"
  
        for i, prompt in enumerate(test_prompts):
            print(f"\n=== 生成测试视频 {i+1}/{len(test_prompts)} ===")
            print(f"提示词: {prompt}")
      
            video = pipe(
                prompt=prompt,
                negative_prompt=negative_prompt,
                seed=i + 42,
                tiled=True,
            )
      
            output_path = f"lora_test_{i+1:02d}.mp4"
            save_video(video, output_path, fps=15, quality=5)
      
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024*1024)
                print(f"✅ 视频生成成功: {output_path} ({file_size:.2f} MB)")
            else:
                print(f"❌ 视频生成失败: {output_path}")
  
        print(f"\n✅ LoRA推理测试完成！")
        return True
  
    except Exception as e:
        print(f"❌ 推理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== Wan2.1-T2V-1.3B LoRA推理测试 ===")
  
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
  
    print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
    print(f"当前设备: {torch.cuda.get_device_name()}")
  
    # 运行测试
    success = test_lora_inference()
  
    if success:
        print("\n🎉 LoRA推理测试成功！可以进行权重合并。")
    else:
        print("\n❌ LoRA推理测试失败，请检查训练权重。")

if __name__ == "__main__":
    main()
```

### 7.2 运行LoRA推理测试

```bash
python test_lora_inference.py
```

## 8. 权重合并代码

### 8.1 完整权重合并脚本

创建 `create_complete_merged_model.py`：

```python
#!/usr/bin/env python3
"""
创建完整的合并模型脚本
确保包含原始模型的所有文件，只替换合并后的DiT权重
"""

import torch
import os
import json
import shutil
from pathlib import Path
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def create_complete_merged_model(base_model_id, lora_path, output_path, alpha=1.0):
    """创建包含所有原始文件的完整合并模型"""

    print(f"\n=== 创建完整合并模型 ===")
    print(f"基础模型: {base_model_id}")
    print(f"LoRA权重: {lora_path}")
    print(f"输出路径: {output_path}")
    print(f"合并系数: {alpha}")

    # 确定原始模型路径
    base_model_path = Path(f"./models/{base_model_id}")
    if not base_model_path.exists():
        print(f"❌ 原始模型路径不存在: {base_model_path}")
        return False

    try:
        # 1. 加载基础模型并应用LoRA
        print(f"\n📥 加载基础模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cpu",  # 使用CPU节省显存
            model_configs=[
                ModelConfig(model_id=base_model_id, origin_file_pattern="diffusion_pytorch_model*.safetensors"),
                ModelConfig(model_id=base_model_id, origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
                ModelConfig(model_id=base_model_id, origin_file_pattern="Wan2.1_VAE.pth"),
            ],
        )

        print(f"🔄 应用LoRA权重...")
        pipe.load_lora(pipe.dit, lora_path, alpha=alpha)
        merged_dit_state = pipe.dit.state_dict()
        print(f"✅ LoRA权重应用完成")

        # 2. 创建输出目录
        output_path = Path(output_path)
        if output_path.exists():
            print(f"⚠️  输出目录已存在，将清空: {output_path}")
            shutil.rmtree(output_path)

        output_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建输出目录: {output_path}")

        # 3. 复制原始模型的所有文件和文件夹
        print(f"\n📋 复制原始模型的所有内容...")
        print(f"  源路径: {base_model_path}")

        total_copied = 0
        total_size = 0

        for item in base_model_path.rglob('*'):
            if item.is_file():
                # 计算相对路径
                rel_path = item.relative_to(base_model_path)
                dst_path = output_path / rel_path

                # 跳过diffusion_pytorch_model.safetensors，稍后保存合并版本
                if item.name == "diffusion_pytorch_model.safetensors":
                    continue

                # 创建目标目录
                dst_path.parent.mkdir(parents=True, exist_ok=True)

                # 复制文件
                shutil.copy2(item, dst_path)

                file_size = item.stat().st_size
                total_size += file_size
                total_copied += 1

                # 显示大文件的复制进度
                if file_size > 100 * 1024 * 1024:  # 大于100MB
                    size_mb = file_size / (1024 * 1024)
                    print(f"    复制大文件: {rel_path} ({size_mb:.1f} MB)")

        print(f"  ✅ 复制完成: {total_copied} 个文件，总大小 {total_size / (1024**3):.2f} GB")

        # 4. 保存合并后的DiT权重
        print(f"\n💾 保存合并后的DiT模型...")
        merged_dit_path = output_path / "diffusion_pytorch_model.safetensors"

        from safetensors.torch import save_file
        save_file(merged_dit_state, str(merged_dit_path))

        dit_size = merged_dit_path.stat().st_size / (1024**3)
        print(f"  ✅ DiT模型已保存: {dit_size:.2f} GB")

        # 5. 创建合并信息文件
        print(f"\n📝 创建合并信息...")

        # 合并配置
        merge_info = {
            "model_type": "wan_video_merged",
            "base_model": base_model_id,
            "lora_path": str(lora_path),
            "lora_filename": Path(lora_path).name,
            "alpha": alpha,
            "merged_date": "2025-07-19",
            "merged_by": "DiffSynth-Studio LoRA Merge Script",
            "description": f"Complete merged model from {base_model_id} with LoRA weights (alpha={alpha})",
            "files_info": {
                "diffusion_pytorch_model.safetensors": "Merged DiT model with LoRA weights applied",
                "models_t5_umt5-xxl-enc-bf16.pth": "T5 text encoder (unchanged)",
                "Wan2.1_VAE.pth": "Video VAE (unchanged)",
                "other_files": "All original model files preserved"
            }
        }

        merge_info_path = output_path / "MERGE_INFO.json"
        with open(merge_info_path, 'w', encoding='utf-8') as f:
            json.dump(merge_info, f, indent=2, ensure_ascii=False)

        # 6. 验证完整性
        print(f"\n🔍 验证合并模型完整性...")

        # 检查关键文件
        required_files = [
            "diffusion_pytorch_model.safetensors",
            "models_t5_umt5-xxl-enc-bf16.pth",
            "Wan2.1_VAE.pth",
            "config.json",
            "README.md"
        ]

        missing_files = []
        for filename in required_files:
            if not (output_path / filename).exists():
                missing_files.append(filename)

        if missing_files:
            print(f"  ⚠️  缺少关键文件: {missing_files}")
        else:
            print(f"  ✅ 所有关键文件都存在")

        # 计算总大小
        total_size = sum(f.stat().st_size for f in output_path.rglob('*') if f.is_file())
        total_size_gb = total_size / (1024**3)

        print(f"\n📊 合并模型统计:")
        print(f"  - 总文件数: {len(list(output_path.rglob('*')))}")
        print(f"  - 总大小: {total_size_gb:.2f} GB")
        print(f"  - 输出路径: {output_path}")

        print(f"\n✅ 完整合并模型创建成功！")
        return True

    except Exception as e:
        print(f"❌ 创建合并模型失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== Wan2.1-T2V-1.3B 完整模型合并工具 ===")

    # 配置参数
    base_model_id = "Wan-AI/Wan2.1-T2V-1.3B"
    lora_path = "./models/train/Wan2.1-T2V-1.3B_lora/epoch-3.safetensors"

    # 检查LoRA文件
    if not os.path.exists(lora_path):
        print(f"❌ LoRA权重文件不存在: {lora_path}")
        return

    print(f"✅ 找到LoRA权重文件: {lora_path}")

    # 创建不同alpha值的完整合并模型
    alpha_values = [0.8, 1.0, 1.2]

    for alpha in alpha_values:
        print(f"\n{'='*60}")
        print(f"创建 Alpha = {alpha} 的完整合并模型")
        print(f"{'='*60}")

        output_path = f"./models/merged_complete/Wan2.1-T2V-1.3B-epoch3-alpha{alpha}-complete"

        success = create_complete_merged_model(
            base_model_id=base_model_id,
            lora_path=lora_path,
            output_path=output_path,
            alpha=alpha
        )

        if success:
            print(f"✅ Alpha={alpha} 的完整合并模型创建成功")
        else:
            print(f"❌ Alpha={alpha} 的完整合并模型创建失败")
            break

    print(f"\n{'='*60}")
    print("🎉 完整模型合并完成！")
    print(f"{'='*60}")

    # 显示创建的模型
    merged_dir = Path("./models/merged_complete")
    if merged_dir.exists():
        print(f"\n📁 创建的完整合并模型:")
        for model_dir in sorted(merged_dir.iterdir()):
            if model_dir.is_dir():
                size_gb = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file()) / (1024**3)
                print(f"  - {model_dir.name} ({size_gb:.1f} GB)")

if __name__ == "__main__":
    main()
```

### 8.2 运行权重合并

```bash
python create_complete_merged_model.py
```

### 8.3 验证合并结果

```bash
# 检查合并模型文件夹
ls -la models/merged_complete/

# 查看具体模型内容
ls -la models/merged_complete/Wan2.1-T2V-1.3B-epoch3-alpha1.0-complete/

# 查看合并信息
cat models/merged_complete/Wan2.1-T2V-1.3B-epoch3-alpha1.0-complete/MERGE_INFO.json
```

## 9. 合并模型推理测试代码

### 9.1 合并模型推理脚本

创建 `test_merged_model.py`：

```python
#!/usr/bin/env python3
"""
测试完整合并模型的推理功能
"""

import torch
import os
import json
from pathlib import Path
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def test_merged_model(model_path, test_prompt, output_name):
    """测试完整合并模型"""

    model_path = Path(model_path)

    print(f"\n=== 测试完整合并模型 ===")
    print(f"模型路径: {model_path}")

    # 检查模型文件夹
    if not model_path.exists():
        print(f"❌ 模型文件夹不存在: {model_path}")
        return False

    # 读取合并信息
    merge_info_path = model_path / "MERGE_INFO.json"
    if merge_info_path.exists():
        with open(merge_info_path, 'r', encoding='utf-8') as f:
            merge_info = json.load(f)

        print(f"📋 合并模型信息:")
        print(f"  - 基础模型: {merge_info['base_model']}")
        print(f"  - LoRA文件: {merge_info['lora_filename']}")
        print(f"  - Alpha值: {merge_info['alpha']}")
        print(f"  - 合并日期: {merge_info['merged_date']}")

    # 检查关键文件
    required_files = [
        "diffusion_pytorch_model.safetensors",
        "models_t5_umt5-xxl-enc-bf16.pth",
        "Wan2.1_VAE.pth",
        "config.json"
    ]

    print(f"🔍 检查模型文件:")
    missing_files = []
    total_size = 0

    for filename in required_files:
        file_path = model_path / filename
        if file_path.exists():
            size_gb = file_path.stat().st_size / (1024**3)
            total_size += size_gb
            print(f"  ✅ {filename}: {size_gb:.2f} GB")
        else:
            missing_files.append(filename)
            print(f"  ❌ {filename}: 缺失")

    if missing_files:
        print(f"❌ 缺少关键文件: {missing_files}")
        return False

    print(f"  总大小: {total_size:.2f} GB")

    try:
        # 加载完整合并模型
        print(f"\n📥 加载完整合并模型...")

        dit_path = model_path / "diffusion_pytorch_model.safetensors"
        t5_path = model_path / "models_t5_umt5-xxl-enc-bf16.pth"
        vae_path = model_path / "Wan2.1_VAE.pth"

        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(path=str(dit_path), offload_device="cpu"),
                ModelConfig(path=str(t5_path), offload_device="cpu"),
                ModelConfig(path=str(vae_path), offload_device="cpu"),
            ],
        )

        pipe.enable_vram_management()
        print(f"✅ 完整合并模型加载成功")

        # 生成测试视频
        print(f"\n🎬 生成测试视频...")
        print(f"提示词: {test_prompt}")

        negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"

        video = pipe(
            prompt=test_prompt,
            negative_prompt=negative_prompt,
            seed=42,
            tiled=True,
        )

        # 保存视频
        output_path = f"merged_{output_name}.mp4"
        save_video(video, output_path, fps=15, quality=5)

        # 检查结果
        if os.path.exists(output_path):
            size_mb = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 视频生成成功: {output_path} ({size_mb:.2f} MB)")
            return True
        else:
            print(f"❌ 视频生成失败")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_all_merged_models():
    """对比所有完整合并模型的效果"""

    print("=== 对比所有完整合并模型 ===")

    # 查找所有完整合并模型
    merged_dir = Path("./models/merged_complete")
    if not merged_dir.exists():
        print(f"❌ 完整合并模型目录不存在: {merged_dir}")
        return

    model_paths = []
    for model_dir in sorted(merged_dir.iterdir()):
        if model_dir.is_dir() and "complete" in model_dir.name:
            model_paths.append(model_dir)

    if not model_paths:
        print(f"❌ 未找到完整合并模型")
        return

    print(f"找到 {len(model_paths)} 个完整合并模型:")
    for model_path in model_paths:
        print(f"  - {model_path.name}")

    # 测试提示词
    test_prompt = "纪实摄影风格画面，一只活泼的小狗在绿茵茵的草地上迅速奔跑，阳光洒在毛发上"

    success_count = 0

    for i, model_path in enumerate(model_paths):
        print(f"\n{'='*60}")
        print(f"测试模型 {i+1}/{len(model_paths)}: {model_path.name}")
        print(f"{'='*60}")

        # 从文件名提取alpha值
        if "alpha0.8" in model_path.name:
            output_name = "alpha0.8"
        elif "alpha1.0" in model_path.name:
            output_name = "alpha1.0"
        elif "alpha1.2" in model_path.name:
            output_name = "alpha1.2"
        else:
            output_name = f"model{i+1}"

        success = test_merged_model(
            model_path=model_path,
            test_prompt=test_prompt,
            output_name=output_name
        )

        if success:
            success_count += 1
            print(f"✅ 模型 {model_path.name} 测试成功")
        else:
            print(f"❌ 模型 {model_path.name} 测试失败")

    print(f"\n{'='*60}")
    print(f"🎉 测试完成！成功: {success_count}/{len(model_paths)}")
    print(f"{'='*60}")

    # 显示生成的视频
    video_files = [f for f in os.listdir('.') if f.startswith('merged_') and f.endswith('.mp4')]
    if video_files:
        print(f"\n🎬 生成的对比视频:")
        for video_file in sorted(video_files):
            size_mb = os.path.getsize(video_file) / (1024*1024)
            print(f"  - {video_file} ({size_mb:.2f} MB)")

def main():
    print("=== 完整合并模型测试工具 ===")

    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return

    print(f"✅ CUDA可用，设备: {torch.cuda.get_device_name()}")

    # 运行对比测试
    compare_all_merged_models()

if __name__ == "__main__":
    main()
```

### 9.2 运行合并模型测试

```bash
python test_merged_model.py
```
