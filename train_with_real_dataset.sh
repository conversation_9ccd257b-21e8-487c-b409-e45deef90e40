#!/bin/bash
# 使用真实动画数据集训练Wan2.1-T2V-1.3B LoRA的完整脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="./logs"
TENSORBOARD_DIR="./tensorboard_logs/real_dataset_training_${TIMESTAMP}"

# 创建目录
mkdir -p "$LOG_DIR"
mkdir -p "$TENSORBOARD_DIR"

log_info "🎬 开始使用真实动画数据集训练Wan2.1-T2V-1.3B LoRA"
log_info "📅 时间戳: $TIMESTAMP"
log_info "📁 日志目录: $LOG_DIR"
log_info "📊 TensorBoard: $TENSORBOARD_DIR"

# 步骤1: 环境检查
log_info "📋 步骤1/8: 环境检查"
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

python -c "
import torch
print(f'✅ PyTorch版本: {torch.__version__}')
print(f'✅ CUDA可用: {torch.cuda.is_available()}')
print(f'✅ GPU数量: {torch.cuda.device_count()}')
for i in range(torch.cuda.device_count()):
    print(f'✅ GPU {i}: {torch.cuda.get_device_name(i)}')
" | tee "$LOG_DIR/01_environment_check_${TIMESTAMP}.log"

log_success "环境检查完成"

# 步骤2: 创建真实数据集
log_info "📋 步骤2/8: 创建真实动画数据集"

# 检查是否已存在增强版数据集
if [ -d "data/enhanced_t2v_dataset" ] && [ -f "data/enhanced_t2v_dataset/metadata.csv" ]; then
    log_info "发现现有增强版数据集，跳过创建"
    DATASET_PATH="data/enhanced_t2v_dataset"
    METADATA_PATH="data/enhanced_t2v_dataset/metadata.csv"
else
    log_info "创建新的增强版数据集..."
    python create_enhanced_t2v_dataset.py \
        --resolution 576x320 \
        --fps 8 \
        --duration 2.5 \
        --variants 5 \
        --output_dir data/enhanced_t2v_dataset 2>&1 | tee "$LOG_DIR/02_dataset_creation_${TIMESTAMP}.log"
    
    DATASET_PATH="data/enhanced_t2v_dataset"
    METADATA_PATH="data/enhanced_t2v_dataset/metadata.csv"
fi

# 验证数据集
if [ ! -f "$METADATA_PATH" ]; then
    log_error "数据集创建失败，metadata.csv不存在"
    exit 1
fi

DATASET_SIZE=$(wc -l < "$METADATA_PATH")
DATASET_SIZE=$((DATASET_SIZE - 1))  # 减去标题行
log_success "数据集验证完成，包含 $DATASET_SIZE 个视频样本"

# 步骤3: 配置训练参数
log_info "📋 步骤3/8: 配置训练参数"

# 根据数据集大小调整训练参数
if [ $DATASET_SIZE -lt 20 ]; then
    DATASET_REPEAT=500
    NUM_EPOCHS=8
elif [ $DATASET_SIZE -lt 50 ]; then
    DATASET_REPEAT=200
    NUM_EPOCHS=6
else
    DATASET_REPEAT=100
    NUM_EPOCHS=5
fi

EFFECTIVE_SAMPLES=$((DATASET_SIZE * DATASET_REPEAT))

log_info "训练配置:"
log_info "  原始样本数: $DATASET_SIZE"
log_info "  数据重复次数: $DATASET_REPEAT"
log_info "  有效样本数: $EFFECTIVE_SAMPLES"
log_info "  训练轮数: $NUM_EPOCHS"

# 创建Accelerate配置
if [ ! -f "accelerate_config.yaml" ]; then
    cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF
    log_info "创建accelerate_config.yaml"
fi

# 步骤4: 启动TensorBoard监控
log_info "📋 步骤4/8: 启动TensorBoard监控"
nohup tensorboard --logdir "$TENSORBOARD_DIR" --port 6011 --host 0.0.0.0 > "$LOG_DIR/tensorboard_${TIMESTAMP}.log" 2>&1 &
TENSORBOARD_PID=$!
log_success "TensorBoard已启动 (PID: $TENSORBOARD_PID, 端口: 6011)"
log_info "🌐 访问地址: http://localhost:6011"

# 步骤5: 创建训练监控脚本
log_info "📋 步骤5/8: 启动训练监控"
cat > "real_dataset_monitor_${TIMESTAMP}.py" << EOF
import torch
from torch.utils.tensorboard import SummaryWriter
import json
from datetime import datetime

# 创建TensorBoard写入器
writer = SummaryWriter(log_dir="$TENSORBOARD_DIR")

# 记录数据集信息
with open("$DATASET_PATH/dataset_stats.json", 'r') as f:
    dataset_stats = json.load(f)

dataset_info = f"""
# 真实动画数据集训练信息

## 数据集统计
- 总样本数: {dataset_stats['total_samples']}
- 独特场景: {dataset_stats['unique_scenes']}
- 每场景变体: {dataset_stats['variants_per_scene']}
- 视频分辨率: {dataset_stats['video_specs']['resolution']}
- 帧率: {dataset_stats['video_specs']['fps']}fps
- 视频时长: {dataset_stats['video_specs']['duration_seconds']}秒

## 场景分类
{chr(10).join([f"- {cat}: {count}个样本" for cat, count in dataset_stats['categories'].items()])}

## 训练配置
- 数据重复: $DATASET_REPEAT 次
- 有效样本: $EFFECTIVE_SAMPLES 个
- 训练轮数: $NUM_EPOCHS epochs
- 预计训练时间: 约{$EFFECTIVE_SAMPLES // 100}分钟

## 开始时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

writer.add_text("Dataset/Real_Animation_Info", dataset_info, 0)
writer.close()
print(f"✅ 数据集信息已记录到TensorBoard")
EOF

python "real_dataset_monitor_${TIMESTAMP}.py"
log_success "训练监控已启动"

# 步骤6: 执行LoRA训练
log_info "📋 步骤6/8: 启动LoRA训练"
log_info "🚀 使用真实动画数据集进行训练..."
log_info "📝 训练日志: $LOG_DIR/06_training_output_${TIMESTAMP}.log"

# 完整的训练命令
TRAIN_CMD="accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path $DATASET_PATH \
  --dataset_metadata_path $METADATA_PATH \
  --height 320 \
  --width 576 \
  --dataset_repeat $DATASET_REPEAT \
  --model_id_with_origin_paths 'Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth' \
  --learning_rate 1e-4 \
  --num_epochs $NUM_EPOCHS \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt 'pipe.dit.' \
  --output_path './models/train/Wan2.1-T2V-1.3B_real_dataset_lora' \
  --lora_base_model 'dit' \
  --lora_target_modules 'q,k,v,o,ffn.0,ffn.2' \
  --lora_rank 16 \
  --use_gradient_checkpointing_offload"

echo "训练命令: $TRAIN_CMD" >> "$LOG_DIR/06_training_start_${TIMESTAMP}.log"

# 执行训练
eval $TRAIN_CMD 2>&1 | tee "$LOG_DIR/06_training_output_${TIMESTAMP}.log"
TRAIN_EXIT_CODE=${PIPESTATUS[0]}

# 步骤7: 训练结果验证
log_info "📋 步骤7/8: 训练结果验证"

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    log_success "训练成功完成!"
    
    OUTPUT_DIR="./models/train/Wan2.1-T2V-1.3B_real_dataset_lora"
    if [ -d "$OUTPUT_DIR" ]; then
        echo "训练输出文件:" > "$LOG_DIR/07_results_${TIMESTAMP}.log"
        find "$OUTPUT_DIR" -type f -exec ls -lh {} \; >> "$LOG_DIR/07_results_${TIMESTAMP}.log"
        log_success "训练结果已保存到: $OUTPUT_DIR"
    fi
else
    log_error "训练失败，退出码: $TRAIN_EXIT_CODE"
fi

# 步骤8: 生成最终报告
log_info "📋 步骤8/8: 生成最终报告"

cat > "$LOG_DIR/real_dataset_training_report_${TIMESTAMP}.md" << EOF
# 真实动画数据集LoRA训练报告

## 训练概览
- 开始时间: $(date)
- 训练状态: $([ $TRAIN_EXIT_CODE -eq 0 ] && echo "成功" || echo "失败")
- 退出码: $TRAIN_EXIT_CODE

## 数据集信息
- 数据集路径: $DATASET_PATH
- 原始样本数: $DATASET_SIZE
- 数据重复次数: $DATASET_REPEAT
- 有效样本数: $EFFECTIVE_SAMPLES
- 训练轮数: $NUM_EPOCHS

## 文件位置
- 日志目录: $LOG_DIR
- TensorBoard: $TENSORBOARD_DIR
- 模型输出: ./models/train/Wan2.1-T2V-1.3B_real_dataset_lora

## 访问链接
- TensorBoard: http://localhost:6011

## 后续步骤
1. 查看TensorBoard监控数据
2. 使用真实数据集测试推理效果
3. 对比不同数据集的训练结果
4. 优化提示词和训练参数

EOF

# 清理临时文件
rm -f "real_dataset_monitor_${TIMESTAMP}.py"

log_success "🎉 真实动画数据集训练流水线执行完成!"
log_info "📊 最终报告: $LOG_DIR/real_dataset_training_report_${TIMESTAMP}.md"
log_info "🌐 TensorBoard: http://localhost:6011"

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    log_info "🔄 TensorBoard将继续运行，PID: $TENSORBOARD_PID"
    log_info "💡 停止TensorBoard: kill $TENSORBOARD_PID"
    
    log_info "🧪 测试训练结果:"
    log_info "   python test_real_dataset_inference.py"
else
    log_info "🛑 由于训练失败，停止TensorBoard"
    kill $TENSORBOARD_PID 2>/dev/null || true
fi

exit $TRAIN_EXIT_CODE
