#!/usr/bin/env python3
"""
Wan2.1-T2V-1.3B LoRA训练脚本 - 集成TensorBoard可视化
基于原始训练脚本，添加详细的TensorBoard日志记录
"""

import torch
import os
import json
import time
import numpy as np
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
from pathlib import Path

# 导入原始训练模块
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from diffsynth.trainers.utils import DiffusionTrainingModule, VideoDataset, ModelLogger, wan_parser
from accelerate import Accelerator

os.environ["TOKENIZERS_PARALLELISM"] = "false"

class TensorBoardLogger:
    """TensorBoard日志记录器 - 专门用于Wan视频训练"""
    
    def __init__(self, log_dir, config=None):
        self.log_dir = log_dir
        self.config = config or {}
        self.writer = SummaryWriter(log_dir=log_dir)
        self.step = 0
        
        # 记录训练配置
        self.log_training_config()
        
        # 训练历史记录
        self.train_losses = []
        self.learning_rates = []
        self.gradient_norms = []
        
    def log_training_config(self):
        """记录训练配置信息"""
        if not self.config:
            return
            
        config_text = f"""
# Wan2.1-T2V-1.3B LoRA训练配置

## 模型配置
- Model ID: {self.config.get('model_id', 'Wan-AI/Wan2.1-T2V-1.3B')}
- LoRA Base Model: {self.config.get('lora_base_model', 'dit')}
- LoRA Target Modules: {self.config.get('lora_target_modules', 'q,k,v,o,ffn.0,ffn.2')}
- LoRA Rank: {self.config.get('lora_rank', 16)}

## 训练配置
- Learning Rate: {self.config.get('learning_rate', 1e-4)}
- Num Epochs: {self.config.get('num_epochs', 5)}
- Gradient Accumulation Steps: {self.config.get('gradient_accumulation_steps', 8)}
- Dataset Repeat: {self.config.get('dataset_repeat', 500)}

## 视频配置
- Height: {self.config.get('height', 320)}
- Width: {self.config.get('width', 576)}
- Dataset Path: {self.config.get('dataset_base_path', 'data/example_video_dataset')}

## 硬件配置
- Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}
- GPU Count: {torch.cuda.device_count()}
- CUDA Version: {torch.version.cuda if torch.cuda.is_available() else 'N/A'}
"""
        
        self.writer.add_text("Config/Training_Configuration", config_text, 0)
    
    def log_scalar(self, tag, value, step=None):
        """记录标量值"""
        if step is None:
            step = self.step
        self.writer.add_scalar(tag, value, step)
    
    def log_scalars(self, main_tag, tag_scalar_dict, step=None):
        """记录多个标量值"""
        if step is None:
            step = self.step
        self.writer.add_scalars(main_tag, tag_scalar_dict, step)
    
    def log_histogram(self, tag, values, step=None):
        """记录直方图"""
        if step is None:
            step = self.step
        if isinstance(values, torch.Tensor):
            values = values.detach().cpu()
        self.writer.add_histogram(tag, values, step)
    
    def log_figure(self, tag, figure, step=None):
        """记录matplotlib图表"""
        if step is None:
            step = self.step
        self.writer.add_figure(tag, figure, step)
    
    def log_model_parameters(self, model, step):
        """记录模型参数统计"""
        for name, param in model.named_parameters():
            if param.requires_grad and param.grad is not None:
                # 参数值分布
                self.log_histogram(f"Parameters/{name}", param.data, step)
                # 梯度分布
                self.log_histogram(f"Gradients/{name}", param.grad.data, step)
                
                # 参数统计
                self.log_scalar(f"ParamStats/{name}/mean", param.data.mean().item(), step)
                self.log_scalar(f"ParamStats/{name}/std", param.data.std().item(), step)
                self.log_scalar(f"ParamStats/{name}/norm", param.data.norm().item(), step)
                
                # 梯度统计
                if param.grad is not None:
                    self.log_scalar(f"GradStats/{name}/mean", param.grad.data.mean().item(), step)
                    self.log_scalar(f"GradStats/{name}/std", param.grad.data.std().item(), step)
                    self.log_scalar(f"GradStats/{name}/norm", param.grad.data.norm().item(), step)
    
    def log_memory_usage(self, step):
        """记录显存使用情况"""
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # GB
                total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                
                self.log_scalar(f"Memory/GPU_{i}/Allocated_GB", memory_allocated, step)
                self.log_scalar(f"Memory/GPU_{i}/Reserved_GB", memory_reserved, step)
                self.log_scalar(f"Memory/GPU_{i}/Total_GB", total_memory, step)
                self.log_scalar(f"Memory/GPU_{i}/Utilization_%", memory_allocated/total_memory*100, step)
    
    def log_training_step(self, loss, lr, grad_norm, step, epoch=None):
        """记录训练步骤信息"""
        # 基础指标
        self.log_scalar("Loss/Train", loss, step)
        self.log_scalar("Learning_Rate", lr, step)
        self.log_scalar("Gradient_Norm", grad_norm, step)
        
        if epoch is not None:
            self.log_scalar("Epoch", epoch, step)
        
        # 更新历史记录
        self.train_losses.append(loss)
        self.learning_rates.append(lr)
        self.gradient_norms.append(grad_norm)
        
        # 记录显存使用
        self.log_memory_usage(step)
        
        # 每10步创建图表
        if step % 10 == 0 and len(self.train_losses) > 1:
            self.create_training_plots(step)
    
    def create_training_plots(self, step):
        """创建训练图表"""
        # 损失曲线图
        if len(self.train_losses) > 1:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.plot(self.train_losses[-50:], 'b-', linewidth=2, label='Training Loss')
            ax.set_title('Training Loss (Last 50 steps)', fontsize=14)
            ax.set_xlabel('Step')
            ax.set_ylabel('Loss')
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # 添加趋势线
            if len(self.train_losses) > 10:
                recent_losses = self.train_losses[-50:]
                z = np.polyfit(range(len(recent_losses)), recent_losses, 1)
                p = np.poly1d(z)
                ax.plot(range(len(recent_losses)), p(range(len(recent_losses))), 
                       "r--", alpha=0.8, label='Trend')
                ax.legend()
            
            self.log_figure("Plots/Loss_Curve", fig, step)
            plt.close(fig)
        
        # 学习率图表
        if len(self.learning_rates) > 1:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.plot(self.learning_rates, 'g-', linewidth=2)
            ax.set_title('Learning Rate Schedule', fontsize=14)
            ax.set_xlabel('Step')
            ax.set_ylabel('Learning Rate')
            ax.set_yscale('log')
            ax.grid(True, alpha=0.3)
            
            self.log_figure("Plots/Learning_Rate", fig, step)
            plt.close(fig)
    
    def log_epoch_summary(self, epoch, avg_loss, step):
        """记录epoch总结"""
        self.log_scalar("Loss/Epoch_Average", avg_loss, step)
        
        # 创建epoch总结图
        if len(self.train_losses) > 0:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # 损失曲线
            axes[0, 0].plot(self.train_losses, 'b-', linewidth=2)
            axes[0, 0].set_title(f'Training Loss - Epoch {epoch}')
            axes[0, 0].set_xlabel('Step')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 学习率
            axes[0, 1].plot(self.learning_rates, 'g-', linewidth=2)
            axes[0, 1].set_title('Learning Rate')
            axes[0, 1].set_xlabel('Step')
            axes[0, 1].set_ylabel('Learning Rate')
            axes[0, 1].set_yscale('log')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 梯度范数
            axes[1, 0].plot(self.gradient_norms, 'r-', linewidth=2)
            axes[1, 0].set_title('Gradient Norms')
            axes[1, 0].set_xlabel('Step')
            axes[1, 0].set_ylabel('Gradient Norm')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 损失分布
            axes[1, 1].hist(self.train_losses, bins=30, alpha=0.7, color='blue')
            axes[1, 1].set_title('Loss Distribution')
            axes[1, 1].set_xlabel('Loss Value')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            self.log_figure(f"Summary/Epoch_{epoch}_Summary", fig, step)
            plt.close(fig)
    
    def close(self):
        """关闭writer"""
        self.writer.close()

class WanTrainingModuleWithTensorBoard(DiffusionTrainingModule):
    """集成TensorBoard的Wan训练模块"""
    
    def __init__(
        self,
        model_paths=None, model_id_with_origin_paths=None,
        trainable_models=None,
        lora_base_model=None, lora_target_modules="q,k,v,o,ffn.0,ffn.2", lora_rank=32,
        use_gradient_checkpointing=True,
        use_gradient_checkpointing_offload=False,
        extra_inputs=None,
        tensorboard_logger=None,
    ):
        super().__init__()
        
        # 保存TensorBoard logger
        self.tensorboard_logger = tensorboard_logger
        
        # Load models
        model_configs = []
        if model_paths is not None:
            model_paths = json.loads(model_paths)
            model_configs += [ModelConfig(path=path) for path in model_paths]
        if model_id_with_origin_paths is not None:
            model_id_with_origin_paths = model_id_with_origin_paths.split(",")
            model_configs += [ModelConfig(model_id=i.split(":")[0], origin_file_pattern=i.split(":")[1]) for i in model_id_with_origin_paths]
        
        print("📥 加载Wan2.1-T2V-1.3B模型...")
        self.pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device="cpu", model_configs=model_configs)
        
        # Reset training scheduler
        self.pipe.scheduler.set_timesteps(1000, training=True)
        
        # Freeze untrainable models
        self.pipe.freeze_except([] if trainable_models is None else trainable_models.split(","))
        
        # Add LoRA to the base models
        if lora_base_model is not None:
            print(f"🔧 添加LoRA到 {lora_base_model} (rank={lora_rank}, modules={lora_target_modules})")
            model = self.add_lora_to_model(
                getattr(self.pipe, lora_base_model),
                target_modules=lora_target_modules.split(","),
                lora_rank=lora_rank
            )
            setattr(self.pipe, lora_base_model, model)
            
            # 统计可训练参数
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            total_params = sum(p.numel() for p in model.parameters())
            print(f"📊 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
            
        # Store other configs
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.use_gradient_checkpointing_offload = use_gradient_checkpointing_offload
        self.extra_inputs = extra_inputs.split(",") if extra_inputs is not None else []
        
    def forward_preprocess(self, data):
        inputs_shared = {}
        inputs_posi = {}
        inputs_nega = {}
        
        # Prepare inputs
        for key in ["prompt", "negative_prompt", "height", "width", "num_frames", "fps"]:
            if key in data:
                if key == "prompt":
                    inputs_posi[key] = data[key]
                elif key == "negative_prompt":
                    inputs_nega[key] = data[key]
                else:
                    inputs_shared[key] = data[key]
        
        # Video
        if "video" in data:
            inputs_shared["video"] = data["video"]
        
        # Extra inputs
        for extra_input in self.extra_inputs:
            if extra_input in data:
                if extra_input.endswith("_posi"):
                    inputs_posi[extra_input[:-5]] = data[extra_input]
                elif extra_input.endswith("_nega"):
                    inputs_nega[extra_input[:-5]] = data[extra_input]
                else:
                    inputs_shared[extra_input] = data[extra_input]
        
        # Pipeline units will automatically process the input parameters.
        for unit in self.pipe.units:
            inputs_shared, inputs_posi, inputs_nega = self.pipe.unit_runner(unit, self.pipe, inputs_shared, inputs_posi, inputs_nega)
        return {**inputs_shared, **inputs_posi}
    
    def forward(self, data, inputs=None):
        if inputs is None:
            inputs = self.forward_preprocess(data)
        models = {name: getattr(self.pipe, name) for name in self.pipe.in_iteration_models}
        loss = self.pipe.training_loss(**models, **inputs)
        return loss

def custom_training_loop(dataset, model, model_logger, optimizer, scheduler,
                        num_epochs, gradient_accumulation_steps, tensorboard_logger):
    """自定义训练循环，集成TensorBoard日志记录"""

    print("🏋️ 开始训练循环...")

    # 初始化Accelerator
    accelerator = Accelerator(
        mixed_precision="bf16",
        gradient_accumulation_steps=gradient_accumulation_steps,
    )

    # 准备模型、优化器、数据加载器
    model, optimizer, dataset, scheduler = accelerator.prepare(
        model, optimizer, dataset, scheduler
    )

    print(f"🖥️ 设备: {accelerator.device}")
    print(f"🔢 进程数: {accelerator.num_processes}")
    print(f"📊 数据集大小: {len(dataset)}")

    global_step = 0

    for epoch in range(num_epochs):
        print(f"\n📅 Epoch {epoch + 1}/{num_epochs}")
        model.train()

        epoch_losses = []
        epoch_start_time = time.time()

        for step, batch in enumerate(dataset):
            step_start_time = time.time()

            with accelerator.accumulate(model):
                # 前向传播
                loss = model(batch)

                # 反向传播
                accelerator.backward(loss)

                # 计算梯度范数
                grad_norm = 0.0
                if accelerator.sync_gradients:
                    grad_norm = accelerator.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.step()
                scheduler.step()
                optimizer.zero_grad()

                # 记录指标
                loss_value = loss.item()
                lr_value = scheduler.get_last_lr()[0] if hasattr(scheduler, 'get_last_lr') else optimizer.param_groups[0]['lr']

                epoch_losses.append(loss_value)

                # TensorBoard日志记录
                if accelerator.is_main_process and tensorboard_logger:
                    tensorboard_logger.log_training_step(
                        loss=loss_value,
                        lr=lr_value,
                        grad_norm=grad_norm,
                        step=global_step,
                        epoch=epoch
                    )

                    # 每10步记录详细参数信息
                    if global_step % 10 == 0:
                        tensorboard_logger.log_model_parameters(model, global_step)

                # 打印进度
                if step % 10 == 0:
                    step_time = time.time() - step_start_time
                    print(f"  步骤 {step}/{len(dataset)}: "
                          f"损失={loss_value:.4f}, "
                          f"学习率={lr_value:.2e}, "
                          f"梯度范数={grad_norm:.4f}, "
                          f"时间={step_time:.2f}s")

                global_step += 1

        # Epoch结束处理
        epoch_time = time.time() - epoch_start_time
        avg_loss = sum(epoch_losses) / len(epoch_losses)

        print(f"📊 Epoch {epoch + 1} 完成:")
        print(f"  - 平均损失: {avg_loss:.4f}")
        print(f"  - 训练时间: {epoch_time:.1f}秒")
        print(f"  - 步骤数: {len(epoch_losses)}")

        # TensorBoard epoch总结
        if accelerator.is_main_process and tensorboard_logger:
            tensorboard_logger.log_epoch_summary(epoch + 1, avg_loss, global_step)

        # 保存检查点
        if accelerator.is_main_process:
            model_logger.save_checkpoint(
                accelerator.unwrap_model(model),
                epoch=epoch + 1,
                step=global_step
            )
            print(f"💾 保存检查点: epoch-{epoch + 1}")

def main():
    """主函数"""

    print("🚀 开始Wan2.1-T2V-1.3B LoRA训练 (集成TensorBoard)")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 解析命令行参数
    parser = wan_parser()
    args = parser.parse_args()

    # 创建TensorBoard日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tensorboard_dir = f"./tensorboard_logs/wan_lora_training_{timestamp}"
    os.makedirs(tensorboard_dir, exist_ok=True)

    # 准备配置信息
    config = {
        'model_id': 'Wan-AI/Wan2.1-T2V-1.3B',
        'lora_base_model': args.lora_base_model,
        'lora_target_modules': args.lora_target_modules,
        'lora_rank': args.lora_rank,
        'learning_rate': args.learning_rate,
        'num_epochs': args.num_epochs,
        'gradient_accumulation_steps': args.gradient_accumulation_steps,
        'dataset_repeat': args.dataset_repeat,
        'height': args.height,
        'width': args.width,
        'dataset_base_path': args.dataset_base_path,
    }

    # 初始化TensorBoard记录器
    tensorboard_logger = TensorBoardLogger(tensorboard_dir, config)
    print(f"📊 TensorBoard日志目录: {tensorboard_dir}")
    print(f"💡 启动TensorBoard: tensorboard --logdir {tensorboard_dir}")

    # 创建数据集
    print("📂 准备数据集...")
    dataset = VideoDataset(args=args)

    # 创建模型
    print("🤖 初始化模型...")
    model = WanTrainingModuleWithTensorBoard(
        model_paths=args.model_paths,
        model_id_with_origin_paths=args.model_id_with_origin_paths,
        trainable_models=args.trainable_models,
        lora_base_model=args.lora_base_model,
        lora_target_modules=args.lora_target_modules,
        lora_rank=args.lora_rank,
        use_gradient_checkpointing_offload=args.use_gradient_checkpointing_offload,
        extra_inputs=args.extra_inputs,
        tensorboard_logger=tensorboard_logger,
    )

    # 创建模型记录器
    model_logger = ModelLogger(
        args.output_path,
        remove_prefix_in_ckpt=args.remove_prefix_in_ckpt
    )

    # 创建优化器和调度器
    print("⚙️ 配置优化器...")
    optimizer = torch.optim.AdamW(model.trainable_modules(), lr=args.learning_rate)
    scheduler = torch.optim.lr_scheduler.ConstantLR(optimizer)

    # 启动自定义训练循环
    try:
        custom_training_loop(
            dataset=dataset,
            model=model,
            model_logger=model_logger,
            optimizer=optimizer,
            scheduler=scheduler,
            num_epochs=args.num_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            tensorboard_logger=tensorboard_logger,
        )

        print("✅ 训练完成!")

    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        raise

    finally:
        # 关闭TensorBoard记录器
        tensorboard_logger.close()
        print(f"📊 TensorBoard日志已保存到: {tensorboard_dir}")
        print(f"💡 查看结果: tensorboard --logdir {tensorboard_dir}")

    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
