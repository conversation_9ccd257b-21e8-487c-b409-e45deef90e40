#!/bin/bash

# Wan2.1-T2V-1.3B LoRA微调训练脚本 (极限显存优化版)
# 时间: 2025-07-22
# GPU: 1x RTX 3090 (单卡训练避免通信开销)
# 优化: 最小分辨率、最小LoRA配置、单卡训练

echo "🚀 开始Wan2.1-T2V-1.3B LoRA微调训练 (极限优化版)..."
echo "时间: $(date)"
echo "GPU配置: 1x NVIDIA GeForce RTX 3090 (单卡训练)"

# 设置环境变量 (极限优化)
export CUDA_VISIBLE_DEVICES=0  # 只使用第一张GPU
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:64
export CUDA_LAUNCH_BLOCKING=1
export NCCL_DEBUG=WARN

# 训练参数配置 (极限显存优化)
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-T2V-1.3B_lora_minimal"
LOG_DIR="./logs/training"

# 创建输出目录
mkdir -p ${OUTPUT_PATH}
mkdir -p ${LOG_DIR}

echo "📁 输出路径: ${OUTPUT_PATH}"
echo "📁 日志路径: ${LOG_DIR}"

# 强制清理GPU显存
echo "🧹 强制清理GPU显存..."
python -c "
import torch
import gc
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    gc.collect()
    torch.cuda.empty_cache()
    print('✅ GPU显存已强制清理')
    
    # 显示清理后的显存状态
    for i in range(torch.cuda.device_count()):
        memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
        memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
        print(f'GPU {i}: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB')
else:
    print('❌ CUDA不可用')
"

# 验证数据集
echo "🔍 验证数据集..."
if [ ! -f "${DATASET_METADATA_PATH}" ]; then
    echo "❌ 数据集元数据文件不存在: ${DATASET_METADATA_PATH}"
    exit 1
fi

echo "✅ 数据集验证通过"

# 验证模型文件
echo "🔍 验证模型文件..."
MODEL_FILES=(
    "models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors"
    "models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth"
    "models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth"
)

for file in "${MODEL_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 模型文件不存在: $file"
        exit 1
    fi
done

echo "✅ 模型文件验证通过"

# 创建单卡accelerate配置
echo "⚙️  创建单卡accelerate配置..."
cat > accelerate_config_single.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: NO
downcast_bf16: 'no'
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 1
use_cpu: false
EOF

echo "✅ 单卡配置创建完成"

# 记录训练开始时间
TRAIN_START_TIME=$(date +%s)
echo "⏰ 训练开始时间: $(date)"

# 启动单卡LoRA训练 (极限优化配置)
echo "🚀 启动单卡LoRA训练 (极限优化)..."
echo "📊 极限优化配置:"
echo "  - 数据集: ${DATASET_BASE_PATH}"
echo "  - 输出分辨率: 256x448 (极小分辨率)"
echo "  - 数据重复: 10次 (最少重复)"
echo "  - 学习率: 1e-4"
echo "  - 训练轮数: 1 (快速验证)"
echo "  - LoRA rank: 8 (最小rank)"
echo "  - 目标模块: q,k (最少模块)"
echo "  - 单卡训练: 避免通信开销"
echo "  - 显存优化: 最大化"

# 执行训练命令 (极限优化版)
accelerate launch --config_file accelerate_config_single.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 256 \
  --width 448 \
  --dataset_repeat 10 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
  --learning_rate 1e-4 \
  --num_epochs 1 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k" \
  --lora_rank 8 \
  2>&1 | tee ${LOG_DIR}/training_minimal_$(date +%Y%m%d_%H%M%S).log

# 记录训练结束时间
TRAIN_END_TIME=$(date +%s)
TRAIN_DURATION=$((TRAIN_END_TIME - TRAIN_START_TIME))
TRAIN_HOURS=$((TRAIN_DURATION / 3600))
TRAIN_MINUTES=$(((TRAIN_DURATION % 3600) / 60))
TRAIN_SECONDS=$((TRAIN_DURATION % 60))

echo "⏰ 训练结束时间: $(date)"
echo "⏱️  训练总耗时: ${TRAIN_HOURS}小时${TRAIN_MINUTES}分钟${TRAIN_SECONDS}秒"

# 检查训练结果
echo "🔍 检查训练结果..."
if [ -d "${OUTPUT_PATH}" ]; then
    echo "✅ 输出目录存在: ${OUTPUT_PATH}"
    
    # 列出生成的checkpoint文件
    echo "📁 生成的checkpoint文件:"
    if ls ${OUTPUT_PATH}/*.safetensors 1> /dev/null 2>&1; then
        ls -la ${OUTPUT_PATH}/*.safetensors
        echo "✅ 找到checkpoint文件"
        
        # 显示checkpoint详细信息
        for checkpoint in ${OUTPUT_PATH}/*.safetensors; do
            if [ -f "$checkpoint" ]; then
                size=$(du -sh "$checkpoint" | cut -f1)
                echo "  📄 $(basename $checkpoint): $size"
            fi
        done
        
        TRAINING_SUCCESS=true
    else
        echo "⚠️  未找到.safetensors文件"
        TRAINING_SUCCESS=false
    fi
    
    # 计算输出目录大小
    OUTPUT_SIZE=$(du -sh ${OUTPUT_PATH} | cut -f1)
    echo "📊 输出目录大小: ${OUTPUT_SIZE}"
    
else
    echo "❌ 输出目录不存在，训练失败"
    TRAINING_SUCCESS=false
fi

# 显示最终GPU显存使用情况
echo "📊 训练后GPU显存使用情况:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits

# 训练结果总结
echo ""
echo "=" | tr '\n' '=' | head -c 60; echo ""
if [ "$TRAINING_SUCCESS" = true ]; then
    echo "🎉 Wan2.1-T2V-1.3B LoRA极限优化训练成功完成！"
    echo "✅ 模型保存在: ${OUTPUT_PATH}"
    echo "✅ 训练配置: 256x448, rank=8, 单卡"
    echo "✅ 可以进行下一步推理测试"
else
    echo "❌ Wan2.1-T2V-1.3B LoRA训练失败"
    echo "❌ 可能需要进一步优化配置"
    echo "❌ 建议检查日志文件: ${LOG_DIR}/training_minimal_*.log"
fi
echo "=" | tr '\n' '=' | head -c 60; echo ""
