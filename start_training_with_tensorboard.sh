#!/bin/bash
"""
一键启动带TensorBoard可视化的LoRA训练
自动启动训练和TensorBoard服务
"""

set -e  # 遇到错误立即退出

echo "🚀 启动带TensorBoard可视化的LoRA训练..."
echo "⏰ 开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 检查环境
echo "🔍 检查环境..."
if ! command -v conda &> /dev/null; then
    echo "❌ Conda未安装"
    exit 1
fi

if ! nvidia-smi &> /dev/null; then
    echo "❌ NVIDIA驱动未安装或GPU不可用"
    exit 1
fi

# 激活环境
echo "🔧 激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate wan_video_env

# 检查Python环境
python -c "import torch; print(f'✅ PyTorch: {torch.__version__}'); print(f'✅ CUDA可用: {torch.cuda.is_available()}'); print(f'✅ GPU数量: {torch.cuda.device_count()}')"

# 检查TensorBoard
if ! python -c "import tensorboard" &> /dev/null; then
    echo "📦 安装TensorBoard..."
    pip install tensorboard
fi

# 设置环境变量
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=1

# 创建必要目录
echo "📁 创建目录..."
mkdir -p ./tensorboard_logs
mkdir -p ./models/train
mkdir -p ./logs
mkdir -p ./data

# 检查是否已有TensorBoard在运行
echo "🔍 检查TensorBoard状态..."
python tensorboard_manager.py status

# 启动训练（后台运行）
echo "🏋️ 启动训练..."
echo "📊 训练日志将保存到 ./logs/training_with_tensorboard.log"

# 创建训练启动脚本
cat > ./start_training.py << 'EOF'
#!/usr/bin/env python3
import subprocess
import sys
import os
import time
from datetime import datetime

def main():
    print("🚀 启动训练进程...")
    
    # 启动训练
    cmd = [sys.executable, "train_lora_with_tensorboard.py"]
    
    log_file = f"./logs/training_with_tensorboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    with open(log_file, 'w') as f:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"📝 训练日志: {log_file}")
        print("🔄 训练进行中...")
        
        # 实时输出日志
        for line in process.stdout:
            print(line.rstrip())
            f.write(line)
            f.flush()
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ 训练完成!")
        else:
            print(f"❌ 训练失败，退出码: {process.returncode}")
        
        return process.returncode

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
EOF

# 启动训练（后台）
python start_training.py &
TRAINING_PID=$!

echo "🔄 训练已在后台启动 (PID: $TRAINING_PID)"

# 等待一下让训练开始生成日志
echo "⏳ 等待训练初始化..."
sleep 10

# 查找最新的TensorBoard日志目录
echo "🔍 查找TensorBoard日志目录..."
LATEST_LOG_DIR=""
if [ -d "./tensorboard_logs" ]; then
    LATEST_LOG_DIR=$(find ./tensorboard_logs -name "lora_training_*" -type d | sort | tail -1)
fi

if [ -z "$LATEST_LOG_DIR" ]; then
    echo "⚠️ 未找到TensorBoard日志目录，使用默认目录"
    LATEST_LOG_DIR="./tensorboard_logs"
fi

echo "📊 TensorBoard日志目录: $LATEST_LOG_DIR"

# 启动TensorBoard
echo "🌐 启动TensorBoard..."
python tensorboard_manager.py start --logdir "$LATEST_LOG_DIR" --port 6006

# 等待TensorBoard启动
sleep 3

# 显示访问信息
echo ""
echo "🎉 启动完成!"
echo "=" * 50
echo "📊 TensorBoard访问地址: http://localhost:6006"
echo "🏋️ 训练进程PID: $TRAINING_PID"
echo "📁 日志目录: $LATEST_LOG_DIR"
echo "📝 训练日志: ./logs/training_with_tensorboard_*.log"
echo ""
echo "💡 使用说明:"
echo "  - 在浏览器中打开 http://localhost:6006 查看训练进度"
echo "  - 使用 'python tensorboard_manager.py status' 检查TensorBoard状态"
echo "  - 使用 'python tensorboard_manager.py stop' 停止TensorBoard"
echo "  - 使用 'kill $TRAINING_PID' 停止训练进程"
echo ""

# 监控训练进程
echo "🔍 监控训练进程..."
while kill -0 $TRAINING_PID 2>/dev/null; do
    echo "⏰ $(date '+%H:%M:%S') - 训练进行中..."
    sleep 30
done

echo "✅ 训练进程已结束"

# 检查训练结果
if wait $TRAINING_PID; then
    echo "🎉 训练成功完成!"
    
    # 显示结果统计
    echo ""
    echo "📊 训练结果:"
    if [ -d "./models/train" ]; then
        echo "  - 训练模型: $(find ./models/train -name "*.safetensors" | wc -l) 个文件"
    fi
    
    if [ -d "$LATEST_LOG_DIR" ]; then
        echo "  - TensorBoard日志: $(find $LATEST_LOG_DIR -name "events.out.tfevents.*" | wc -l) 个文件"
    fi
    
    echo ""
    echo "💡 下一步:"
    echo "  - 在TensorBoard中查看训练曲线和指标"
    echo "  - 运行 'python merge_lora_weights.py' 合并权重"
    echo "  - 运行 'python test_merged_models.py' 测试模型"
    
else
    echo "❌ 训练失败"
    echo "📝 请查看日志文件了解详细错误信息"
fi

echo ""
echo "⚠️ TensorBoard仍在运行中"
echo "💡 使用 'python tensorboard_manager.py stop' 停止TensorBoard服务"
