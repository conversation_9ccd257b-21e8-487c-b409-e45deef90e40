# 终端运行日志 - 权重合并阶段
# 时间: 2025-07-22 16:46:53 - 16:47:31
# 阶段: LoRA权重合并到基础模型
# 结果: ✅ 成功生成6个合并模型

## 1. 权重合并开始
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python merge_lora_weights.py
🚀 开始LoRA权重合并...
⏰ 开始时间: 2025-07-22 16:46:53
✅ CUDA可用，GPU数量: 2
=== Wan2.1-T2V-1.3B LoRA权重合并 ===

## 2. 优化版LoRA合并 (rank=16, 3轮训练)
============================================================
🧪 合并LoRA: optimized
📄 LoRA路径: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors

### 2.1 Alpha=0.5合并
🔄 合并 Alpha=0.5...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors
⚖️  Alpha值: 0.5
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 480 个权重
📊 找到 240 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000144, 0.000151]
  ✅ 合并层: blocks.0.cross_attn.o
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000237, 0.000239]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000200, 0.000175]
  ✅ 合并层: blocks.0.cross_attn.v
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000286, 0.000275]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000154, 0.000192]
✅ 权重合并完成，共合并 240 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=0.5 合并成功

### 2.2 Alpha=1.0合并
🔄 合并 Alpha=1.0...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors
⚖️  Alpha值: 1.0
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 480 个权重
📊 找到 240 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000288, 0.000301]
  ✅ 合并层: blocks.0.cross_attn.o
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000473, 0.000479]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000401, 0.000351]
  ✅ 合并层: blocks.0.cross_attn.v
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000572, 0.000549]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000307, 0.000383]
✅ 权重合并完成，共合并 240 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=1.0 合并成功

### 2.3 Alpha=1.5合并
🔄 合并 Alpha=1.5...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors
⚖️  Alpha值: 1.5
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 480 个权重
📊 找到 240 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000431, 0.000452]
  ✅ 合并层: blocks.0.cross_attn.o
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000710, 0.000717]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000603, 0.000526]
  ✅ 合并层: blocks.0.cross_attn.v
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000858, 0.000824]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([16, 1536]), LoRA_B: torch.Size([1536, 16])
     增量: torch.Size([1536, 1536]), 范围: [-0.000462, 0.000576]
✅ 权重合并完成，共合并 240 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=1.5 合并成功

## 3. 极限版LoRA合并 (rank=8, 1轮训练)
============================================================
🧪 合并LoRA: minimal
📄 LoRA路径: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors

### 3.1 Alpha=0.5合并
🔄 合并 Alpha=0.5...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors
⚖️  Alpha值: 0.5
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 240 个权重
📊 找到 120 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000040, 0.000040]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000041, 0.000041]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000036, 0.000036]
  ✅ 合并层: blocks.0.self_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000033, 0.000033]
  ✅ 合并层: blocks.1.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000036, 0.000036]
✅ 权重合并完成，共合并 120 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=0.5 合并成功

### 3.2 Alpha=1.0合并
🔄 合并 Alpha=1.0...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors
⚖️  Alpha值: 1.0
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 240 个权重
📊 找到 120 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000079, 0.000080]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000082, 0.000082]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000073, 0.000072]
  ✅ 合并层: blocks.0.self_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000067, 0.000067]
  ✅ 合并层: blocks.1.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000072, 0.000072]
✅ 权重合并完成，共合并 120 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=1.0 合并成功

### 3.3 Alpha=1.5合并
🔄 合并 Alpha=1.5...
🔄 开始合并LoRA权重...
📥 基础模型: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
📥 LoRA权重: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
📤 输出路径: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors
⚖️  Alpha值: 1.5
📥 加载基础模型...
✅ 基础模型加载完成，包含 825 个权重
📥 加载LoRA权重...
✅ LoRA权重加载完成，包含 240 个权重
📊 找到 120 个LoRA层对
🔄 开始权重合并...
  ✅ 合并层: blocks.0.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000119, 0.000120]
  ✅ 合并层: blocks.0.cross_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000123, 0.000124]
  ✅ 合并层: blocks.0.self_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000110, 0.000108]
  ✅ 合并层: blocks.0.self_attn.q
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000100, 0.000100]
  ✅ 合并层: blocks.1.cross_attn.k
     LoRA_A: torch.Size([8, 1536]), LoRA_B: torch.Size([1536, 8])
     增量: torch.Size([1536, 1536]), 范围: [-0.000108, 0.000108]
✅ 权重合并完成，共合并 120 个层
💾 保存合并后的模型...
✅ 模型保存成功: ./models/merged/Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors
📊 文件大小: 5.29 GB
✅ Alpha=1.5 合并成功

## 4. 权重合并总结
============================================================
📋 权重合并报告
📊 合并统计:
  - 总任务数: 6
  - 成功数量: 6
  - 失败数量: 0
  - 成功率: 100.0%

📁 生成的合并模型:
  - Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors: 5.29 GB
  - 总大小: 31.72 GB

✅ 权重合并完成！生成了 6 个合并模型
⏰ 结束时间: 2025-07-22 16:47:31
🎉 LoRA权重合并全部完成！

## 5. 合并过程分析
### 5.1 优化版LoRA (rank=16)
- 合并层数: 240个 (q,k,v,o × 30层 × 2种注意力)
- 权重增量范围: 约±0.0003到±0.0008
- 合并成功率: 100%

### 5.2 极限版LoRA (rank=8)  
- 合并层数: 120个 (q,k × 30层 × 2种注意力)
- 权重增量范围: 约±0.00003到±0.00012
- 合并成功率: 100%

### 5.3 Alpha值影响
- Alpha=0.5: 权重变化较小，保守合并
- Alpha=1.0: 标准合并，完整应用LoRA
- Alpha=1.5: 权重变化较大，激进合并

## 6. 合并结果总结
- ✅ 6个合并模型全部生成成功
- ✅ 每个模型大小: 5.29GB (与基础模型相同)
- ✅ 总耗时: 38秒 (非常高效)
- ✅ 权重合并算法: B @ A * alpha
- ✅ 文件格式: SafeTensors (安全可靠)

## 下一步: 合并模型测试与对比
