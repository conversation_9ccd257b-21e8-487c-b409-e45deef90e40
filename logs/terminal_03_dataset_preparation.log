# 终端运行日志 - 数据集准备阶段
# 时间: 2025-07-22 12:40:00 - 12:42:00
# 阶段: 下载和验证训练数据集
# 结果: ✅ 成功准备官方示例数据集

## 1. 创建数据目录
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# mkdir -p data

## 2. 下载官方示例数据集
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
2025-07-22 12:40:15,234 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 12:40:17,931 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 12:40:25,509 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 12:40:27,210 - modelscope - INFO - Target directory already exists, skipping creation.
Downloading: 100%|██████████| 85/85 [00:00<00:00, 425kB/s]
Downloading: 100%|██████████| 140/140 [00:00<00:00, 700kB/s]
Downloading: 100%|██████████| 119/119 [00:00<00:00, 595kB/s]
Downloading: 100%|██████████| 105/105 [00:00<00:00, 525kB/s]
Downloading: 100%|██████████| 155/155 [00:00<00:00, 775kB/s]
Downloading: 100%|██████████| 157/157 [00:00<00:00, 785kB/s]
Downloading: 100%|██████████| 406/406 [00:00<00:00, 2.03MB/s]
Downloading: 100%|██████████| 167/167 [00:00<00:00, 835kB/s]
Downloading: 100%|██████████| 3.84k/3.84k [00:00<00:00, 19.2kB/s]
Downloading: 100%|██████████| 4.42M/4.42M [00:15<00:00, 292kB/s]
Downloading: 100%|██████████| 5.00M/5.00M [00:18<00:00, 284kB/s]
Downloading: 100%|██████████| 1.44M/1.44M [00:05<00:00, 288kB/s]
Download completed successfully.

## 3. 验证数据集文件
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# ls -la data/example_video_dataset/
total 11264
drwxr-xr-x 2 <USER> <GROUP>     4096 Jul 22 12:41 .
drwxr-xr-x 3 <USER> <GROUP>     4096 Jul 22 12:40 ..
-rw-r--r-- 1 <USER> <GROUP>      167 Jul 22 12:41 dataset_infos.json
-rw-r--r-- 1 <USER> <GROUP>     3840 Jul 22 12:41 .gitattributes
-rw-r--r-- 1 <USER> <GROUP>       85 Jul 22 12:41 metadata.csv
-rw-r--r-- 1 <USER> <GROUP>      140 Jul 22 12:41 metadata_camera_control.csv
-rw-r--r-- 1 <USER> <GROUP>      119 Jul 22 12:41 metadata_control.csv
-rw-r--r-- 1 <USER> <GROUP>      105 Jul 22 12:41 metadata_motion_bucket_id.csv
-rw-r--r-- 1 <USER> <GROUP>      155 Jul 22 12:41 metadata_reference_control.csv
-rw-r--r-- 1 <USER> <GROUP>      157 Jul 22 12:41 metadata_vace.csv
-rw-r--r-- 1 <USER> <GROUP>      406 Jul 22 12:41 README.md
-rw-r--r-- 1 <USER> <GROUP>  1507328 Jul 22 12:41 reference_image.png
-rw-r--r-- 1 <USER> <GROUP>  4636672 Jul 22 12:41 video1.mp4
-rw-r--r-- 1 <USER> <GROUP>  5242880 Jul 22 12:41 video1_softedge.mp4

## 4. 查看metadata.csv内容
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# cat data/example_video_dataset/metadata.csv
video,prompt
video1.mp4,"from sunset to night, a small town, light, house, river"

## 5. 运行数据集验证脚本
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python validate_dataset.py
=== 数据集验证工具 ===

============================================================
=== 验证数据集: ./data/example_video_dataset ===
✅ 成功读取metadata.csv
📊 数据集包含 1 个样本
✅ 列名验证通过: ['video', 'prompt']

📋 前1个样本:
  1. 视频: video1.mp4
     文本: from sunset to night, a small town, light, house, river

🔍 验证视频文件:
  ✅ video1.mp4
     尺寸: 1920x1080, 帧数: 176, FPS: 25.0
     时长: 7.0s, 大小: 4.4MB

📊 数据集统计:
  - 总样本数: 1
  - 有效视频: 1
  - 缺失视频: 0
  - 平均时长: 7.0秒
  - 总大小: 4.4MB
  - 分辨率: [(1920, 1080)]

✅ 数据集验证通过！所有 1 个样本都有效

⚠️  自定义数据集不存在: ./data/custom_video_dataset

============================================================
📋 验证总结:
✅ 官方示例数据集验证通过

🎉 至少有一个数据集可用，可以开始训练！

## 6. 数据集准备总结
下载状态: ✅ 成功
下载时间: ~2分钟
数据集大小: 11MB
样本数量: 1个视频样本
视频规格: 1920x1080, 7秒, 25fps
文本描述: 英文场景描述
验证结果: 全部通过
