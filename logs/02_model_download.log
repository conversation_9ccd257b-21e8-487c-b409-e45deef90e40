# Wan2.1-T2V-1.3B 多卡微调 - 模型下载日志
# 时间: 2025-07-22 12:25
# 状态: ✅ 成功

## 1. 模型下载命令
```bash
mkdir -p models/Wan-AI
modelscope download --model Wan-AI/Wan2.1-T2V-1.3B --local_dir ./models/Wan-AI/Wan2.1-T2V-1.3B
```

## 2. 下载过程记录
- 开始时间: 12:04
- 完成时间: 12:25
- 总耗时: ~21分钟
- 下载源: ModelScope (https://www.modelscope.cn)

## 3. 下载文件列表
### 3.1 核心模型文件
- ✅ diffusion_pytorch_model.safetensors: 5.29 GB (DiT扩散模型)
- ✅ models_t5_umt5-xxl-enc-bf16.pth: 10.58 GB (T5文本编码器)
- ✅ Wan2.1_VAE.pth: 0.47 GB (视频VAE)
- ✅ config.json: 249 bytes (模型配置)

### 3.2 辅助文件
- configuration.json: 81 bytes
- LICENSE.txt: 11.1 KB
- README.md: 16.5 KB
- assets/: 图片资源文件夹
- examples/: 示例文件夹
- google/umt5-xxl/: T5分词器文件

## 4. 下载过程中的问题
### 4.1 第一次下载
- T5模型文件下载中断 (IncompleteRead错误)
- 已下载: diffusion_pytorch_model.safetensors, Wan2.1_VAE.pth
- 中断位置: models_t5_umt5-xxl-enc-bf16.pth

### 4.2 第二次下载
- ✅ 成功完成T5模型文件下载
- 下载速度: ~292 MB/s
- 总耗时: 52秒

## 5. 模型文件验证结果
```
=== Wan2.1-T2V-1.3B 模型文件验证 ===
模型路径: models/Wan-AI/Wan2.1-T2V-1.3B
✅ diffusion_pytorch_model.safetensors: 5.29 GB (正常)
✅ models_t5_umt5-xxl-enc-bf16.pth: 10.58 GB (正常)
✅ Wan2.1_VAE.pth: 0.47 GB (正常)
✅ config.json: 0.00 GB (正常，文件很小)
```

## 6. 存储统计
- 核心文件总大小: 16.34 GB
- 完整目录大小: 16.37 GB (17 GB on disk)
- 存储位置: ./models/Wan-AI/Wan2.1-T2V-1.3B/

## 7. 模型组件说明
### 7.1 DiT扩散模型 (5.29 GB)
- 文件: diffusion_pytorch_model.safetensors
- 作用: 核心视频生成模型
- 格式: SafeTensors

### 7.2 T5文本编码器 (10.58 GB)
- 文件: models_t5_umt5-xxl-enc-bf16.pth
- 作用: 文本提示词编码
- 精度: BF16

### 7.3 视频VAE (0.47 GB)
- 文件: Wan2.1_VAE.pth
- 作用: 视频编码解码
- 版本: Wan2.1专用VAE

## 8. 验证总结
- ✅ 所有核心文件下载完整
- ✅ 文件大小符合预期
- ✅ 模型结构完整
- ✅ 可以进行下一步数据集准备

## 下一步: 数据集准备
