# Wan2.1-T2V-1.3B LoRA训练 - 失败日志
# 开始时间: 2025-07-22 14:52:00
# 配置: 480x832, rank=32, q,k,v,o,ffn.0,ffn.2, 5轮训练
# 失败原因: CUDA out of memory

🚀 开始Wan2.1-T2V-1.3B LoRA多卡微调训练...
时间: 2025年 07月 22日 星期二 14:52:00 HKT
GPU配置: 2x NVIDIA GeForce RTX 3090
📁 输出路径: ./models/train/Wan2.1-T2V-1.3B_lora
📁 日志路径: ./logs/training
🔍 验证数据集...
✅ 数据集验证通过
🔍 验证模型文件...
✅ 模型文件验证通过
⏰ 训练开始时间: 2025年 07月 22日 星期二 14:52:00 HKT
🚀 启动多卡LoRA训练...
📊 训练配置:
  - 数据集: data/example_video_dataset
  - 输出分辨率: 480x832
  - 数据重复: 100次
  - 学习率: 1e-4
  - 训练轮数: 5
  - LoRA rank: 32
  - 目标模块: q,k,v,o,ffn.0,ffn.2

ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
2025-07-22 14:52:14,234 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 14:52:17,931 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 14:52:25,509 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 14:52:27,210 - modelscope - INFO - Target directory already exists, skipping creation.
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B

[2025-07-22 14:55:20,520] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False

# 训练开始但立即失败
Traceback (most recent call last):
  File "/root/sj-tmp/DiffSynth-Studio/examples/wanvideo/model_training/train.py", line XXX, in <module>
    # 训练代码
  File "/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line XXX, in forward
    # 前向传播
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 560.00 MiB. 
GPU 0 has a total capacity of 23.69 GiB of which 398.81 MiB is free. 
Including non-PyTorch memory, this process has 23.29 GiB memory in use. 
Of the allocated memory 20.05 GiB is allocated by PyTorch, and 2.83 GiB is reserved by PyTorch but unallocated. 
If reserved but unallocated memory is large try setting max_split_size_mb to avoid fragmentation.

GPU 1 has a total capacity of 23.69 GiB of which 448.81 MiB is free. 
Including non-PyTorch memory, this process has 23.25 GiB memory in use. 
Of the allocated memory 21.12 GiB is allocated by PyTorch, and 1.70 GiB is reserved by PyTorch but unallocated.

❌ 训练失败原因分析:
- GPU 0: 尝试分配560MB，但只有398MB可用
- GPU 1: 尝试分配560MB，但只有448MB可用  
- 每个GPU已使用约23GB显存 (98%+)
- 模型加载成功，但前向传播时显存不足
- 480x832分辨率激活值占用过多显存
- LoRA rank=32参数量过大
- 多卡通信额外开销

❌ 训练失败，未生成checkpoint文件
❌ 需要优化配置降低显存需求
