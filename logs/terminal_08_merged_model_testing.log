# 终端运行日志 - 合并模型测试阶段
# 时间: 2025-07-22 17:04:58 - 17:05:11
# 阶段: 合并模型分析、验证和对比
# 结果: ✅ 6个模型全部验证通过，生成详细对比报告

## 1. 合并模型测试开始
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python test_merged_models.py
🚀 开始合并模型测试与对比...
⏰ 开始时间: 2025-07-22 17:04:58
=== 合并模型分析 ===
📊 找到 6 个合并模型:
  - Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors: 5.29 GB
  - Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors: 5.29 GB

📥 加载基础模型进行对比...
✅ 基础模型加载完成，包含 825 个权重

## 2. 极限版模型分析 (rank=8, 1轮训练)

### 2.1 Minimal Alpha=0.5 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00004673
  - 平均差异: 0.00000071
  - 差异最大的5个权重:
    1. blocks.1.cross_attn.q.weight: max=0.00004673, mean=0.00000480
    2. blocks.25.cross_attn.k.weight: max=0.00004673, mean=0.00000600
    3. blocks.19.self_attn.k.weight: max=0.00004339, mean=0.00000562
    4. blocks.24.cross_attn.q.weight: max=0.00004315, mean=0.00000629
    5. blocks.10.self_attn.k.weight: max=0.00004292, mean=0.00000530
📋 模型信息:
  - LoRA类型: minimal
  - Alpha值: 0.5
  - 文件大小: 5.29 GB

### 2.2 Minimal Alpha=1.0 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00009346
  - 平均差异: 0.00000143
  - 差异最大的5个权重:
    1. blocks.1.cross_attn.q.weight: max=0.00009346, mean=0.00000959
    2. blocks.25.cross_attn.k.weight: max=0.00009346, mean=0.00001200
    3. blocks.19.self_attn.k.weight: max=0.00008678, mean=0.00001125
    4. blocks.24.cross_attn.q.weight: max=0.00008631, mean=0.00001259
    5. blocks.10.self_attn.k.weight: max=0.00008583, mean=0.00001060
📋 模型信息:
  - LoRA类型: minimal
  - Alpha值: 1.0
  - 文件大小: 5.29 GB

### 2.3 Minimal Alpha=1.5 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00014019
  - 平均差异: 0.00000214
  - 差异最大的5个权重:
    1. blocks.1.cross_attn.q.weight: max=0.00014019, mean=0.00001439
    2. blocks.25.cross_attn.k.weight: max=0.00014019, mean=0.00001800
    3. blocks.19.self_attn.k.weight: max=0.00012970, mean=0.00001687
    4. blocks.24.cross_attn.q.weight: max=0.00012970, mean=0.00001888
    5. blocks.10.self_attn.k.weight: max=0.00012875, mean=0.00001590
📋 模型信息:
  - LoRA类型: minimal
  - Alpha值: 1.5
  - 文件大小: 5.29 GB

## 3. 优化版模型分析 (rank=16, 3轮训练)

### 3.1 Optimized Alpha=0.5 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00048065
  - 平均差异: 0.00000612
  - 差异最大的5个权重:
    1. blocks.23.cross_attn.q.weight: max=0.00048065, mean=0.00002322
    2. blocks.26.cross_attn.o.weight: max=0.00036812, mean=0.00003534
    3. blocks.26.cross_attn.v.weight: max=0.00034714, mean=0.00002729
    4. blocks.28.cross_attn.v.weight: max=0.00034714, mean=0.00001918
    5. blocks.22.cross_attn.v.weight: max=0.00033951, mean=0.00002209
📋 模型信息:
  - LoRA类型: optimized
  - Alpha值: 0.5
  - 文件大小: 5.29 GB

### 3.2 Optimized Alpha=1.0 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00096130
  - 平均差异: 0.00001224
  - 差异最大的5个权重:
    1. blocks.23.cross_attn.q.weight: max=0.00096130, mean=0.00004643
    2. blocks.26.cross_attn.o.weight: max=0.00073624, mean=0.00007069
    3. blocks.26.cross_attn.v.weight: max=0.00069427, mean=0.00005459
    4. blocks.28.cross_attn.v.weight: max=0.00069427, mean=0.00003837
    5. blocks.22.cross_attn.v.weight: max=0.00067902, mean=0.00004418
📋 模型信息:
  - LoRA类型: optimized
  - Alpha值: 1.0
  - 文件大小: 5.29 GB

### 3.3 Optimized Alpha=1.5 分析
==================================================
🧪 分析模型: Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors
✅ 模型加载成功，包含 825 个权重
📊 权重差异分析:
  - 总权重数: 825
  - 最大差异: 0.00144196
  - 平均差异: 0.00001837
  - 差异最大的5个权重:
    1. blocks.23.cross_attn.q.weight: max=0.00144196, mean=0.00006965
    2. blocks.26.cross_attn.o.weight: max=0.00110626, mean=0.00010603
    3. blocks.26.cross_attn.v.weight: max=0.00103760, mean=0.00008188
    4. blocks.28.cross_attn.v.weight: max=0.00103760, mean=0.00005755
    5. blocks.22.cross_attn.v.weight: max=0.00102234, mean=0.00006627
📋 模型信息:
  - LoRA类型: optimized
  - Alpha值: 1.5
  - 文件大小: 5.29 GB

## 4. 模型完整性验证
==================================================
🔍 验证合并模型完整性
  ✅ Wan2.1-T2V-1.3B_optimized_alpha_0.5.safetensors: 验证通过
  ✅ Wan2.1-T2V-1.3B_optimized_alpha_1.0.safetensors: 验证通过
  ✅ Wan2.1-T2V-1.3B_optimized_alpha_1.5.safetensors: 验证通过
  ✅ Wan2.1-T2V-1.3B_minimal_alpha_0.5.safetensors: 验证通过
  ✅ Wan2.1-T2V-1.3B_minimal_alpha_1.0.safetensors: 验证通过
  ✅ Wan2.1-T2V-1.3B_minimal_alpha_1.5.safetensors: 验证通过

📊 验证统计:
  - 总模型数: 6
  - 验证通过: 6
  - 验证失败: 0
  - 成功率: 100.0%

## 5. 对比报告生成
============================================================
📋 生成模型对比报告
✅ 对比报告已生成: ./reports/merged_models_comparison.md

## 6. 测试总结
============================================================
📋 测试总结:
✅ 模型分析: 成功
✅ 完整性验证: 成功
✅ 对比报告: 已生成
📄 报告路径: ./reports/merged_models_comparison.md
⏰ 结束时间: 2025-07-22 17:05:11
🎉 合并模型测试与对比全部完成！

## 7. 权重差异分析总结

### 7.1 极限版模型 (Minimal, rank=8)
- Alpha=0.5: 最大差异 0.00004673, 平均差异 0.00000071
- Alpha=1.0: 最大差异 0.00009346, 平均差异 0.00000143  
- Alpha=1.5: 最大差异 0.00014019, 平均差异 0.00000214
- 特点: 权重变化较小，影响温和

### 7.2 优化版模型 (Optimized, rank=16)
- Alpha=0.5: 最大差异 0.00048065, 平均差异 0.00000612
- Alpha=1.0: 最大差异 0.00096130, 平均差异 0.00001224
- Alpha=1.5: 最大差异 0.00144196, 平均差异 0.00001837
- 特点: 权重变化较大，影响显著

### 7.3 Alpha值影响规律
- 权重差异与Alpha值呈线性关系
- 优化版的权重变化约为极限版的10倍
- 主要变化集中在cross_attn和self_attn层

## 8. 测试结果总结
- ✅ 6个合并模型全部生成成功
- ✅ 所有模型完整性验证通过
- ✅ 权重差异分析完成
- ✅ 详细对比报告已生成
- ✅ 测试耗时: 13秒 (高效完成)

## 下一步建议
1. 使用optimized_alpha_1.0进行实际推理测试
2. 对比不同alpha值的生成效果
3. 根据应用场景选择最适合的模型
4. 考虑部署和推理优化
