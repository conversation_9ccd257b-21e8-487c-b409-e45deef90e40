# 终端运行日志 - LoRA推理测试阶段
# 时间: 2025-07-22 16:24:20 - 16:36:42
# 阶段: LoRA权重验证和推理测试
# 结果: ✅ 权重验证成功，推理脚本创建完成

## 1. 第一次推理测试尝试（失败）
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python test_lora_inference.py
🚀 开始LoRA推理测试...
⏰ 开始时间: 2025-07-22 16:24:20
✅ CUDA可用，GPU数量: 2
=== Wan2.1-T2V-1.3B LoRA推理测试 ===
📁 输出目录: ./outputs/lora_inference_test
🎯 测试提示词数量: 3
✅ 找到优化版LoRA: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
✅ 找到极限版LoRA: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors

============================================================
🧪 测试LoRA模型: optimized
📄 LoRA路径: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
📥 加载基础模型...
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
📥 加载LoRA权重...
Loading LoRA models from file: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
    Adding LoRA to wan_video_dit (./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors).
    240 tensors are updated.
🔧 创建推理管道...
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.

🎬 生成视频 1/3
📝 提示词: from sunset to night, a small town, light, house, river
❌ optimized LoRA测试失败: WanVideoPipeline.__call__() got an unexpected keyword argument 'guidance_scale'

============================================================
🧪 测试LoRA模型: minimal
📄 LoRA路径: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
📥 加载基础模型...
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
❌ minimal LoRA测试失败: CUDA out of memory. Tried to allocate 80.00 MiB. GPU 0 has a total capacity of 23.69 GiB of which 80.81 MiB is free. Process 3196716 has 23.61 GiB memory in use. Of the allocated memory 22.87 GiB is allocated by PyTorch, and 436.58 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.

============================================================
📋 LoRA推理测试报告
📊 生成视频统计:
  - 总数量: 0

❌ 未生成任何视频，测试失败
⏰ 结束时间: 2025-07-22 16:24:39
❌ LoRA推理测试失败

## 2. 第二次简化测试（成功）
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python test_lora_simple.py
🚀 开始LoRA简化测试...
⏰ 开始时间: 2025-07-22 16:36:42
=== LoRA权重文件测试 ===

==================================================
🧪 测试LoRA: optimized
📄 文件路径: ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
📊 文件大小: 22.6MB
📥 加载LoRA权重...
📊 权重统计:
  - 权重数量: 480
    blocks.0.cross_attn.k.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.k.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.cross_attn.o.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.o.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.cross_attn.q.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.q.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.cross_attn.v.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.v.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.self_attn.k.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.self_attn.k.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.self_attn.o.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.self_attn.o.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.self_attn.q.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.self_attn.q.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    blocks.0.self_attn.v.lora_A.default.weight: torch.Size([16, 1536]) (torch.bfloat16)
    blocks.0.self_attn.v.lora_B.default.weight: torch.Size([1536, 16]) (torch.bfloat16)
    [... 更多权重信息 ...]
  - 权重类型分布: {'lora_A': 240, 'lora_B': 240}
  - 总参数数量: 11,796,480
  - 权重范围: [-0.027710, 0.027832]
  - 平均值: -0.000013
✅ optimized LoRA权重加载成功

==================================================
🧪 测试LoRA: minimal
📄 文件路径: ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
📊 文件大小: 5.7MB
📥 加载LoRA权重...
📊 权重统计:
  - 权重数量: 240
    blocks.0.cross_attn.k.lora_A.default.weight: torch.Size([8, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.k.lora_B.default.weight: torch.Size([1536, 8]) (torch.bfloat16)
    blocks.0.cross_attn.q.lora_A.default.weight: torch.Size([8, 1536]) (torch.bfloat16)
    blocks.0.cross_attn.q.lora_B.default.weight: torch.Size([1536, 8]) (torch.bfloat16)
    blocks.0.self_attn.k.lora_A.default.weight: torch.Size([8, 1536]) (torch.bfloat16)
    blocks.0.self_attn.k.lora_B.default.weight: torch.Size([1536, 8]) (torch.bfloat16)
    blocks.0.self_attn.q.lora_A.default.weight: torch.Size([8, 1536]) (torch.bfloat16)
    blocks.0.self_attn.q.lora_B.default.weight: torch.Size([1536, 8]) (torch.bfloat16)
    [... 更多权重信息 ...]
  - 权重类型分布: {'lora_A': 120, 'lora_B': 120}
  - 总参数数量: 2,949,120
  - 权重范围: [-0.026123, 0.026123]
  - 平均值: -0.000011
✅ minimal LoRA权重加载成功

==================================================
🧪 测试基础模型加载
📋 检查模型文件:
  ✅ diffusion_pytorch_model.safetensors: 5.29GB
  ✅ models_t5_umt5-xxl-enc-bf16.pth: 10.58GB
  ✅ Wan2.1_VAE.pth: 0.47GB
  📊 GPU 0: 23.7GB 可用 / 23.7GB 总计
  📊 GPU 1: 23.7GB 可用 / 23.7GB 总计
✅ 基础模型文件检查完成

==================================================
📝 创建简化推理脚本
✅ 推理脚本已创建: ./simple_inference.py

==================================================
📋 测试总结:
✅ LoRA权重文件验证完成
✅ 基础模型文件检查完成
✅ 简化推理脚本已创建

💡 下一步可以运行: python simple_inference.py
⏰ 结束时间: 2025-07-22 16:36:42

## 3. LoRA权重分析结果
### 3.1 优化版LoRA (rank=16)
- 文件大小: 22.6MB
- 权重数量: 480个
- 参数总量: 11,796,480个
- 目标模块: q,k,v,o (4个模块)
- 权重范围: [-0.027710, 0.027832]
- 训练轮数: 3轮

### 3.2 极限版LoRA (rank=8)
- 文件大小: 5.7MB
- 权重数量: 240个
- 参数总量: 2,949,120个
- 目标模块: q,k (2个模块)
- 权重范围: [-0.026123, 0.026123]
- 训练轮数: 1轮

## 4. 测试结果总结
- ✅ LoRA权重文件完整性验证通过
- ✅ 权重加载和解析成功
- ✅ 基础模型文件检查通过
- ✅ 简化推理脚本创建成功
- ❌ 直接推理测试因API参数和显存问题失败
- ✅ 权重验证和分析完全成功
