# Wan2.1-T2V-1.3B LoRA训练 - 极限版运行日志
# 开始时间: 2025-07-22 15:12:04
# 配置: 256x448, rank=8, q,k, 1轮训练, 单卡

🚀 开始Wan2.1-T2V-1.3B LoRA微调训练 (极限优化版)...
时间: 2025年 07月 22日 星期二 15:12:02 HKT
GPU配置: 1x NVIDIA GeForce RTX 3090 (单卡训练)
📁 输出路径: ./models/train/Wan2.1-T2V-1.3B_lora_minimal
📁 日志路径: ./logs/training
🧹 强制清理GPU显存...
✅ GPU显存已强制清理
GPU 0: 已分配 0.00GB, 已保留 0.00GB
🔍 验证数据集...
✅ 数据集验证通过
🔍 验证模型文件...
✅ 模型文件验证通过
⚙️  创建单卡accelerate配置...
✅ 单卡配置创建完成
⏰ 训练开始时间: 2025年 07月 22日 星期二 15:12:04 HKT
🚀 启动单卡LoRA训练 (极限优化)...
📊 极限优化配置:
  - 数据集: data/example_video_dataset
  - 输出分辨率: 256x448 (极小分辨率)
  - 数据重复: 10次 (最少重复)
  - 学习率: 1e-4
  - 训练轮数: 1 (快速验证)
  - LoRA rank: 8 (最小rank)
  - 目标模块: q,k (最少模块)
  - 单卡训练: 避免通信开销
  - 显存优化: 最大化

ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
2025-07-22 15:12:14,234 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:17,931 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:25,509 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:12:27,210 - modelscope - INFO - Target directory already exists, skipping creation.
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B

# 单轮训练 (Epoch 0)
100%|██████████| 10/10 [01:33<00:00,  9.32s/it]

[2025-07-22 15:14:03,941] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-22 15:14:05,466] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False

⏰ 训练结束时间: 2025年 07月 22日 星期二 15:14:11 HKT
⏱️  训练总耗时: 0小时2分钟7秒
🔍 检查训练结果...
✅ 输出目录存在: ./models/train/Wan2.1-T2V-1.3B_lora_minimal
📁 生成的checkpoint文件:
-rw-r--r-- 1 <USER> <GROUP> 5925352  7月 22 15:14 ./models/train/Wan2.1-T2V-1.3B_lora_minimal/epoch-0.safetensors
✅ 找到checkpoint文件
  📄 epoch-0.safetensors: 5.7M
📊 输出目录大小: 5.7M
📊 训练后GPU显存使用情况:
0, NVIDIA GeForce RTX 3090, 1, 24576, 0
1, NVIDIA GeForce RTX 3090, 1, 24576, 0

============================================================
🎉 Wan2.1-T2V-1.3B LoRA极限优化训练成功完成！
✅ 模型保存在: ./models/train/Wan2.1-T2V-1.3B_lora_minimal
✅ 训练配置: 256x448, rank=8, 单卡
✅ 可以进行下一步推理测试
============================================================

# 训练成功总结
✅ 训练状态: 成功完成
✅ 训练轮数: 1轮
✅ 每轮步数: 10步
✅ 每步耗时: ~9.3秒
✅ 生成文件: 1个checkpoint (5.7MB)
✅ 总大小: 5.7MB
✅ 显存状态: 训练后已清理
✅ GPU利用率: 0% (训练完成后)
✅ 单卡训练: 避免了多卡通信开销
✅ 极限优化: 最小显存占用成功
