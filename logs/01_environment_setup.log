# Wan2.1-T2V-1.3B 多卡微调 - 环境搭建日志
# 时间: 2025-07-22 12:02
# 状态: ✅ 成功

## 1. 系统环境检查
- 操作系统: Linux
- Python版本: 3.13.2 (原始环境)
- CUDA版本: 12.2
- GPU信息: 2x NVIDIA GeForce RTX 3090 (24GB each)

## 2. Conda环境创建
```bash
conda create -n wan_video_env python=3.12 -y
conda activate wan_video_env
```
- ✅ 成功创建Python 3.12.11环境
- ✅ 环境激活成功

## 3. 代理设置
```bash
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
```
- ✅ 学术代理已启用

## 4. DiffSynth-Studio安装
```bash
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio
pip install -e .
```
- ✅ 仓库克隆成功 (13.90 MiB)
- ✅ 核心库安装成功

## 5. 训练依赖安装
```bash
pip install deepspeed peft
```
- ✅ DeepSpeed 0.17.2 安装成功
- ✅ PEFT 0.16.0 安装成功

## 6. 关键依赖版本
- PyTorch: 2.7.1+cu126
- Transformers: 4.53.2
- Accelerate: 1.9.0
- SafeTensors: 0.5.3
- ModelScope: 1.28.0
- CuPy-CUDA12x: 13.5.1
- OpenCV-Python: *********

## 7. 环境验证结果
```python
import torch
import diffsynth
import accelerate
import deepspeed
import peft
```
- ✅ 所有依赖导入成功
- ✅ CUDA可用: True
- ✅ GPU数量: 2
- ✅ PyTorch版本: 2.7.1+cu126

## 8. GPU状态
```
GPU 0: NVIDIA GeForce RTX 3090 (24576 MiB)
GPU 1: NVIDIA GeForce RTX 3090 (24576 MiB)
总显存: 48 GB
```

## 9. 环境搭建总结
- ✅ Python 3.12环境创建成功
- ✅ DiffSynth-Studio安装成功
- ✅ 多卡训练依赖安装成功
- ✅ CUDA和GPU环境验证通过
- ✅ 所有核心库导入正常

## 下一步: 模型下载与验证
