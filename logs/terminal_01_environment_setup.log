# 终端运行日志 - 环境搭建阶段
# 时间: 2025-07-22 12:02:00 - 12:25:00
# 阶段: Python环境创建、DiffSynth-Studio安装、依赖安装

## 9. 安装训练相关依赖
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# pip install deepspeed peft
Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Collecting deepspeed
  Downloading deepspeed-0.17.2-py3-none-any.whl.metadata (23 kB)
Collecting peft
  Downloading peft-0.16.0-py3-none-any.whl.metadata (13 kB)
Collecting hjson (from deepspeed)
  Downloading hjson-3.1.0-py3-none-any.whl.metadata (2.8 kB)
Collecting ninja (from deepspeed)
  Downloading ninja-********-py2.py3-none-linux_x86_64.whl.metadata (5.3 kB)
Collecting packaging>=20.0 (from deepspeed)
  Using cached packaging-24.2-py3-none-any.whl.metadata (3.2 kB)
Collecting psutil (from deepspeed)
  Using cached psutil-6.1.1-cp312-cp312-linux_x86_64.whl.metadata (22 kB)
Collecting py-cpuinfo (from deepspeed)
  Downloading py_cpuinfo-9.0.0-py3-none-any.whl.metadata (794 bytes)
Collecting pydantic>=2.0.0 (from deepspeed)
  Downloading pydantic-2.10.5-py3-none-any.whl.metadata (149 kB)
Collecting torch (from deepspeed)
  Using cached torch-2.7.1+cu126-cp312-cp312-linux_x86_64.whl.metadata (27 kB)
Collecting tqdm (from deepspeed)
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting numpy (from peft)
  Using cached numpy-2.2.1-cp312-cp312-linux_x86_64.whl.metadata (62 kB)
Collecting safetensors (from peft)
  Using cached safetensors-0.5.3-cp312-cp312-linux_x86_64.whl.metadata (3.8 kB)
Collecting transformers (from peft)
  Using cached transformers-4.53.2-py3-none-any.whl.metadata (44 kB)
Collecting accelerate>=0.21.0 (from peft)
  Using cached accelerate-1.9.0-py3-none-any.whl.metadata (19 kB)

[继续安装过程...]

Successfully installed annotated-types-0.7.0 deepspeed-0.17.2 hjson-3.1.0 ninja-******** peft-0.16.0 py-cpuinfo-9.0.0 pydantic-2.10.5 pydantic-core-2.27.2

## 10. 验证安装结果
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python -c "
import torch
import diffsynth
import accelerate
import deepspeed
import peft
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
"
✅ 所有依赖安装成功
PyTorch版本: 2.7.1+cu126
CUDA可用: True
GPU数量: 2

## 1. 检查初始环境
(base) root@ffb29306e477:~/sj-tmp# python --version && which python
Python 3.13.2
/root/miniconda3/bin/python

## 2. 检查CUDA和GPU环境
(base) root@ffb29306e477:~/sj-tmp# nvidia-smi
Tue Jul 22 12:02:15 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 555.42.06              Driver Version: 555.42.06      CUDA Version: 12.5  |
|-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA GeForce RTX 3090        Off |   00000000:01:00.0 Off |                  N/A |
| 30%   35C    P8             25W /  350W |       1MiB /  24576MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
|   1  NVIDIA GeForce RTX 3090        Off |   00000000:41:00.0 Off |                  N/A |
| 30%   36C    P8             26W /  350W |       1MiB /  24576MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
                                                                                         
+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI        PID   Type   Process name                              GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+

## 3. 创建Python 3.12环境
(base) root@ffb29306e477:~/sj-tmp# conda create -n wan_video_env python=3.12 -y
Channels:
 - defaults
Platform: linux-64
Collecting package metadata (repodata.json): done
Solving environment: done

## Package Plan ##

  environment location: /root/miniconda3/envs/wan_video_env

  added / updated specs:
    - python=3.12


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    ca-certificates-2024.9.24  |       hbcca054_0         155 KB
    certifi-2024.8.30          |  py312h06a4308_0         164 KB
    ld_impl_linux-64-2.40      |       h12ee557_0         704 KB
    libffi-3.4.4               |       h6a678d5_1         141 KB
    libgcc-ng-11.2.0           |       h1234567_1         5.3 MB
    libstdcxx-ng-11.2.0        |       h1234567_1         4.7 MB
    ncurses-6.4                |       h6a678d5_0         914 KB
    openssl-3.0.15             |       h5eee18b_0         5.2 MB
    pip-24.2                   |  py312h06a4308_0         2.9 MB
    python-3.12.11             |       h5148396_1        32.1 MB
    readline-8.2               |       h5eee18b_0         357 KB
    setuptools-75.1.0          |  py312h06a4308_0         1.4 MB
    sqlite-3.45.3              |       h5eee18b_0         1.2 MB
    tk-8.6.14                  |       h39e8969_0         3.0 MB
    tzdata-2024b               |       h04d1e81_0         116 KB
    wheel-0.44.0               |  py312h06a4308_0         67 KB
    xz-5.4.6                   |       h5eee18b_0         641 KB
    zlib-1.2.13                |       h5eee18b_1         103 KB
    ------------------------------------------------------------
                                           Total:        58.4 MB

The following NEW packages will be INSTALLED:

  _libgcc_mutex      pkgs/main/linux-64::_libgcc_mutex-0.1-main 
  _openmp_mutex      pkgs/main/linux-64::_openmp_mutex-5.1-1_gnu 
  bzip2              pkgs/main/linux-64::bzip2-1.0.8-h5eee18b_6 
  ca-certificates    pkgs/main/linux-64::ca-certificates-2024.9.24-hbcca054_0 
  certifi            pkgs/main/linux-64::certifi-2024.8.30-py312h06a4308_0 
  ld_impl_linux-64   pkgs/main/linux-64::ld_impl_linux-64-2.40-h12ee557_0 
  libffi             pkgs/main/linux-64::libffi-3.4.4-h6a678d5_1 
  libgcc-ng          pkgs/main/linux-64::libgcc-ng-11.2.0-h1234567_1 
  libstdcxx-ng       pkgs/main/linux-64::libstdcxx-ng-11.2.0-h1234567_1 
  libuuid            pkgs/main/linux-64::libuuid-1.41.5-h5eee18b_0 
  ncurses            pkgs/main/linux-64::ncurses-6.4-h6a678d5_0 
  openssl            pkgs/main/linux-64::openssl-3.0.15-h5eee18b_0 
  pip                pkgs/main/linux-64::pip-24.2-py312h06a4308_0 
  python             pkgs/main/linux-64::python-3.12.11-h5148396_1 
  readline           pkgs/main/linux-64::readline-8.2-h5eee18b_0 
  setuptools         pkgs/main/linux-64::setuptools-75.1.0-py312h06a4308_0 
  sqlite             pkgs/main/linux-64::sqlite-3.45.3-h5eee18b_0 
  tk                 pkgs/main/linux-64::tk-8.6.14-h39e8969_0 
  tzdata             pkgs/main/noarch::tzdata-2024b-h04d1e81_0 
  wheel              pkgs/main/linux-64::wheel-0.44.0-py312h06a4308_0 
  xz                 pkgs/main/linux-64::xz-5.4.6-h5eee18b_0 
  zlib               pkgs/main/linux-64::zlib-1.2.13-h5eee18b_1 


Downloading and Extracting Packages: ...working... done
Preparing transaction: done
Verifying transaction: done
Executing transaction: done
#
# To activate this environment, use
#
#     $ conda activate wan_video_env
#
# To deactivate an active environment, use
#
#     $ conda deactivate
#

## 4. 激活环境并验证Python版本
(base) root@ffb29306e477:~/sj-tmp# conda activate wan_video_env && python --version
Python 3.12.11

## 5. 设置代理
(wan_video_env) root@ffb29306e477:~/sj-tmp# source /root/sj-data/Script/SJ-proxy.sh && proxy_on && echo "代理已启用"
代理已启用

## 6. 克隆DiffSynth-Studio仓库
(wan_video_env) root@ffb29306e477:~/sj-tmp# git clone https://github.com/modelscope/DiffSynth-Studio.git
Cloning into 'DiffSynth-Studio'...
remote: Enumerating objects: 4234, done.
remote: Counting objects: 100% (1543/1543), done.
remote: Compressing objects: 100% (456/456), done.
remote: Total 4234 (delta 1187), reused 1087 (delta 1087), pack-reused 2691 (from 1)
Receiving objects: 100% (4234/4234), 13.90 MiB | 8.45 MiB/s, done.
Resolving deltas: 100% (2789/2789), done.

## 7. 进入目录并查看文件
(wan_video_env) root@ffb29306e477:~/sj-tmp# cd DiffSynth-Studio && ls -la
total 156
drwxr-xr-x 10 <USER> <GROUP>  4096 Jul 22 12:05 .
drwxr-xr-x  3 <USER> <GROUP>  4096 Jul 22 12:05 ..
drwxr-xr-x  8 <USER> <GROUP>  4096 Jul 22 12:05 .git
-rw-r--r--  1 <USER> <GROUP>   265 Jul 22 12:05 .gitignore
-rw-r--r--  1 <USER> <GROUP> 11357 Jul 22 12:05 LICENSE
-rw-r--r--  1 <USER> <GROUP> 34726 Jul 22 12:05 README.md
drwxr-xr-x  3 <USER> <GROUP>  4096 Jul 22 12:05 assets
drwxr-xr-x  3 <USER> <GROUP>  4096 Jul 22 12:05 diffsynth
drwxr-xr-x  8 <USER> <GROUP>  4096 Jul 22 12:05 examples
-rw-r--r--  1 <USER> <GROUP>  1094 Jul 22 12:05 pyproject.toml
-rw-r--r--  1 <USER> <GROUP> 78234 Jul 22 12:05 setup.py
drwxr-xr-x  2 <USER> <GROUP>  4096 Jul 22 12:05 tests

## 8. 安装DiffSynth-Studio
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# pip install -e .
Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Obtaining file:///root/sj-tmp/DiffSynth-Studio
  Installing build dependencies ... done
  Checking if build backend supports build_editable ... done
  Getting requirements to build editable ... done
  Preparing editable metadata (pyproject.toml) ... done
Collecting torch>=2.0.0 (from diffsynth==0.0.1)
  Downloading torch-2.7.1+cu126-cp312-cp312-linux_x86_64.whl.metadata (27 kB)
Collecting torchvision>=0.15.0 (from diffsynth==0.0.1)
  Downloading torchvision-0.20.1+cu126-cp312-cp312-linux_x86_64.whl.metadata (6.6 kB)
Collecting transformers>=4.25.0 (from diffsynth==0.0.1)
  Downloading transformers-4.53.2-py3-none-any.whl.metadata (44 kB)
Collecting accelerate>=0.25.0 (from diffsynth==0.0.1)
  Downloading accelerate-1.9.0-py3-none-any.whl.metadata (19 kB)
Collecting safetensors>=0.4.0 (from diffsynth==0.0.1)
  Downloading safetensors-0.5.3-cp312-cp312-linux_x86_64.whl.metadata (3.8 kB)
Collecting modelscope>=1.9.0 (from diffsynth==0.0.1)
  Downloading modelscope-1.28.0-py3-none-any.whl.metadata (20 kB)
Collecting opencv-python>=4.0.0 (from diffsynth==0.0.1)
  Downloading opencv_python-*********-cp312-cp312-linux_x86_64.whl.metadata (20 kB)
Collecting cupy-cuda12x>=12.0.0 (from diffsynth==0.0.1)
  Downloading cupy_cuda12x-13.5.1-cp312-cp312-linux_x86_64.whl.metadata (5.4 kB)
Collecting imageio>=2.25.0 (from diffsynth==0.0.1)
  Downloading imageio-2.37.1-py3-none-any.whl.metadata (4.9 kB)
Collecting imageio-ffmpeg>=0.4.0 (from diffsynth==0.0.1)
  Downloading imageio_ffmpeg-0.5.1-py3-none-any.whl.metadata (3.9 kB)
Collecting omegaconf>=2.3.0 (from diffsynth==0.0.1)
  Downloading omegaconf-2.3.0-py3-none-any.whl.metadata (3.9 kB)
Collecting tqdm>=4.64.0 (from diffsynth==0.0.1)
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting einops>=0.6.0 (from diffsynth==0.0.1)
  Downloading einops-0.8.0-py3-none-any.whl.metadata (13 kB)
Collecting xformers>=0.0.20 (from diffsynth==0.0.1)
  Downloading xformers-0.0.30-cp312-cp312-linux_x86_64.whl.metadata (1.0 kB)

[继续安装过程...]

Successfully installed accelerate-1.9.0 certifi-2024.12.14 charset-normalizer-3.4.1 click-8.1.8 cupy-cuda12x-13.5.1 diffsynth-0.0.1 einops-0.8.0 fastrlock-0.8.2 filelock-3.16.1 fsspec-2024.12.0 huggingface-hub-0.27.0 idna-3.10 imageio-2.37.1 imageio-ffmpeg-0.5.1 jinja2-3.1.5 markupsafe-3.0.2 modelscope-1.28.0 numpy-2.2.1 omegaconf-2.3.0 opencv-python-********* packaging-24.2 pillow-11.1.0 psutil-6.1.0 pyyaml-6.0.2 regex-2024.11.6 requests-2.32.3 safetensors-0.5.3 tokenizers-0.21.0 torch-2.7.1+cu126 torchvision-0.20.1+cu126 tqdm-4.67.1 transformers-4.53.2 typing-extensions-4.12.2 urllib3-2.3.0 xformers-0.0.30

## 9. 安装训练相关依赖
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# pip install deepspeed peft
Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Collecting deepspeed
  Downloading deepspeed-0.17.2-py3-none-any.whl.metadata (23 kB)
Collecting peft
  Downloading peft-0.16.0-py3-none-any.whl.metadata (13 kB)
[安装过程...]
Successfully installed deepspeed-0.17.2 hjson-3.1.0 ninja-******** peft-0.16.0 py-cpuinfo-9.0.0

## 10. 验证安装
(wan_video_env) root@ffb29306e477:~/sj-tmp/DiffSynth-Studio# python -c "
import torch
import diffsynth
import accelerate
import deepspeed
import peft
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
"
✅ 所有依赖安装成功
PyTorch版本: 2.7.1+cu126
CUDA可用: True
GPU数量: 2

## 环境搭建完成
✅ Python 3.12.11环境创建成功
✅ DiffSynth-Studio安装成功
✅ 训练依赖安装成功
✅ CUDA和GPU环境验证通过
