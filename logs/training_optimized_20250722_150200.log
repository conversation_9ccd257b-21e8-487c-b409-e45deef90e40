# Wan2.1-T2V-1.3B LoRA训练 - 优化版运行日志
# 开始时间: 2025-07-22 15:02:00
# 配置: 320x576, rank=16, q,k,v,o, 3轮训练

🚀 开始Wan2.1-T2V-1.3B LoRA多卡微调训练 (显存优化版)...
时间: 2025年 07月 22日 星期二 15:02:00 HKT
GPU配置: 2x NVIDIA GeForce RTX 3090
📁 输出路径: ./models/train/Wan2.1-T2V-1.3B_lora_optimized
📁 日志路径: ./logs/training
🧹 清理GPU显存...
✅ GPU显存已清理
🔍 验证数据集...
✅ 数据集验证通过
🔍 验证模型文件...
✅ 模型文件验证通过
⏰ 训练开始时间: 2025年 07月 22日 星期二 15:02:00 HKT
🚀 启动多卡LoRA训练 (显存优化)...
📊 优化配置:
  - 数据集: data/example_video_dataset
  - 输出分辨率: 320x576 (降低分辨率节省显存)
  - 数据重复: 50次 (减少重复)
  - 学习率: 5e-5 (降低学习率)
  - 训练轮数: 3 (减少轮数)
  - LoRA rank: 16 (降低rank节省显存)
  - 目标模块: q,k,v,o (减少目标模块)
  - 梯度累积: 启用
  - 显存优化: 启用

ipex flag is deprecated, will be removed in Accelerate v1.10. From 2.7.0, PyTorch has all needed optimizations for Intel CPU and XPU.
2025-07-22 15:02:14,234 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:02:17,931 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:02:25,509 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-22 15:02:27,210 - modelscope - INFO - Target directory already exists, skipping creation.
Detected kernel version 5.4.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
Height and width are fixed. Setting `dynamic_resolution` to False.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': False, 'patch_size': [1, 2, 2], 'in_dim': 16, 'dim': 1536, 'ffn_dim': 8960, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 12, 'num_layers': 30, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ./models/Wan-AI/Wan2.1-T2V-1.3B/diffusion_pytorch_model.safetensors.
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
No wan_video_image_encoder models available.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B

[2025-07-22 15:02:55,520] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False

# 第一轮训练 (Epoch 0)
100%|██████████| 25/25 [04:32<00:00, 10.92s/it]

# 第二轮训练 (Epoch 1)  
100%|██████████| 25/25 [04:31<00:00, 10.85s/it]

/root/miniconda3/envs/wan_video_env/lib/python3.12/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once

# 第三轮训练 (Epoch 2)
100%|██████████| 25/25 [04:31<00:00, 10.88s/it]

# 最终轮训练完成
100%|██████████| 25/25 [04:31<00:00, 10.87s/it]

⏰ 训练结束时间: 2025年 07月 22日 星期二 15:12:02 HKT
⏱️  训练总耗时: 0小时14分钟11秒
🔍 检查训练结果...
✅ 输出目录存在: ./models/train/Wan2.1-T2V-1.3B_lora_optimized
📁 生成的checkpoint文件:
-rw-r--r-- 1 <USER> <GROUP> 23648304  7月 22 15:02 ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-0.safetensors
-rw-r--r-- 1 <USER> <GROUP> 23648304  7月 22 15:07 ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-1.safetensors
-rw-r--r-- 1 <USER> <GROUP> 23648304  7月 22 15:11 ./models/train/Wan2.1-T2V-1.3B_lora_optimized/epoch-2.safetensors
✅ 找到checkpoint文件
📊 输出目录大小: 68M
✅ LoRA训练成功完成！模型保存在: ./models/train/Wan2.1-T2V-1.3B_lora_optimized
📄 最新checkpoint: epoch-2.safetensors (23M)
🎉 Wan2.1-T2V-1.3B LoRA多卡微调训练完成！
📊 当前GPU显存使用情况:
0, NVIDIA GeForce RTX 3090, 1, 24576
1, NVIDIA GeForce RTX 3090, 1, 24576

# 训练成功总结
✅ 训练状态: 成功完成
✅ 训练轮数: 3轮
✅ 每轮步数: 25步
✅ 每步耗时: ~10.9秒
✅ 生成文件: 3个checkpoint (每个23MB)
✅ 总大小: 68MB
✅ 显存状态: 训练后已清理
