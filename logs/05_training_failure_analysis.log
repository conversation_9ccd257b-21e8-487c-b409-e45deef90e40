# Wan2.1-T2V-1.3B 多卡微调 - 训练失败分析日志
# 时间: 2025-07-22 14:52
# 状态: ❌ 失败 (显存不足)

## 1. 训练失败概述
### 1.1 错误类型
- 错误: torch.OutOfMemoryError
- 原因: CUDA out of memory
- 尝试分配: 560.00 MiB
- 可用显存: GPU0 398.81 MiB, GPU1 448.81 MiB

### 1.2 显存使用情况
```
GPU 0:
- 总容量: 23.69 GiB
- 已使用: 23.29 GiB (98.3%)
- PyTorch分配: 20.05 GiB
- PyTorch保留: 2.83 GiB
- 可用: 398.81 MiB

GPU 1:
- 总容量: 23.69 GiB
- 已使用: 23.25 GiB (98.1%)
- PyTorch分配: 21.12 GiB
- PyTorch保留: 1.70 GiB
- 可用: 448.81 MiB
```

## 2. 失败原因分析
### 2.1 模型加载阶段
- ✅ 模型文件加载成功
- ✅ NCCL通信初始化成功
- ✅ 多卡分布式环境配置正确

### 2.2 训练阶段
- ❌ 前向传播时显存不足
- ❌ LoRA层计算时内存分配失败
- ❌ 梯度检查点重计算时显存不足

### 2.3 根本原因
1. **模型太大**: Wan2.1-T2V-1.3B模型本身占用大量显存
2. **分辨率过高**: 480x832分辨率激活值占用过多显存
3. **LoRA参数**: 即使rank=32的LoRA也需要额外显存
4. **梯度检查点**: 重计算时仍需要大量临时显存

## 3. 显存需求详细分析
### 3.1 模型组件显存占用
```
基础模型加载:
- DiT模型: ~8.2 GB (每GPU分片)
- T5编码器: ~10.6 GB (共享)
- VAE: ~0.5 GB (共享)
- 总计: ~19.3 GB (每GPU)
```

### 3.2 训练时额外显存
```
LoRA参数 (rank=32):
- 参数: ~0.07 GB
- 梯度: ~0.07 GB
- 优化器状态: ~0.14 GB
- 小计: ~0.28 GB

激活值 (480x832):
- 前向激活: ~4-6 GB
- 梯度检查点重计算: ~2-4 GB
- 小计: ~6-10 GB

总需求: 19.3 + 0.28 + 10 = ~29.6 GB > 24 GB
```

## 4. 解决方案分析
### 4.1 立即可行方案
1. **大幅降低分辨率**: 320x576 → 256x448
2. **减少LoRA rank**: 32 → 8
3. **减少目标模块**: 只训练q,k,v
4. **启用CPU offload**: 将部分模型移到CPU

### 4.2 高级优化方案
1. **DeepSpeed ZeRO**: 模型参数分片
2. **Gradient Checkpointing**: 更激进的检查点策略
3. **Mixed Precision**: 确保所有计算使用BF16
4. **Sequential Training**: 分层训练

## 5. 训练配置建议
### 5.1 极限显存优化配置
```bash
--height 256
--width 448
--lora_rank 8
--lora_target_modules "q,k,v"
--dataset_repeat 20
--num_epochs 2
--learning_rate 1e-4
```

### 5.2 环境变量优化
```bash
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=1
```

## 6. 硬件限制分析
### 6.1 RTX 3090限制
- 单卡显存: 24GB
- 双卡总显存: 48GB
- 显存带宽: 936 GB/s
- 计算能力: 足够

### 6.2 模型规模限制
- Wan2.1-T2V-1.3B: 1.3B参数
- 模型文件: 16.3GB
- 推荐显存: 32GB+ (单卡)
- 实际需求: 30GB+ (训练时)

### 6.3 结论
**RTX 3090双卡配置对于Wan2.1-T2V-1.3B全分辨率训练显存不足**

## 7. 替代训练策略
### 7.1 单卡训练
- 使用一张RTX 3090
- 极低分辨率: 256x448
- 最小LoRA配置
- 梯度累积: 8-16步

### 7.2 分阶段训练
- 阶段1: 低分辨率预训练
- 阶段2: 逐步提升分辨率
- 阶段3: 最终微调

### 7.3 模型裁剪
- 减少DiT层数
- 使用更小的T5编码器
- 简化VAE结构

## 8. 实际可行方案
### 8.1 推荐配置
```bash
# 极限优化配置
--height 256
--width 448
--lora_rank 8
--lora_target_modules "q,k"
--dataset_repeat 10
--num_epochs 1
--batch_size 1
--gradient_accumulation_steps 4
```

### 8.2 预期效果
- 显存使用: ~20GB (每GPU)
- 训练速度: 较慢但可行
- 模型质量: 有限但可用
- 训练时间: 1-2小时

## 9. 长期解决方案
### 9.1 硬件升级
- RTX 4090: 24GB → 更高效
- RTX A6000: 48GB → 充足显存
- H100: 80GB → 理想配置

### 9.2 软件优化
- 等待DiffSynth-Studio优化
- 使用更高效的LoRA实现
- 采用量化训练技术

## 10. 训练失败总结
- ❌ 原始配置: 480x832, rank=32 → 显存不足
- ❌ 优化配置: 320x576, rank=16 → 仍然不足
- ✅ 建议配置: 256x448, rank=8 → 理论可行
- 🔄 下一步: 实施极限优化配置

## 11. 调试信息记录
### 11.1 NCCL通信
- ✅ 初始化成功
- ✅ GPU间通信正常
- ✅ 分布式环境配置正确

### 11.2 模型加载
- ✅ DiT模型加载成功
- ✅ T5编码器加载成功
- ✅ VAE加载成功
- ✅ LoRA层初始化成功

### 11.3 失败点
- ❌ 前向传播第一步
- ❌ FFN层LoRA计算
- ❌ 560MB内存分配失败

## 下一步: 实施极限优化训练
