# Wan2.1-T2V-1.3B 多卡微调 - 调试过程日志
# 时间: 2025-07-22 14:50 - 15:15
# 问题: 显存不足导致训练失败
# 解决: 逐步优化配置直到成功

## 调试时间线

### 14:50 - 第一次尝试 (原始配置)
配置: 480x832, rank=32, q,k,v,o,ffn.0,ffn.2, 5轮, 多卡
结果: ❌ CUDA out of memory
问题: 显存需求 ~30GB > 24GB单卡容量

### 14:55 - 分析显存使用
发现问题:
- 基础模型: 16.3GB
- 激活值 (480x832): ~6-10GB  
- LoRA参数 (rank=32): ~0.3GB
- 多卡通信开销: ~1-2GB
- 总需求: ~30GB > 24GB

### 15:00 - 第二次尝试 (优化配置)
配置: 320x576, rank=16, q,k,v,o, 3轮, 多卡
结果: ✅ 成功！
优化措施:
- 分辨率降低: 480x832 → 320x576 (减少激活值)
- LoRA rank降低: 32 → 16 (减少参数)
- 目标模块减少: 6个 → 4个
- 训练轮数减少: 5 → 3

训练过程:
- 每轮25步，每步10.9秒
- 3轮训练，总计14分钟
- 生成3个checkpoint，每个23MB

### 15:12 - 第三次尝试 (极限配置)
配置: 256x448, rank=8, q,k, 1轮, 单卡
结果: ✅ 成功！
极限优化:
- 分辨率极小: 320x576 → 256x448
- LoRA rank最小: 16 → 8
- 目标模块最少: 4个 → 2个
- 单卡训练: 避免通信开销

训练过程:
- 1轮10步，每步9.3秒
- 总计2分钟快速完成
- 生成1个checkpoint，5.7MB

## 关键发现

### 显存瓶颈分析
1. **模型加载**: 基础模型16.3GB已占用大部分显存
2. **激活值**: 高分辨率视频激活值是主要瓶颈
3. **LoRA参数**: 虽然相对较小，但累积效应明显
4. **多卡通信**: 额外的同步和通信开销

### 成功优化策略
1. **分辨率优化**: 最关键，直接影响激活值大小
2. **LoRA配置**: rank和目标模块数量需要平衡
3. **训练模式**: 单卡vs多卡的权衡
4. **环境变量**: PYTORCH_CUDA_ALLOC_CONF设置有效

### 配置对比
```
原始配置 (失败):
- 分辨率: 480x832
- LoRA rank: 32  
- 目标模块: 6个
- 显存需求: ~30GB
- 结果: OOM

优化配置 (成功):
- 分辨率: 320x576
- LoRA rank: 16
- 目标模块: 4个  
- 显存需求: ~20GB
- 结果: 成功，14分钟

极限配置 (成功):
- 分辨率: 256x448
- LoRA rank: 8
- 目标模块: 2个
- 显存需求: ~15GB  
- 结果: 成功，2分钟
```

## 经验总结

### 硬件限制
- RTX 3090 24GB: 对于1.3B模型训练显存紧张
- 推荐配置: RTX A6000 48GB 或 H100 80GB
- 当前配置: 需要激进的显存优化

### 软件优化
- 分辨率是最大影响因素
- LoRA配置需要精心调优
- 单卡训练在显存受限时更稳定
- 环境变量优化不可忽视

### 训练策略
- 先用极限配置验证可行性
- 再用优化配置获得更好质量
- 分阶段训练: 低分辨率 → 高分辨率
- 渐进式优化: 逐步增加复杂度

## 最终建议

### 生产环境
- 使用优化配置 (320x576, rank=16)
- 3轮训练获得较好效果
- 23MB checkpoint大小合理

### 快速验证
- 使用极限配置 (256x448, rank=8)  
- 1轮训练快速验证
- 5.7MB checkpoint便于分发

### 硬件升级
- 考虑更大显存GPU
- 或使用模型并行/流水线并行
- DeepSpeed ZeRO等高级优化技术
